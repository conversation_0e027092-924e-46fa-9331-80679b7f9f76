import request from '@/utils/request'

export default {
  /**
   * 新增二级合伙人
   * @param {Object} data - 请求参数
   * @param {string} data.name - 合伙人姓名
   * @param {string} data.idName - 身份证号码
   * @param {string} data.contactPhone - 手机号码
   * @param {string} data.remark - 备注
   * @returns {Promise} - 返回接口响应
   */
  addSecondPartner(data) {
    return request({
      url: '/partner/secondAdd',
      method: 'POST',
      data: data
    })
  },

  /**
   * 获取二级合伙人列表
   * @param {Object} data - 请求参数
   * @param {number} data.page - 页码
   * @param {number} data.pageSize - 每页数量
   * @param {string} data.keyword - 搜索关键词
   * @returns {Promise} - 返回接口响应
   */
  getSecondPartnerList(data) {
    return request({
      url: '/partner/secondList',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取二级合伙人详情
   * @param {Object} data - 请求参数
   * @param {string|number} data.id - 合伙人ID
   * @returns {Promise} - 返回接口响应
   */
  getSecondPartnerDetail(data) {
    return request({
      url: '/partner/secondDetail',
      method: 'GET',
      data: data
    })
  },

  /**
   * 编辑二级合伙人
   * @param {Object} data - 请求参数
   * @param {string|number} data.id - 合伙人ID
   * @param {string} data.name - 合伙人姓名
   * @param {string} data.idName - 身份证号码
   * @param {string} data.contactPhone - 手机号码
   * @param {string} data.remark - 备注
   * @returns {Promise} - 返回接口响应
   */
  editSecondPartner(data) {
    return request({
      url: '/partner/secondEdit',
      method: 'POST',
      data: data
    })
  },

  /**
   * 删除二级合伙人
   * @param {Object} data - 请求参数
   * @param {string|number} data.id - 合伙人ID
   * @returns {Promise} - 返回接口响应
   */
  deleteSecondPartner(data) {
    return request({
      url: '/partner/secondDelete',
      method: 'POST',
      data: data
    })
  },

  /**
   * 获取合伙人统计数据
   * @returns {Promise} - 返回接口响应
   */
  getPartnerStats() {
    return request({
      url: '/partner/stats',
      method: 'GET'
    })
  },

  /**
   * 获取合伙人收益数据
   * @param {Object} data - 请求参数
   * @param {string} data.timeRange - 时间范围 (month/quarter/year)
   * @returns {Promise} - 返回接口响应
   */
  getPartnerEarnings(data) {
    return request({
      url: '/partner/earnings',
      method: 'GET',
      data: data
    })
  }
}
