import request from '@/utils/request'

export default {
  /**
   * 获取划拨记录列表
   * @param {Object} params - 请求参数
   * @param {string} [params.partnerName] - 合伙人名称
   * @param {string} [params.activationCode] - 激活码
   * @param {string} [params.packageType] - 套餐类型
   * @param {string} [params.transferDateRange] - 划拨日期范围
   * @param {number} [params.page] - 页码
   * @param {number} [params.size] - 每页数量
   * @returns {Promise} - 返回划拨记录列表数据
   */
  getTransferRecordList(params) {
    return request({
      url: '/partner/transfer/record/list',
      method: 'GET',
      params: params
    })
  },

  /**
   * 获取合伙人选项
   * @returns {Promise} - 返回合伙人选项数据
   */
  getPartnerOptions() {
    return request({
      url: '/partner/transfer/partner/options',
      method: 'GET'
    })
  },

  /**
   * 获取激活码选项
   * @returns {Promise} - 返回激活码选项数据
   */
  getActivationCodeOptions() {
    return request({
      url: '/partner/transfer/activation-code/options',
      method: 'GET'
    })
  },

  /**
   * 获取套餐类型选项
   * @returns {Promise} - 返回套餐类型选项数据
   */
  getPackageTypeOptions() {
    return request({
      url: '/partner/transfer/package-type/options',
      method: 'GET'
    })
  },

  /**
   * 执行划拨操作
   * @param {Object} data - 请求参数
   * @param {string} data.recordId - 记录ID
   * @returns {Promise} - 返回划拨操作结果
   */
  executeTransfer(data) {
    return request({
      url: '/partner/transfer/execute',
      method: 'POST',
      data: data
    })
  },

  /**
   * 执行回拨操作
   * @param {Object} data - 请求参数
   * @param {string} data.recordId - 记录ID
   * @returns {Promise} - 返回回拨操作结果
   */
  executeReturn(data) {
    return request({
      url: '/partner/transfer/return',
      method: 'POST',
      data: data
    })
  }
}
