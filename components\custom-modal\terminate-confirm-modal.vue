<template>
  <common-modal
    :visible="visible"
    title="请确认"
    @close="handleClose"
    @confirm="handleConfirm"
  >

    <text class="confirm-text">确认进行终止吗？</text>
  </common-modal>
</template>

<script setup>
import CommonModal from './common-modal.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'confirm'])

// 关闭弹框
const handleClose = () => {
  emit('close')
}

// 确认
const handleConfirm = () => {
  emit('confirm')
  handleClose()
}
</script>

<style lang="scss" scoped>
.confirm-text {
  width: 100%;
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  text-align: center;
  padding: 40rpx 0;
  display: block;
}
</style>
