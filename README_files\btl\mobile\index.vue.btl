<template>
    <%
    var searchCount = 0;
    var row = 0;
    %>
    <% for(var i = 0; i < configList.~size; i++) { %>
        <% if(!configList[i].needTableId && configList[i].needPage) { searchCount ++; }%>
    <% } %>
    <view>
        <z-paging ref="dataPagingRef" :fixed="true" use-virtual-list :force-close-inner-list="true" cell-height-mode="dynamic" @virtualListChange="virtualListChange" @query="loadData" :auto="true">
            <template #top>
                <% if (searchCount > 0) { %>
                <% for(var i = 0; i < configList.~size; i++) { %>
                <% if(!configList[i].needTableId && configList[i].needPage) { row ++; %>
                <% if(row <= 1) { %>
                <view class="snowy-z-paging-item">
                    <snowy-search placeholder="请输入${configList[i].fieldRemark}" v-model="searchFormData.${configList[i].fieldNameCamelCase}" @confirm="$refs.dataPagingRef.reload()" @clear="$refs.dataPagingRef.reload()" :seniorEnable="true" @seniorSearch="$refs.searchRef.open()"></snowy-search>
                </view>
                <% } %>
                <% } %>
                <% } %>
                <% } %>
            </template>
            <view class="snowy-shadow snowy-z-paging-item snowy-padding snowy-hover" v-for="(item, index) in ${classNameFirstLower}Data" :key="index" @tap="moreTapItem(item, index)">
                <% for(var i = 0; i < configList.~size; i++) { %>
                <% if(!configList[i].needTableId && configList[i].whetherTable && configList[i].fieldNameCamelCase != 'tenantId') { %>
                <uni-row class="item-uni-row">
                    <uni-col :span="2">
                        <uni-icons size="18" type="paperclip" color="#5677fc"></uni-icons>
                    </uni-col>
                    <uni-col :span="10">
                        <view class="snowy-sub-title">${configList[i].fieldRemark}</view>
                    </uni-col>
                    <uni-col :span="12">
                        <view class="snowy-text-right snowy-text-bold snowy-text-ellipsis"> {{ item.${configList[i].fieldNameCamelCase} }} </view>
                    </uni-col>
                </uni-row>
                <% } %>
                <% } %>
            </view>
        </z-paging>
		<% if (searchCount > 0) { %>
		<search ref="searchRef" :searchFormData="searchFormData" @confirm="$refs.dataPagingRef.reload()"></search>
		<% } %>
        <snowy-float-btn v-if="$snowy.hasPerm('mobile${className}Add')" @click="add"></snowy-float-btn>
        <more ref="moreRef" @handleOk="$refs.dataPagingRef.reload()"></more>
    </view>
</template>

<script setup name="${busName}">
    <% if (searchCount > 0) { %>
    import search from './search.vue'
    <% } %>
    import ${classNameFirstLower}Api from '@/api/${moduleName}/${classNameLowerKebab}-api'
    import more from './more.vue'
    import { onLoad, onShow, onReady, onPullDownRefresh, onReachBottom } from "@dcloudio/uni-app"
    import { reactive, ref, getCurrentInstance, nextTick } from "vue"

    const { proxy } = getCurrentInstance()
    const dataPagingRef = ref()
    const searchFormData = ref({})
    const ${classNameFirstLower}Data = ref([])
    const virtualListChange = (vList) => {
        ${classNameFirstLower}Data.value = vList
    }
    const loadData = async (pageNo, pageSize) => {
        const parameter = {
            current: pageNo,
            size: pageSize
        }
        Object.assign(parameter, searchFormData.value)
        const data = await ${classNameFirstLower}Api.${classNameFirstLower}Page(parameter)
        dataPagingRef.value.complete(data?.records)
    }
	onShow(() => {
        uni.$snowy.tool.refresh(() => {
            nextTick(() => {
                dataPagingRef.value.reload()
            })
        }, {
            key: "formBack"
        })
    })
    // 新增
    const add = () => {
        uni.navigateTo({
            url: '/pages/${moduleName}/${busName}/form'
        })
    }
    // 更多操作
    const moreRef = ref()
    const moreTapItem = (item, index) => {
        moreRef.value.open(item)
    }
</script>

<style lang="scss" scoped>
    @import '@/static/scss/index.scss';
    ::v-deep .uni-row {
        @extend .snowy-flex-v-center;
        padding: 5rpx;
    }
</style>
