<script setup>
import { onLaunch, onShow, onError, onLoad } from '@dcloudio/uni-app'
import { checkPermission, initializeApp } from '@/utils/auth'
import store from '@/store'
// #ifdef H5
import { getH5RouteByUrl } from '@/utils/common'
//#endif
const initApp = async () => {
  checkPermission()
  const token = uni.getStorageSync('token')
  const userInfo = uni.getStorageSync('userInfo')
  const roleApp = uni.getStorageSync('roleApp')
  const isAuthPhone = uni.getStorageSync('isAuthPhone')

  // 如果有缓存的用户信息和角色信息，先加载到store中
  if (userInfo) {
    store.commit('SET_userInfo', userInfo)
  }
  if (roleApp) {
    store.commit('SET_roleApp', roleApp)
  }

  if (token && isAuthPhone != 1) {
    uni.$snowy.tab.reLaunch('/pages/auth/index')
    return
  }
  if (token) {
    // 如果有token但没有用户信息，尝试获取用户信息
    // if (!userInfo) {
    //   try {
    //     await store.dispatch('GetUserInfo')
    //   } catch (error) {
    //     console.error('获取用户信息失败:', error)
    //   }
    // }
    initializeApp()
  }
}
onLaunch((e) => {
  console.log(e, 'onLaunch')
  if (e.path == 'pages/my-clue/task-edit') {
    return
  }
  initApp()
})

onShow((e) => {

})

// 提供给外部调用的登录成功后的方法
uni.$login = {
  success: async () => {
    await initializeApp(uni.getStorageSync('cardId') || '')
  }
}
</script>

<style lang="scss">
@import '@/static/scss/index.scss';
@import '@/static/icon/iconfont.css';

:root {
  --textColor: #337fff;
}

.su-title-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.adaptive-image {
  width: 100%;
  height: auto;
}

.uni-fab {
  bottom: 160rpx !important;
}

.uni-fab__plus {
  bottom: 160rpx !important;
}

:deep(p) {
  text-wrap: wrap !important;
}

:deep(rich-text) {
  white-space: pre-wrap;
  white-space: -moz-pre-wrap;
  white-space: -pre-wrap;
  white-space: -o-pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  /* 标准属性 */
  overflow: hidden;
}

view {
  white-space: pre-wrap;
}

text {
  white-space: pre-wrap;
}

:deep(.van-cell) {
  line-height: 20rpx !important;
}
</style>
