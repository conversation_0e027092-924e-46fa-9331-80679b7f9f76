<template>
  <snowy-layout title="消息列表" :isTabbar="false" :isFirstPage="false">
    <view class="message-list">
      <view style="text-align: right;display: flex;justify-content: flex-end;">
        <view class="action-btn reassign-btn" @click="handleMarkAllAsRead">全部已读</view>
      </view>
      <!-- 消息列表 -->
      <scroll-view class="message-scroll" scroll-y="true" :refresher-enabled="true" :refresher-triggered="refreshing"
        @refresherrefresh="onRefresh" @scrolltolower="onLoadMore" :lower-threshold="100">
        <view v-if="messageList.length > 0" class="message-items">
          <view v-for="(item, index) in messageList" :key="item.id || index" class="message-item"
            :class="{ 'unread': !item.isRead }" @click="handleMessageClick(item, index)">
            <!-- 未读标识点 -->
            <view v-if="!item.isRead" class="unread-dot"></view>

            <!-- 消息内容 -->
            <view class="message-content">
              <view class="message-text">{{ item.context || item.message || '--' }}</view>
              <view class="message-time">{{ item.createTime || '--' }}</view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-else-if="!loading" class="empty-state">
          <text class="empty-text">暂无消息</text>
        </view>

        <!-- 加载更多状态 -->
        <view v-if="loading" class="loading-state">
          <text class="loading-text">加载中...</text>
        </view>

        <view v-else-if="hasMore && messageList.length > 0" class="load-more">
          <text class="load-more-text">上拉加载更多</text>
        </view>

        <view v-else-if="!hasMore && messageList.length > 0" class="no-more">
          <text class="no-more-text">没有更多数据了</text>
        </view>
      </scroll-view>
    </view>
  </snowy-layout>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import bizInventoryApi from '@/api/biz/bizInventoryApi'

// 消息列表数据
const messageList = ref([])

// 分页参数
const currentPage = ref(1)
const pageSize = ref(20)
const hasMore = ref(true)
const total = ref(0)

// 加载状态
const loading = ref(false)
const refreshing = ref(false)

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true
  try {
    currentPage.value = 1
    await loadData(false)
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('刷新失败:', error)
  } finally {
    refreshing.value = false
  }
}

// 上拉加载更多
const onLoadMore = async () => {
  if (loading.value || !hasMore.value) return

  currentPage.value++
  await loadData(true)
}

// 获取数据
const loadData = async (isLoadMore = false) => {
  if (loading.value) return

  try {
    loading.value = true

    const params = {
      current: currentPage.value,
      size: pageSize.value
    }

    const response = await bizInventoryApi.getMessageList(params)

    const { records, total: totalCount } = response

    if (isLoadMore) {
      messageList.value = [...messageList.value, ...(records || [])]
    } else {
      messageList.value = records || []
    }

    total.value = totalCount
    hasMore.value = messageList.value.length < totalCount
  } catch (error) {
    console.error('获取数据失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 处理消息点击
const handleMessageClick = async (item, index) => {
  // 如果消息未读，标记为已读
  if (!item.isRead) {
    try {
      // 先更新UI，提供即时反馈
      messageList.value[index].isRead = true

      // 调用已读接口
      await bizInventoryApi.markMessageAsRead([{ id: item.id }])

    } catch (error) {
      // 如果接口调用失败，回滚UI状态
      messageList.value[index].isRead = false
      console.error('标记消息已读失败:', error)
      uni.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
    }
  }

  // 这里可以添加其他点击处理逻辑，比如跳转到消息详情页面
  // 或者显示消息的完整内容等
}
const handleMarkAllAsRead = async () => {
  // 如果消息未读，标记为已读
  try {
    await bizInventoryApi.markMessageAsRead([])
    uni.showToast({
      title: '全部已读成功',
      icon: 'none'
    })
    messageList.value=[]
    currentPage.value=1
    loadData()
  } catch (error) {
    // 如果接口调用失败，回滚UI状态
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
  }
}
// 通知导航栏刷新未读数量
const refreshNavBarUnreadCount = () => {
  // 通过事件总线或者其他方式通知导航栏刷新
  // 这里可以使用 uni.$emit 或者直接调用导航栏组件的方法
  try {
    // 获取当前页面栈
    const pages = uni.getCurrentPages()
    if (pages.length > 1) {
      const prevPage = pages[pages.length - 2]
      // 如果上一个页面有刷新未读数量的方法，调用它
      if (prevPage.$vm && prevPage.$vm.refreshUnreadCount) {
        prevPage.$vm.refreshUnreadCount()
      }
    }
  } catch (error) {
    console.error('刷新导航栏未读数量失败:', error)
  }
}

// 页面加载
onMounted(() => {
  loadData()
})

// 页面卸载时刷新导航栏未读数量
onUnmounted(() => {
  refreshNavBarUnreadCount()
})
</script>

<style lang="scss" scoped>
.message-list {
  height: 100%;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.message-scroll {
  flex: 1;
  height: 0;
  padding: 20rpx 0rpx;
  box-sizing: border-box;

  .message-items {
    padding: 0 30rpx;
    background-color: white;

    .message-item {
      position: relative;
      border-radius: 20rpx;
      padding: 30rpx 40rpx;
      transition: all 0.2s ease;
      border-bottom: 2rpx solid #EEEEEE;

      &:active {
        background-color: #f8f9fa;
      }

      &.unread {
        border-left: 6rpx solid #ff4757;
      }

      .unread-dot {
        position: absolute;
        top: 47rpx;
        left: 20rpx;
        width: 12rpx;
        height: 12rpx;
        background: #ff4757;
        border-radius: 50%;
      }

      .message-content {
        .message-text {
          font-size: 30rpx;
          color: #333;
          line-height: 1.6;
          margin-bottom: 10rpx;
          word-break: break-all;
        }

        .message-time {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400rpx;

    .empty-text {
      font-size: 28rpx;
      color: #999;
    }
  }

  .loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;

    .loading-text {
      font-size: 28rpx;
      color: #666;
    }
  }

  .load-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;

    .load-more-text {
      font-size: 28rpx;
      color: #999;
    }
  }

  .no-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40rpx 0;

    .no-more-text {
      font-size: 28rpx;
      color: #ccc;
    }
  }
}

.action-btn {
  width: 100rpx;
  height: 60rpx;
  line-height: 60rpx;
  padding: 0 30rpx;
  font-size: 26rpx;
  border-radius: 30rpx;
  margin: 20rpx;
}

.edit-btn {
  color: #666;
  background-color: #f5f5f5;
}

.reassign-btn {
  color: #fff;
  background-color: #ffaa00;
}
</style>
