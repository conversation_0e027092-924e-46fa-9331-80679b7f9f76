import request from '@/utils/request'

export default {
  /**
   * 获取线索列表
   * @param {Object} data - 查询参数
   * @param {number} [data.current] - 当前页码
   * @param {number} [data.size] - 每页条数
   * @returns {Promise} - 返回接口响应
   */
  clueList(data) {
    return request({
      url: '/clue/list',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取线索详情
   * @param {Object} data - 查询参数
   * @param {string|number} data.id - 线索ID
   * @returns {Promise} - 返回接口响应
   */
  clueDetail(data) {
    return request({
      url: '/clue/detail',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取访问记录
   * @param {Object} data - 查询参数
   * @param {string|number} data.clueId - 线索ID
   * @param {number} [data.current] - 页码
   * @param {number} [data.size] - 每页条数
   * @returns {Promise} - 返回接口响应
   */
  accessRecordQuery(data) {
    return request({
      url: '/accessrecord/query',
      method: 'GET',
      data: data
    })
  },
  /**
   * 获取访问记录
   * @param {Object} data - 查询参数
   * @param {string|number} data.userId - 用户ID
   * @returns {Promise} - 返回接口响应
   */
  accessRecordQueryAll(data) {
    return request({
      url: '/accessrecord/query',
      method: 'GET',
      data: data
    })
  },
  /**
   * 保存备注
   * @param {Object} data - 查询参数
   * @param {string|number} data.merchantId - 商户ID
   * @param {string|number} data.uuid - 线索ID
   * @param {string} data.remark - 备注
   * @returns {Promise} - 返回接口响应
   */
  saveRemark(data) {
    return request({
      url: '/accessrecord/remark',
      method: 'POST',
      data: data
    })
  },
  statMerAccessRecord(data) {
    return request({
      url: '/accessrecord/statMerAccessRecord',
      method: 'GET',
      data: data
    })
  },
} 