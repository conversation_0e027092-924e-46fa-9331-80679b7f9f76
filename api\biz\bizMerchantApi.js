import request from '@/utils/request'

export default {
  /**
   * 获取商户信息
   * @returns {Promise} - 返回接口响应，包含商户信息、套餐信息和有效期
   */
  getOwner() {
    return request({
      url: '/merchant/getOwner',
      method: 'GET'
    })
  },

  /**
   * 保存商户备注
   * @param {Object} data - 请求参数
   * @param {string|number} data.id - 记录ID
   * @param {string} data.remark - 备注内容
   * @returns {Promise} - 返回接口响应
   */
  saveRemark(data) {
    return request({
      url: '/merchant/saveRemark',
      method: 'POST',
      data: data
    })
  },
  
  /**
   * 使用激活码激活/升级套餐
   * @param {Object} data - 请求参数
   * @param {string} data.code - 激活码
   * @returns {Promise} - 返回激活结果
   */
  activatePackage(data) {
    return request({
      url: '/merchant/activatePackage',
      method: 'POST',
      data: data
    })
  },

  /**
   * 查询审核通过的商户列表
   * @param {Object} data - 请求参数
   * @param {string} [data.name] - 商户名称
   * @param {string} [data.type] - 商户类型：企业(COMPANY),个人(PERSON),个体户(PRIVATE)
   * @param {string} [data.status] - 商户状态：录入中(ENTERING),已提交(SUBMITTED),正常(NORMAL),已冻结(FROZEN)
   * @param {string} [data.cpackageName] - 套餐名称
   * @param {string} [data.enterTimeStart] - 入驻时间开始
   * @param {string} [data.enterTimeEnd] - 入驻时间结束
   * @param {string} [data.cpEffectEndTimeStart] - 激活码到期日-开始
   * @param {string} [data.cpEffectEndTimeEnd] - 激活码到期日-结束
   * @param {number} [data.current] - 当前页码
   * @param {number} [data.size] - 每页条数
   * @returns {Promise} - 返回商户列表数据
   */
  queryAuditPassMerchantList(data) {
    return request({
      url: '/merchant/queryAuditPassMerchantList',
      method: 'GET',
      data: data
    })
  },

  /**
   * 校验套餐激活码
   * @param {Object} data - 请求参数
   * @param {string} data.code - 激活码
   * @returns {Promise} - 返回校验结果，包含激活天数信息
   */
  checkCdkey(data) {
    return request({
      url: '/biz/cdkey/check',
      method: 'POST',
      data: data
    })
  },

  /**
   * 激活套餐
   * @param {Object} data - 请求参数
   * @param {string} data.code - 激活码
   * @returns {Promise} - 返回激活结果
   */
  activateCdkey(data) {
    return request({
      url: '/biz/cdkey/activate',
      method: 'POST',
      data: data
    })
  }
} 