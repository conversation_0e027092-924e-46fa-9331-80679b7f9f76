/* [z-paging]公用的静态css资源 */

.zp-line-loading-image {
	/* #ifndef APP-NVUE */
	animation: loading-flower 1s steps(12) infinite;
	/* #endif */
	color: #666666;
}
.zp-line-loading-image-rpx {
	margin-right: 8rpx;
	width: 34rpx;
	height: 34rpx;
}
.zp-line-loading-image-px {
	margin-right: 4px;
	width: 17px;
	height: 17px;
}

.zp-loading-image-ios-rpx {
	width: 40rpx;
	height: 40rpx;
}
.zp-loading-image-ios-px {
	width: 20px;
	height: 20px;
}

.zp-loading-image-android-rpx {
	width: 34rpx;
	height: 34rpx;
}
.zp-loading-image-android-px {
	width: 17px;
	height: 17px;
}

/* #ifndef APP-NVUE */
@keyframes loading-flower {
	0% {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
	}
	to {
		-webkit-transform: rotate(1turn);
		transform: rotate(1turn);
	}
}
/* #endif */

