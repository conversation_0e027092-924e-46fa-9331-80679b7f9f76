<template>
  <!-- 自定义导航栏容器 -->
  <view class="custom-navbar" :style="{ height: navBarHeight + 'px' }">
    <!-- 状态栏占位 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>

    <!-- 导航栏主体内容 -->
    <view class="navbar-content">
      <!-- 中间标题 -->
      <!-- <view v-if="isFirstPage" class="nav-title">
        <uni-icons type="auth" size="22"></uni-icons>
        <text class="title">个人中心</text>
      </view> -->
      <view v-if="!isFirstPage" style="position: absolute;">
        <uni-icons type="arrowleft" size="25" @click="handleBack"></uni-icons>
      </view>

      <!-- 右侧自定义内容（如“张三网”） -->
      <view class="nav-right">
        <text v-if="title" class="brand">{{ title }}</text>
        <text v-else class="brand">{{ cardInfo.name }}</text>
      </view>
      <view class="nav-right-icon" @click="handleToMessage">
        <image
          src="/static/images/icons/message.png"
          style="width: 38rpx; height: 38rpx"
          mode="widthFix"
        />
        <!-- 未读消息数量红点 -->
        <view v-if="unreadCount > 0" class="unread-badge">
          <text class="unread-text">{{ unreadCount > 99 ? '99+' : unreadCount }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import store from '@/store'
import bizInventoryApi from '@/api/biz/bizInventoryApi'

const systemInfo = uni.getSystemInfoSync();
const statusBarHeight = ref(systemInfo.statusBarHeight || 0);
const navBarHeight = ref(44);

// 未读消息数量
const unreadCount = ref(0)

const props = defineProps({
  title: {
    type: String,
    default: "",
  },
  isFirstPage: {
    type: Boolean,
    default: false,
  },
});

const cardInfo = computed(() => store.getters.cardInfo)

const handleBack = () => {
  uni.navigateBack();
};

// 跳转到消息列表页面
const handleToMessage = () => {
  uni.navigateTo({
    url: '/pages/message-list/index'
  })
}

// 获取未读消息数量
const getUnreadCount = async () => {
  try {
    const response = await bizInventoryApi.getUnreadMessageCount()
    unreadCount.value = response || 0
  } catch (error) {
    console.error('获取未读消息数量失败:', error)
  }
}

// 页面加载时获取未读消息数量
onMounted(() => {
  getUnreadCount()
})

// 暴露方法供外部调用
defineExpose({
  refreshUnreadCount: getUnreadCount
})
</script>

<style scoped lang="scss">
/* 导航栏容器 */
.custom-navbar {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  background: #ffffff; /* 按设计调整背景色 */
  // box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1); /* 可选阴影 */
}

/* 状态栏占位 */
.status-bar {
  width: 100%;
}

/* 导航栏主体内容 */
.navbar-content {
  height: 44px; /* 固定导航栏内容高度 */
  display: flex;
  align-items: center;
  padding: 0 15px;
  position: relative;
  background: white;
}

/* 左右布局 */
.nav-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 中间标题 */
.nav-title {
  position: absolute;
  top: 50%;
  left: 20px;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #333333;
  width: 174rpx;
  height: 62rpx;
  background: white;
  border-radius: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
}
/* 品牌文字 */
.brand {
  font-size: 40rpx;
  color: rgba(51, 51, 51, 1);
}
.nav-right-icon{
  position: absolute;
  right: 200rpx;
  top: 50%;
  transform: translateY(-50%);

  .unread-badge {
    position: absolute;
    top: -8rpx;
    right: -14rpx;
    // min-width: 32rpx;
    height: 25rpx;
    background: #ff4757;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 8rpx;

    .unread-text {
      font-size: 20rpx;
      color: white;
      font-weight: 600;
      line-height: 1;
    }
  }
}
</style>
