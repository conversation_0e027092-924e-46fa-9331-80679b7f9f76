<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>划拨页面预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f6fa;
        }
        
        .inventory-page {
            height: 100vh;
            background-color: #f5f6fa;
            display: flex;
            flex-direction: column;
        }
        
        .tab-bar {
            display: flex;
            background: white;
            border-bottom: 1px solid #eee;
        }
        
        .tab-item {
            flex: 1;
            text-align: center;
            padding: 15px 0;
            font-size: 14px;
            color: #666;
            position: relative;
        }
        
        .tab-item.active {
            color: #4A90E2;
            font-weight: 600;
        }
        
        .tab-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 2px;
            background: #4A90E2;
            border-radius: 1px;
        }
        
        .search-bar {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            background: white;
            border-bottom: 1px solid #eee;
        }
        
        .search-input {
            flex: 1;
            display: flex;
            align-items: center;
            background: #f5f6fa;
            border-radius: 25px;
            padding: 10px 15px;
            margin-right: 10px;
        }
        
        .search-input input {
            flex: 1;
            margin-left: 10px;
            font-size: 14px;
            color: #333;
            border: none;
            background: transparent;
            outline: none;
        }
        
        .bottom-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            padding: 15px 20px;
            border-top: 1px solid #eee;
            display: flex;
            align-items: center;
            justify-content: space-between;
            z-index: 100;
        }

        .left-section {
            display: flex;
            align-items: center;
        }

        .select-all {
            display: flex;
            align-items: center;
            margin-right: 20px;
        }

        .selected-count {
            font-size: 13px;
            color: #666;
        }
        
        .content-area {
            flex: 1;
            padding: 10px;
            overflow-y: auto;
        }
        
        .package-group {
            background: white;
            border-radius: 8px;
            margin-bottom: 10px;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
            display: flex;
            overflow: hidden;
        }
        
        .package-group > div:first-child {
            width: 140px;
            border-right: 1px solid #f0f0f0;
        }
        
        .package-header {
            display: flex;
            align-items: center;
            padding: 15px 10px;
            background: #f8f9fa;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .package-name {
            margin-left: 8px;
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }
        
        .condition-item {
            display: flex;
            align-items: center;
            padding: 12px 10px;
            border-bottom: 1px solid #f5f5f5;
        }
        
        .checkbox {
            width: 16px;
            height: 16px;
            border: 1px solid #ddd;
            border-radius: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
        }
        
        .checkbox.checked {
            background: #4A90E2;
            border-color: #4A90E2;
        }
        
        .checkbox.checked::after {
            content: '✓';
            color: white;
            font-size: 10px;
        }
        
        .condition-text {
            flex: 1;
            font-size: 13px;
            color: #333;
        }
        
        .condition-count {
            font-size: 12px;
            color: #999;
        }
        
        .list-area {
            flex: 1;
            overflow: hidden;
        }
        
        .inventory-list {
            height: 100%;
            padding: 10px;
        }
        
        .inventory-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin-bottom: 10px;
            border-bottom: 1px solid #f5f5f5;
        }
        
        .inventory-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .item-content {
            flex: 1;
        }
        
        .activation-code {
            font-size: 15px;
            color: #333;
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .validity {
            font-size: 13px;
            color: #666;
        }
        
        .action-btn {
            background: #4A90E2;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="inventory-page">
        <!-- 顶部标签栏 -->
        <div class="tab-bar">
            <div class="tab-item active">在库</div>
            <div class="tab-item">已出库</div>
            <div class="tab-item">已售出</div>
        </div>

        <!-- 搜索栏 -->
        <div class="search-bar">
            <div class="search-input">
                <span>🔍</span>
                <input type="text" placeholder="请输入激活码进行查询" />
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="content-area">
            <!-- 套餐A -->
            <div class="package-group">
                <!-- 左侧筛选区域 -->
                <div>
                    <div class="package-header">
                        <span>📋</span>
                        <span class="package-name">套餐A</span>
                    </div>
                    <div class="condition-list">
                        <div class="condition-item">
                            <div class="checkbox checked"></div>
                            <span class="condition-text">1个月</span>
                            <span class="condition-count">(20个)</span>
                        </div>
                        <div class="condition-item">
                            <div class="checkbox"></div>
                            <span class="condition-text">1年</span>
                            <span class="condition-count">(20个)</span>
                        </div>
                        <div class="condition-item">
                            <div class="checkbox"></div>
                            <span class="condition-text">2年</span>
                            <span class="condition-count">(20个)</span>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧列表区域 -->
                <div class="list-area">
                    <div class="inventory-list">
                        <div class="inventory-item">
                            <div class="item-content">
                                <div class="activation-code">我是激活码我是激活码</div>
                                <div class="validity">有效期：1个月</div>
                            </div>
                            <div class="checkbox checked">✓</div>
                        </div>
                        <div class="inventory-item">
                            <div class="item-content">
                                <div class="activation-code">我是激活码我是激活码</div>
                                <div class="validity">有效期：1个月</div>
                            </div>
                            <div class="checkbox">✓</div>
                        </div>
                        <div class="inventory-item">
                            <div class="item-content">
                                <div class="activation-code">我是激活码我是激活码</div>
                                <div class="validity">有效期：1个月</div>
                            </div>
                            <div class="checkbox checked">✓</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 套餐B -->
            <div class="package-group">
                <!-- 左侧筛选区域 -->
                <div>
                    <div class="package-header">
                        <span>📋</span>
                        <span class="package-name">套餐B</span>
                    </div>
                    <div class="condition-list">
                        <div class="condition-item">
                            <div class="checkbox"></div>
                            <span class="condition-text">1个月</span>
                            <span class="condition-count">(20个)</span>
                        </div>
                        <div class="condition-item">
                            <div class="checkbox"></div>
                            <span class="condition-text">1年</span>
                            <span class="condition-count">(20个)</span>
                        </div>
                        <div class="condition-item">
                            <div class="checkbox"></div>
                            <span class="condition-text">2年</span>
                            <span class="condition-count">(20个)</span>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧列表区域 -->
                <div class="list-area">
                    <div class="inventory-list">
                        <!-- 没有选中条件时不显示激活码 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部操作栏 -->
        <div class="bottom-bar">
            <div class="left-section">
                <div class="select-all">
                    <div class="checkbox checked">✓</div>
                    <span>全选当前页</span>
                </div>
                <span class="selected-count">已选择3个</span>
            </div>
            <div class="action-btn">选择合伙人</div>
        </div>
    </div>
</body>
</html>
