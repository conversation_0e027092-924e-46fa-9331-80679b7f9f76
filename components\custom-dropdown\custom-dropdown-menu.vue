<template>
  <view class="custom-dropdown-menu">
    <!-- 菜单栏 -->
    <view class="dropdown-bar" :style="{ zIndex:showOverlay? currentZIndex:0 }">
      <slot></slot>
    </view>
    
    <!-- 遮罩层 -->
    <view
      class="dropdown-overlay"
      v-if="showOverlay"
      :style="{ zIndex: currentZIndex - 1 }"
      @click="closeAll"
    ></view>
  </view>
</template>

<script setup>
import { ref, provide, computed } from 'vue'

// 全局 z-index 管理
let globalZIndex = 1000
const getNextZIndex = () => {
  return globalZIndex += 10
}

const props = defineProps({
  activeColor: {
    type: String,
    default: 'rgba(85, 152, 255, 1)'
  },
  overlay: {
    type: Boolean,
    default: true
  },
  zIndex: {
    type: Number,
    default: 10
  },
  duration: {
    type: Number,
    default: 200
  },
  direction: {
    type: String,
    default: 'down'
  },
  closeOnClickOverlay: {
    type: Boolean,
    default: true
  }
})

// 当前激活的下拉项索引
const activeIndex = ref(-1)
// 子组件列表
const children = ref([])
// 当前组件的动态 z-index
const currentZIndex = ref(props.zIndex)

// 是否显示遮罩层
const showOverlay = computed(() => {
  return props.overlay && activeIndex.value !== -1
})

// 注册子组件
const registerChild = (child) => {
  children.value.push(child)
  return children.value.length - 1
}

// 注销子组件
const unregisterChild = (child) => {
  const index = children.value.indexOf(child)
  if (index > -1) {
    children.value.splice(index, 1)
  }
}

// 切换下拉项
const toggle = (index, show) => {
  if (show && activeIndex.value !== -1 && activeIndex.value !== index) {
    // 关闭其他已打开的下拉项
    children.value[activeIndex.value]?.close()
  }

  if (show) {
    // 打开时分配新的 z-index，确保当前组件在最上层
    currentZIndex.value = getNextZIndex()
  }

  activeIndex.value = show ? index : -1
}

// 关闭所有下拉项
const closeAll = () => {
  if (!props.closeOnClickOverlay) return
  
  if (activeIndex.value !== -1) {
    children.value[activeIndex.value]?.close()
    activeIndex.value = -1
  }
}

// 向子组件提供方法和数据
provide('dropdownMenu', {
  props,
  activeIndex,
  currentZIndex,
  registerChild,
  unregisterChild,
  toggle
})
</script>

<style lang="scss" scoped>
.custom-dropdown-menu {
  position: relative;
}

.dropdown-bar {
  position: relative;
  display: flex;
  background: #fff;
  user-select: none;
  justify-content: space-between;
}

.dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
}
</style>
