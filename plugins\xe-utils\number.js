'use strict'

import random from './random'
import max from './max'
import min from './min'
import commafy from './commafy'
import round from './round'
import ceil from './ceil'
import floor from './floor'
import toFixed from './toFixed'
import toInteger from './toInteger'
import toNumber from './toNumber'
import toNumberString from './toNumberString'
import add from './add'
import subtract from './subtract'
import multiply from './multiply'
import divide from './divide'
import sum from './sum'
import mean from './mean'

var numberExports = {
  random: random,
  min: min,
  max: max,
  commafy: commafy,
  round: round,
  ceil: ceil,
  floor: floor,
  toFixed: toFixed,
  toNumber: toNumber,
  toNumberString: toNumberString,
  toInteger: toInteger,
  add: add,
  subtract: subtract,
  multiply: multiply,
  divide: divide,
  sum,
  mean
}

export default numberExports
