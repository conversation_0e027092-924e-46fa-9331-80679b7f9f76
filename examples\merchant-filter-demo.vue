<template>
  <view class="demo-page">
    <view class="demo-title">商户管理筛选条件演示</view>
    <view class="demo-subtitle">我的商户 vs 合伙人商户</view>
    
    <!-- Tab 切换 -->
    <view class="tab-section">
      <view class="tab-container">
        <view 
          class="tab-item"
          :class="{ 'active': activeTab === 'my' }"
          @click="activeTab = 'my'"
        >
          我的商户
        </view>
        <view 
          class="tab-item"
          :class="{ 'active': activeTab === 'partner' }"
          @click="activeTab = 'partner'"
        >
          合伙人商户
        </view>
      </view>
    </view>

    <!-- 筛选条件演示 -->
    <view class="filter-demo-section">
      <!-- 我的商户筛选条件 -->
      <template v-if="activeTab === 'my'">
        <view class="section-title">我的商户筛选条件</view>
        
        <!-- 第一行筛选条件 -->
        <custom-dropdown-menu>
          <custom-dropdown-item
            :value="filterData.merchantName"
            :options="merchantNameOptions"
            @change="onMerchantNameChange"
            title="商户名称"
            style="width: 30%;"
          />
          <custom-dropdown-item
            :value="filterData.merchantType"
            :options="merchantTypeOptions"
            @change="onMerchantTypeChange"
            title="商户类型"
            style="width: 25%;"
          />
          <custom-dropdown-item
            :value="filterData.status"
            :options="statusOptions"
            @change="onStatusChange"
            title="状态"
            style="width: 40%;"
          />
        </custom-dropdown-menu>

        <!-- 第二行筛选条件 -->
        <custom-dropdown-menu>
          <custom-dropdown-item
            :value="filterData.packageName"
            :options="packageNameOptions"
            @change="onPackageNameChange"
            title="套餐名称"
            style="width: 30%;"
          />
          <custom-dropdown-item
            title="入驻时间"
            :show-selected-value="false"
            style="width: 25%;"
          >
            <view class="datetime-picker-container">
              <uni-datetime-picker
                v-model="joinTimeRange"
                type="daterange"
                rangeSeparator="至"
                placeholder="选择入驻时间范围"
                @change="onJoinTimeChange"
              />
            </view>
          </custom-dropdown-item>
          <custom-dropdown-item
            title="激活码到期日"
            :show-selected-value="false"
            style="width: 40%;"
          >
            <view class="datetime-picker-container">
              <uni-datetime-picker
                v-model="expireTimeRange"
                type="datetimerange"
                rangeSeparator="至"
                placeholder="选择激活码到期时间范围"
                @change="onExpireTimeChange"
              />
            </view>
          </custom-dropdown-item>
        </custom-dropdown-menu>
      </template>

      <!-- 合伙人商户筛选条件 -->
      <template v-else-if="activeTab === 'partner'">
        <view class="section-title">合伙人商户筛选条件</view>
        
        <custom-dropdown-menu>
          <custom-dropdown-item
            :value="filterData.partnerName"
            :options="partnerNameOptions"
            @change="onPartnerNameChange"
            title="合伙人名称"
            style="width: 50%;"
          />
          <custom-dropdown-item
            :value="filterData.partnerStatus"
            :options="partnerStatusOptions"
            @change="onPartnerStatusChange"
            title="状态"
            style="width: 50%;"
          />
        </custom-dropdown-menu>
      </template>
    </view>

    <!-- 筛选结果显示 -->
    <view class="result-section">
      <view class="result-title">当前筛选条件</view>
      
      <view class="result-group">
        <view class="group-title">{{ activeTab === 'my' ? '我的商户' : '合伙人商户' }}</view>
        
        <template v-if="activeTab === 'my'">
          <view class="result-item">
            <text class="result-label">商户名称:</text>
            <text class="result-value">{{ filterData.merchantName || '全部' }}</text>
          </view>
          <view class="result-item">
            <text class="result-label">商户类型:</text>
            <text class="result-value">{{ filterData.merchantType || '全部' }}</text>
          </view>
          <view class="result-item">
            <text class="result-label">状态:</text>
            <text class="result-value">{{ filterData.status || '全部' }}</text>
          </view>
          <view class="result-item">
            <text class="result-label">套餐名称:</text>
            <text class="result-value">{{ filterData.packageName || '全部' }}</text>
          </view>
          <view class="result-item">
            <text class="result-label">入驻时间:</text>
            <text class="result-value">{{ joinTimeRange || '未选择' }}</text>
          </view>
          <view class="result-item">
            <text class="result-label">激活码到期日:</text>
            <text class="result-value">{{ expireTimeRange || '未选择' }}</text>
          </view>
        </template>
        
        <template v-else>
          <view class="result-item">
            <text class="result-label">合伙人名称:</text>
            <text class="result-value">{{ filterData.partnerName || '全部' }}</text>
          </view>
          <view class="result-item">
            <text class="result-label">状态:</text>
            <text class="result-value">{{ filterData.partnerStatus || '全部' }}</text>
          </view>
        </template>
      </view>
    </view>

    <!-- 功能说明 -->
    <view class="feature-section">
      <view class="feature-title">功能说明</view>
      
      <view class="feature-item">
        <view class="feature-name">我的商户</view>
        <view class="feature-desc">包含完整的筛选条件：商户名称、类型、状态、套餐、入驻时间、激活码到期日</view>
      </view>
      
      <view class="feature-item">
        <view class="feature-name">合伙人商户</view>
        <view class="feature-desc">简化的筛选条件：仅包含合伙人名称和状态两个筛选项</view>
      </view>
      
      <view class="feature-item">
        <view class="feature-name">动态切换</view>
        <view class="feature-desc">根据选中的 Tab 动态显示不同的筛选条件</view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button class="action-btn reset-btn" @click="resetFilters">重置筛选条件</button>
      <button class="action-btn search-btn" @click="searchMerchants">搜索商户</button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import CustomDropdownMenu from '@/components/custom-dropdown/custom-dropdown-menu.vue'
import CustomDropdownItem from '@/components/custom-dropdown/custom-dropdown-item.vue'

// 当前选中的Tab
const activeTab = ref('my')

// 筛选数据
const filterData = ref({
  // 我的商户筛选条件
  merchantName: '',
  merchantType: '',
  status: '',
  packageName: '',
  // 合伙人商户筛选条件
  partnerName: '',
  partnerStatus: ''
})

// uni-datetime-picker 数据
const joinTimeRange = ref('')
const expireTimeRange = ref('')

// 我的商户筛选选项
const merchantNameOptions = ref([
  { text: '全部', value: '' },
  { text: '商贸公司', value: '商贸公司' },
  { text: '科技有限公司', value: '科技有限公司' },
  { text: '餐饮管理公司', value: '餐饮管理公司' }
])

const merchantTypeOptions = ref([
  { text: '全部', value: '' },
  { text: '企业', value: '企业' },
  { text: '个体工商户', value: '个体工商户' },
  { text: '合作社', value: '合作社' }
])

const statusOptions = ref([
  { text: '全部', value: '' },
  { text: '有效', value: '有效' },
  { text: '已到期', value: '已到期' },
  { text: '待审核', value: '待审核' }
])

const packageNameOptions = ref([
  { text: '全部', value: '' },
  { text: '基础版', value: '基础版' },
  { text: '标准版', value: '标准版' },
  { text: '专业版', value: '专业版' }
])

// 合伙人商户筛选选项
const partnerNameOptions = ref([
  { text: '全部', value: '' },
  { text: '张三合伙人', value: 'zhangsan' },
  { text: '李四合伙人', value: 'lisi' },
  { text: '王五合伙人', value: 'wangwu' }
])

const partnerStatusOptions = ref([
  { text: '全部', value: '' },
  { text: '正常', value: 'active' },
  { text: '停用', value: 'inactive' },
  { text: '待审核', value: 'pending' }
])

// 我的商户筛选事件处理
const onMerchantNameChange = (value) => {
  filterData.value.merchantName = value.text
}

const onMerchantTypeChange = (value) => {
  filterData.value.merchantType = value.text
}

const onStatusChange = (value) => {
  filterData.value.status = value.text
}

const onPackageNameChange = (value) => {
  filterData.value.packageName = value.text
}

const onJoinTimeChange = (value) => {
  console.log('入驻时间选择:', value)
}

const onExpireTimeChange = (value) => {
  console.log('激活码到期时间选择:', value)
}

// 合伙人商户筛选事件处理
const onPartnerNameChange = (value) => {
  filterData.value.partnerName = value.text
}

const onPartnerStatusChange = (value) => {
  filterData.value.partnerStatus = value.text
}

// 重置筛选条件
const resetFilters = () => {
  filterData.value = {
    merchantName: '',
    merchantType: '',
    status: '',
    packageName: '',
    partnerName: '',
    partnerStatus: ''
  }
  joinTimeRange.value = ''
  expireTimeRange.value = ''
}

// 搜索商户
const searchMerchants = () => {
  console.log('搜索条件:', {
    tab: activeTab.value,
    filters: filterData.value,
    joinTime: joinTimeRange.value,
    expireTime: expireTimeRange.value
  })
}
</script>

<style lang="scss" scoped>
.demo-page {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.demo-title {
  font-size: 40rpx;
  font-weight: bold;
  text-align: center;
  color: #333;
  margin-bottom: 10rpx;
}

.demo-subtitle {
  font-size: 28rpx;
  text-align: center;
  color: #666;
  margin-bottom: 30rpx;
}

.tab-section {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
}

.tab-container {
  display: flex;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 8rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 8rpx;
  transition: all 0.3s;
  
  &.active {
    background: white;
    color: rgba(85, 152, 255, 1);
    font-weight: 600;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  }
}

.filter-demo-section {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.section-title {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  background: #f8f9fa;
  border-bottom: 1rpx solid #eee;
}

.result-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.result-group {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.group-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 16rpx;
}

.result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.result-label {
  font-size: 26rpx;
  color: #666;
}

.result-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.feature-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.feature-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.feature-item {
  margin-bottom: 20rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.feature-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.action-section {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
  
  &.reset-btn {
    background: #f8f9fa;
    color: #666;
    border: 1rpx solid #ddd;
  }
  
  &.search-btn {
    background: rgba(85, 152, 255, 1);
    color: white;
  }
}

// uni-datetime-picker 容器样式
.datetime-picker-container {
  padding: 40rpx;
  background: white;
  
  :deep(.uni-datetime-picker__input) {
    border: 1rpx solid #e9ecef;
    border-radius: 8rpx;
    padding: 20rpx 24rpx;
    font-size: 28rpx;
    background: #f8f9fa;
  }
}
</style>
