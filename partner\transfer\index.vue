<template>
  <snowy-layout :title="pageTitle" :isTabbar="false" :isFirstPage="false">
    <view class="transfer-page">
      <!-- 搜索栏 -->
      <view class="search-bar">
        <view class="search-input">
          <uni-icons type="search" size="18" color="#999"></uni-icons>
          <input type="text" placeholder="请输入激活码进行查询" v-model="searchKeyword" @input="onSearchInput" />
        </view>
      </view>
      <!-- 主要内容区域 -->
      <view class="content-area">
        <view v-for="(pkg, index) in packageList" :key="index" class="package-group">
          <!-- 左侧筛选区域 -->
          <view class="filter-sidebar">
            <view class="package-header">
              <image class="package-icon"
                :src="$store.getters.allEnv[$store.getters.envKey].image_baseUrl + '/static/images/partner/cpackage.png'"
                mode="widthFix"></image>
              <text class="package-name">{{ pkg.name }}</text>
            </view>
            <view class="condition-list">
              <view v-for="(condition, condIndex) in pkg.conditions" :key="condIndex" class="condition-item"
                @click="toggleCondition(index, condIndex)">
                <view class="checkbox" :class="{ checked: condition.checked }">
                  <uni-icons v-if="condition.checked" type="checkmarkempty" size="12" color="#fff"></uni-icons>
                </view>
                <text class="condition-text">{{ condition.name }}</text>
                <text class="condition-count">({{ condition.num || 0 }}个)</text>
              </view>
            </view>
          </view>

          <!-- 右侧列表区域 -->
          <view class="list-area">
            <scroll-view class="inventory-list" scroll-y="true" :refresher-enabled="true"
              :refresher-triggered="refreshing" @refresherrefresh="onRefresh" @scrolltolower="onLoadMore">
              <view v-for="(item, itemIndex) in pkg.children" :key="itemIndex" class="inventory-item">
                <view class="item-content">
                  <view class="activation-code">{{ item.code }}</view>
                  <view class="validity">有效期：{{ item.validity }}</view>
                </view>
                <view class="item-action">
                  <view class="checkbox" :class="{ checked: item.selected }"
                    @click="toggleItemSelection(index, itemIndex)">
                    <uni-icons v-if="item.selected" type="checkmarkempty" size="12" color="#fff"></uni-icons>
                  </view>
                </view>
              </view>

              <!-- 加载更多提示 -->
              <view v-if="loading" class="loading-tip">
                <uni-load-more :status="loadStatus"></uni-load-more>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>

      <!-- 底部操作栏 -->
      <view class="bottom-bar">
        <view class="left-section">
          <view class="select-all" @click="toggleSelectAll">
            <view class="checkbox" :class="{ checked: isAllSelected }">
              <uni-icons v-if="isAllSelected" type="checkmarkempty" size="12" color="#fff"></uni-icons>
            </view>
            <text>全选当前页</text>
          </view>
          <text class="selected-count">已选择{{ selectedCount }}个</text>
        </view>
        <view class="action-btn" :class="{ disabled: selectedCount === 0 }" @click="handleAction">
          {{ actionButtonText }}
        </view>
      </view>
    </view>
  </snowy-layout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import bizInventoryApi from '@/api/biz/bizInventoryApi'
import { onLoad } from "@dcloudio/uni-app"

const store = useStore()

// 搜索关键词
const searchKeyword = ref('')

// 刷新状态
const refreshing = ref(false)

// 加载状态
const loading = ref(false)
const loadStatus = ref('more')

// 分页参数
const currentPage = ref(1)
const pageSize = ref(20)

// 页面模式（划拨或回拨）
const isReturnMode = ref(false)
const pageTitle = ref('划拨')
const actionButtonText = ref('选择合伙人')

// 套餐列表数据
const packageList = ref([])

// 计算已选择的激活码数量
const selectedCount = computed(() => {
  let count = 0
  packageList.value.forEach(pkg => {
    pkg.children.forEach(item => {
      if (item.selected) count++
    })
  })
  return count
})

// 计算是否全选
const isAllSelected = computed(() => {
  const totalItems = packageList.value.reduce((total, pkg) => total + pkg.children.length, 0)
  return totalItems > 0 && selectedCount.value === totalItems
})

// 切换筛选条件
const toggleCondition = (packageIndex, conditionIndex) => {
  const pkg = packageList.value[packageIndex]
  const condition = pkg.conditions[conditionIndex]

  // 切换选中状态
  condition.checked = !condition.checked

  // 更新当前套餐的children列表
  updatePackageChildren(packageIndex)
}

// 更新套餐的children列表
const updatePackageChildren = (packageIndex) => {
  const pkg = packageList.value[packageIndex]
  const selectedConditions = pkg.conditions.filter(condition => condition.checked)

  // 清空当前children
  pkg.children = []

  // 根据选中的条件添加对应的激活码
  selectedConditions.forEach(condition => {
    if (condition.children && condition.children.length > 0) {
      // 为每个激活码添加 selected 属性
      const childrenWithSelection = condition.children.map(child => ({
        ...child,
        selected: child.selected || false
      }))
      pkg.children = [...pkg.children, ...childrenWithSelection]
    }
  })
}

// 切换单个激活码选择状态
const toggleItemSelection = (packageIndex, itemIndex) => {
  packageList.value[packageIndex].children[itemIndex].selected =
    !packageList.value[packageIndex].children[itemIndex].selected
}

// 切换全选状态
const toggleSelectAll = () => {
  const shouldSelectAll = !isAllSelected.value

  packageList.value.forEach(pkg => {
    pkg.children.forEach(item => {
      item.selected = shouldSelectAll
    })
  })
}

// 搜索输入
const onSearchInput = () => {
  // 实时搜索可以在这里处理
  loadData()
}

// 处理操作（划拨或回拨）
const handleAction = () => {
  if (selectedCount.value === 0) {
    uni.showToast({
      title: '请先选择激活码',
      icon: 'none'
    })
    return
  }

  if (isReturnMode.value) {
    // 回拨模式：直接确认回拨
    confirmReturn()
  } else {
    // 划拨模式：跳转到选择合伙人页面
    selectPartner()
  }
}

// 选择合伙人（划拨模式）
const selectPartner = () => {
  // 收集选中的激活码ID
  const selectedCdkeyIds = []
  packageList.value.forEach(pkg => {
    pkg.children.forEach(item => {
      if (item.selected) {
        selectedCdkeyIds.push(item.id)
      }
    })
  })

  // 将选中的激活码ID存储到全局状态或本地存储
  uni.setStorageSync('selectedCdkeyIds', selectedCdkeyIds)

  // 跳转到合伙人选择页面
  uni.navigateTo({
    url: '/partner/transfer/partner-select'
  })
}

// 确认回拨
const confirmReturn = async () => {
  // 收集选中的激活码ID
  const selectedCdkeyIds = []
  packageList.value.forEach(pkg => {
    pkg.children.forEach(item => {
      if (item.selected) {
        selectedCdkeyIds.push(item.id)
      }
    })
  })

  uni.showModal({
    title: '确认回拨',
    content: `确认回拨${selectedCount.value}个激活码？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({
            title: '回拨中...'
          })

          // 调用回拨接口
          const result = await bizInventoryApi.transferCdkey({
            cdkeyId: selectedCdkeyIds,
            type: 1, // 1-回拨
            extJson: ""
          })

          uni.showToast({
            title: '回拨成功',
            icon: 'success'
          })

          // 刷新页面数据
          setTimeout(() => {
            loadData()
          }, 1500)
        } catch (error) {
          console.error('回拨失败:', error)
          uni.showToast({
            title: '回拨失败',
            icon: 'none'
          })
        } finally {
          uni.hideLoading()
        }
      }
    }
  })
}

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true
  try {
    await loadData()
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('刷新失败:', error)
  } finally {
    refreshing.value = false
  }
}

// 加载更多
const onLoadMore = async () => {

}

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    loadStatus.value = 'loading'

    // 重置页码
    currentPage.value = 1

    const roleApp = store.getters.roleApp || {}
    const response = await bizInventoryApi.getCdkeyList({
      current: currentPage.value,
      size: pageSize.value,
      searchKey: searchKeyword.value,
      partnerId: roleApp.entityId,
      inventoryStatus: 0 // 在库状态
    })

    if (response) {
      const list = response || []
      // 重置套餐列表
      packageList.value = list.map(v => {
        v.children = []
        v.conditions = v.conditions.map(condition => ({
          ...condition,
          checked: false
        }))
        return v
      })
    } else {
      uni.showToast({
        title: response.message || '加载失败',
        icon: 'none'
      })
    }

  } catch (error) {
    console.error('加载数据失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
    loadStatus.value = 'more'
  }
}
onLoad((option) => {
  if (option.type === 'return') {
    isReturnMode.value = true
    pageTitle.value = '回拨'
    actionButtonText.value = '确认回拨'
  } else {
    isReturnMode.value = false
    pageTitle.value = '划拨'
    actionButtonText.value = '选择合伙人'
  }
})
// 页面加载时检查URL参数，判断是否为回拨模式
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.transfer-page {
  height: 100%;
  background-color: #f5f6fa;
  display: flex;
  flex-direction: column;
}

.search-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #eee;

  .search-input {
    flex: 1;
    display: flex;
    align-items: center;
    background: #f5f6fa;
    border-radius: 50rpx;
    padding: 20rpx 30rpx;

    input {
      flex: 1;
      margin-left: 20rpx;
      font-size: 28rpx;
      color: #333;

      &::placeholder {
        color: #999;
      }
    }
  }
}

.content-area {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
  padding-bottom: 120rpx; // 为底部操作栏留出空间
}

.package-group {
  background: white;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  overflow: hidden;

  // 左侧筛选区域
  >view:first-child {
    width: 220rpx;
    border-right: 1rpx solid #f0f0f0;

    .package-header {
      display: flex;
      align-items: center;
      padding: 30rpx 20rpx 20rpx 20rpx;

      .package-icon {
        width: 35rpx;
      }

      .package-name {
        margin-left: 15rpx;
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
      }
    }

  }
}

.filter-sidebar {
  background: rgba(239, 247, 255, 1);
  padding-bottom: 25rpx;
}

.condition-item {
  display: flex;
  align-items: center;
  padding: 13rpx 20rpx;
  border-bottom: 1rpx solid #f5f5f5;

  .checkbox {
    width: 32rpx;
    height: 32rpx;
    border: 2rpx solid #ddd;
    border-radius: 4rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20rpx;

    &.checked {
      background: rgba(82, 150, 255, 1);
      border-color: rgba(82, 150, 255, 1);
    }
  }

  .condition-text {
    flex: 1;
    font-size: 26rpx;
    color: #333;
    white-space: nowrap;
  }

  .condition-count {
    font-size: 24rpx;
    color: #999;
    white-space: nowrap;
  }
}

.list-area {
  flex: 1;
  overflow: hidden;
  padding: 20rpx;

  .inventory-list {
    max-height: 240px;

    box-sizing: border-box;
    overflow: auto;

    .inventory-item {
      display: flex;
      align-items: center;
      padding: 15rpx 0;
      border-bottom: 1rpx solid #f5f5f5;

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }

      .item-content {
        flex: 1;

        .activation-code {
          font-size: 26rpx;
          color: #333;
          font-weight: 500;
          margin-bottom: 15rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .validity {
          font-size: 26rpx;
          color: #666;
        }
      }

      .item-action {
        .checkbox {
          width: 32rpx;
          height: 32rpx;
          border: 2rpx solid #ddd;
          border-radius: 4rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          &.checked {
            background: rgba(82, 150, 255, 1);
            border-color: rgba(82, 150, 255, 1);
          }
        }
      }
    }

    .loading-tip {
      padding: 30rpx;
      text-align: center;
    }
  }
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 100;

  .left-section {
    display: flex;
    align-items: center;

    .select-all {
      display: flex;
      align-items: center;
      margin-right: 30rpx;

      .checkbox {
        width: 32rpx;
        height: 32rpx;
        border: 2rpx solid #ddd;
        border-radius: 4rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15rpx;

        &.checked {
          background: rgba(82, 150, 255, 1);
          border-color: rgba(82, 150, 255, 1);
        }
      }

      text {
        font-size: 28rpx;
        color: #333;
      }
    }

    .selected-count {
      font-size: 26rpx;
      color: #666;
    }
  }

  .action-btn {
    background: rgba(82, 150, 255, 1);
    color: white;
    padding: 20rpx 40rpx;
    border-radius: 50rpx;
    font-size: 28rpx;

    &.disabled {
      background: #ccc;
      color: #999;
    }
  }
}
</style>
