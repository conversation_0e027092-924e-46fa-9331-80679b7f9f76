<template>
	<view class="floating-button" @click="handleButtonClick">
		<slot>
			<uni-icons type="plusempty" color="#ffffff" :size="20"></uni-icons>
		</slot>
	</view>
</template>
<script setup>
	const emits = defineEmits(['click'])
	const handleButtonClick = () => {
		emits('click');
	}
</script>
<style lang="scss" scoped>
	.floating-button {
		position: fixed;
		bottom: 40rpx;
		right: 40rpx;
		width: 100rpx;
		height: 100rpx;
		background-color: #5677fc;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0px 2rpx 4rpx rgba(0, 0, 0, 0.2);
		transition: background-color 0.3s, transform 0.3s;
		cursor: pointer;
	}

	.floating-button:hover {
		background-color: #2979ff;
		transform: scale(1.1);
	}
</style>