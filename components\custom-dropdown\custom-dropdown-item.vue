<template>
  <view class="custom-dropdown-item">
    <!-- 标题栏 -->
    <view 
      class="dropdown-title"
      :class="{ 'dropdown-title--active': showPopup }"
      :style="{ color: titleColor }"
      @click="toggle"
    >
      <text class="dropdown-title-text">{{ displayTitle }}</text>
      <view 
        class="dropdown-title-icon"
        :class="{ 'dropdown-title-icon--rotate': showPopup }"
        :style="{ color: titleColor }"
      >
        <image class="icon-arrow" :src="$store.getters.allEnv[$store.getters.envKey].image_baseUrl+'/static/images/partner/select-icon.png'" mode="aspectFit"></image>
      </view>
    </view>
    
    <!-- 下拉内容 -->
    <view 
      class="dropdown-content"
      :class="[
        `dropdown-content--${direction}`,
        { 'dropdown-content--show': showPopup }
      ]"
      :style="{
        zIndex: zIndex,
        animationDuration: duration + 'ms'
      }"
      v-if="showWrapper"
    >
      <!-- 默认选项列表 -->
      <scroll-view 
        class="dropdown-options" 
        scroll-y
        v-if="options && options.length > 0"
      >
        <view 
          class="dropdown-option"
          :class="{ 'dropdown-option--active': option.value === value }"
          v-for="(option, index) in options"
          :key="index"
          @click="selectOption(option)"
        >
          <text 
            class="dropdown-option-text"
            :style="{ color: option.value === value ? activeColor : '#323233' }"
          >
            {{ option.text }}
          </text>
          <view 
            class="dropdown-option-icon"
            v-if="option.value === value"
            :style="{ color: activeColor }"
          >
            <text class="icon-check">✓</text>
          </view>
        </view>
      </scroll-view>
      
      <!-- 自定义内容插槽 -->
      <slot v-else></slot>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, inject, onMounted, onUnmounted, watch } from 'vue'

const props = defineProps({
  value: {
    type: [String, Number],
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  options: {
    type: Array,
    default: () => []
  },
  disabled: {
    type: Boolean,
    default: false
  },
  titleClass: {
    type: String,
    default: ''
  },
  showSelectedValue: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['change', 'open', 'close', 'opened', 'closed'])

// 注入父组件提供的方法和数据
const dropdownMenu = inject('dropdownMenu', {})
const { props: menuProps = {}, activeIndex, currentZIndex, registerChild, unregisterChild, toggle: menuToggle } = dropdownMenu

// 组件状态
const showPopup = ref(false)
const showWrapper = ref(false)
const childIndex = ref(-1)
const contentTop = ref(0)

// 计算属性
const activeColor = computed(() => menuProps.activeColor || 'rgba(85, 152, 255, 1)')
const zIndex = computed(() => currentZIndex?.value || menuProps.zIndex || 10)
const duration = computed(() => menuProps.duration || 200)
const direction = computed(() => menuProps.direction || 'down')

// 显示的标题
const displayTitle = computed(() => {
  // 如果设置了固定标题且不显示选中值，直接返回标题
  if (props.title && !props.showSelectedValue) {
    return props.title
  }

  // 如果有选项列表且允许显示选中值
  if (props.options && props.options.length > 0 && props.showSelectedValue) {
    const selectedOption = props.options.find(option => option.value === props.value)
    if (selectedOption) {
      return selectedOption.text
    }
  }

  // 返回固定标题或默认文本
  return props.title || '请选择'
})

// 标题颜色
const titleColor = computed(() => {
  return showPopup.value ? activeColor.value : '#323233'
})

// 计算下拉内容位置
const calculatePosition = () => {
  // 获取当前组件的位置信息
  const query = uni.createSelectorQuery()
  query.select('.dropdown-title').boundingClientRect((rect) => {
    if (rect) {
      if (direction.value === 'up') {
        contentTop.value = rect.top - 400 // 向上展开时，减去预估的下拉内容高度
      } else {
        contentTop.value = rect.bottom // 向下展开时，使用底部位置
      }
    }
  }).exec()
}

// 切换下拉状态
const toggle = () => {
  if (props.disabled) return

  if (showPopup.value) {
    close()
  } else {
    open()
  }
}

// 打开下拉
const open = () => {
  if (props.disabled) return

  // 计算下拉内容位置
  calculatePosition()

  showWrapper.value = true

  // 使用 setTimeout 替代 nextTick，确保更好的兼容性
  setTimeout(() => {
    showPopup.value = true
    menuToggle?.(childIndex.value, true)
    emit('open')

    // 动画结束后触发 opened 事件
    setTimeout(() => {
      emit('opened')
    }, duration.value)
  }, 10)
}

// 关闭下拉
const close = () => {
  showPopup.value = false
  menuToggle?.(childIndex.value, false)
  emit('close')
  
  // 动画结束后隐藏包装器并触发 closed 事件
  setTimeout(() => {
    showWrapper.value = false
    emit('closed')
  }, duration.value)
}

// 选择选项
const selectOption = (option) => {
  emit('change', option)
  close()
}

// 监听父组件的 activeIndex 变化
watch(() => activeIndex?.value, (newIndex) => {
  if (newIndex !== childIndex.value && showPopup.value) {
    close()
  }
})

// 组件挂载时注册到父组件
onMounted(() => {
  if (registerChild) {
    childIndex.value = registerChild({
      close
    })
  }
})

// 组件卸载时从父组件注销
onUnmounted(() => {
  if (unregisterChild) {
    unregisterChild({
      close
    })
  }
})

// 暴露方法给父组件
defineExpose({
  close
})
</script>

<style lang="scss" scoped>
.custom-dropdown-item {
  position: relative;
  flex: 1;
}

.dropdown-title {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 24rpx;
  height: 96rpx;
  color: #323233;
  font-size: 28rpx;
  line-height: 36rpx;
  cursor: pointer;
  user-select: none;
  border-right: 1rpx solid #ebedf0;
  
  &:last-child {
    border-right: none;
  }
  
  &--active {
    color: rgba(85, 152, 255, 1);
  }
}

.dropdown-title-text {
  max-width: 200rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-title-icon {
  margin-left: 8rpx;
  transition: transform 0.3s;
  
  &--rotate {
    transform: rotate(180deg);
  }
}

.icon-arrow {
  width: 20rpx;
  height: 20rpx;
}

.dropdown-content {
  position: fixed;
  left: 0;
  right: 0;
  width: 100vw;
  margin: 0;
  padding: 0;
  background: #fff;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.12);
  transform-origin: center top;
  transition: all 0.3s ease;
  border-radius: 0;
  
  &--down {
    transform: scaleY(0);

    &.dropdown-content--show {
      transform: scaleY(1);
    }
  }

  &--up {
    transform: scaleY(0);

    &.dropdown-content--show {
      transform: scaleY(1);
    }
  }
}

.dropdown-options {
  max-height: 400rpx;
  width: 100%;
  box-sizing: border-box;
}

.dropdown-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  color: #323233;
  font-size: 28rpx;
  line-height: 40rpx;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background: #f2f3f5;
  }

  &--active {
    color: rgba(85, 152, 255, 1);
    font-weight: 500;
  }
}

.dropdown-option-text {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-option-icon {
  margin-left: 16rpx;
  font-size: 32rpx;
  line-height: 1;
}

.icon-check {
  font-size: 24rpx;
  font-weight: bold;
}
</style>
