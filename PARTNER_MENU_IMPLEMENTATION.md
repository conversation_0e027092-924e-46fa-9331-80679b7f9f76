# 合伙人菜单系统实现总结

## 已完成的工作

### 1. 修改 `utils/auth.js` 文件

#### 新增功能：
- 导入了 `userIdentity` 工具类用于身份判断
- 添加了 `getMenusByUserIdentity()` 函数，根据用户身份返回不同菜单
- 创建了 `getMerchantMenus()` 函数，返回商户管理员菜单
- 创建了 `getPartnerMenus()` 函数，返回合伙人菜单
- 修改了 `getDefaultMenus()` 函数，返回默认菜单（员工、推荐官等）
- 添加了 `getHomePathByUserIdentity()` 函数，根据身份返回不同的首页路径
- 修改了 `initializeApp()` 函数，根据身份跳转到对应首页

#### 菜单配置：

**商户管理员菜单**：
- 我的线索 (`/pages/my-clue/index`)
- 团队线索 (`/pages/team-clue/index`)
- 商户看板 (`/pages/merchant-board/index`)
- 访问记录 (`/pages/visit-record/index`)
- 我的 (`/pages/mine/index`)

**合伙人菜单**：
- 首页 (`/partner/home/<USER>
- 我的 (`/partner/mine/index`)

**默认菜单**（员工、推荐官等）：
- 我的线索 (`/pages/my-clue/index`)
- 我的 (`/pages/mine/index`)

### 2. 修改 `pages.json` 文件

#### 新增分包：
```json
{
  "root": "partner",
  "pages": [
    {
      "path": "home/index",
      "style": {
        "navigationBarTitleText": "合伙人首页",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "mine/index",
      "style": {
        "navigationBarTitleText": "我的",
        "navigationStyle": "custom"
      }
    }
  ]
}
```

### 3. 创建合伙人页面

#### 合伙人首页 (`partner/home/<USER>
**功能特性**：
- 自定义导航栏
- 用户信息卡片（头像、姓名、身份、收益统计）
- 功能菜单（我的团队、收益明细、推广中心、数据统计）
- 数据概览（团队人数、活跃用户、本月订单、转化率）
- 最新动态列表
- 响应式设计，适配不同屏幕尺寸

**主要组件**：
- 用户信息展示
- 收益数据展示
- 功能入口导航
- 数据统计面板
- 动态消息列表

#### 合伙人我的页面 (`partner/mine/index.vue`)
**功能特性**：
- 自定义导航栏
- 用户信息头部（头像、姓名、身份、ID、编辑按钮）
- 收益概览（累计收益、可提现余额、本月收益）
- 分组功能菜单：
  - 业务功能：我的团队、收益明细、提现记录
  - 推广功能：推广中心、推广二维码、推广素材
  - 系统功能：设置、帮助中心、关于我们
- 退出登录功能

### 4. 创建图标资源

#### 新增图标文件：
- `/static/images/tabbar/home.png` - 首页图标
- `/static/images/tabbar/home-active.png` - 首页激活图标
- `/static/images/icons/` 目录下的功能图标：
  - `team.png` - 团队图标
  - `earnings.png` - 收益图标
  - `promotion.png` - 推广图标
  - `data.png` - 数据统计图标
  - `withdraw.png` - 提现图标
  - `qrcode.png` - 二维码图标
  - `materials.png` - 素材图标
  - `settings.png` - 设置图标
  - `help.png` - 帮助图标
  - `about.png` - 关于图标

*注意：当前创建的是占位文件，实际项目中需要替换为真实的图标文件*

## 身份判断逻辑

系统通过 `userIdentity` 工具类判断用户身份：

1. **合伙人** (`userIdentity.isPartner()`)
   - 显示合伙人专属菜单和页面
   - 首页：`/partner/home/<USER>

2. **商户管理员** (`userIdentity.isMerchantAdmin()` 或 `userIdentity.isGroupMerchant()`)
   - 显示完整的商户管理功能菜单
   - 首页：`/pages/my-clue/index`

3. **其他身份**（员工、推荐官等）
   - 显示简化的默认菜单
   - 首页：`/pages/my-clue/index`

## 使用方法

### 1. 身份切换测试
可以通过修改用户的 `roleApp` 信息来测试不同身份的菜单：

```javascript
// 模拟合伙人身份
store.commit('SET_roleApp', {
  entityType: 'PARTNER',
  entitySubType: 'FIRST'
})

// 重新初始化应用
initializeApp()
```

### 2. 菜单扩展
如需添加新的菜单项，可以在对应的菜单函数中添加：

```javascript
// 在 getPartnerMenus() 中添加新菜单
{
  path: '/partner/new-feature/index',
  text: '新功能',
  icon: '/static/images/icons/new-feature.png',
  activeIcon: '/static/images/icons/new-feature-active.png',
  extJson: '[]'
}
```

### 3. 页面开发
合伙人相关的新页面应该放在 `partner/` 目录下，并在 `pages.json` 的 `partner` 分包中注册。

## 注意事项

1. **图标资源**：当前使用的是占位图标，需要替换为实际的图标文件
2. **API 接口**：页面中的数据获取逻辑需要对接实际的后端 API
3. **权限控制**：确保后端也有相应的权限验证机制
4. **错误处理**：添加了基本的错误处理，但可能需要根据实际需求进一步完善
5. **响应式设计**：页面已考虑响应式设计，但可能需要在不同设备上进一步测试

## 下一步工作

1. 替换占位图标为实际图标
2. 开发合伙人相关的子页面（团队管理、收益明细等）
3. 对接后端 API 获取真实数据
4. 添加更详细的错误处理和加载状态
5. 进行不同设备的兼容性测试
