<template>
  <snowy-layout title="首页" :isTabbar="true" :isFirstPage="true">
    <view class="partner-home">
      <!-- 时间筛选标签 -->
      <view class="time-filter">
        <view class="filter-tabs">
          <view class="filter-tab" :class="{ active: activeTab === 'week' }" @click="switchTab('week')">
            <text>本周</text>
          </view>
          <view class="filter-tab" :class="{ active: activeTab === 'month' }" @click="switchTab('month')">
            <text>近30日</text>
          </view>
          <view class="filter-tab" :class="{ active: activeTab === 'quarter' }" @click="switchTab('quarter')">
            <text>近3月</text>
          </view>
          <view class="filter-tab" :class="{ active: activeTab === 'more' }" @click="switchTab('more')">
            <text>更多</text>
          </view>
        </view>
      </view>

      <!-- 新增商户卡片 -->
      <view class="merchant-card">
        <image class="banner" :src="$store.getters.allEnv[$store.getters.envKey].image_baseUrl+'/static/images/partner/home-banner.png'" mode="widthFix"></image>
        <view class="card-content">
          <view class="card-header">
            <text class="card-title">新增商户</text>
          </view>
          <view class="card-number">
            <text class="number">{{ homeData.addMerchant }}</text>
          </view>
        </view>
      </view>

      <!-- 功能菜单 -->
      <view class="function-menu">
        <view class="menu-grid">
          <view class="menu-item" @click="navigateTo('/partner/merchant/index')">
            <image class="menu-icon merchant-icon" :src="$store.getters.allEnv[$store.getters.envKey].image_baseUrl+'/static/images/partner/home-sh.png'" mode="aspectFit"></image>
            <text class="menu-text">商户管理</text>
          </view>
          <view class="menu-item" @click="navigateTo('/partner/inventory/index')">
            <image class="menu-icon inventory-icon" :src="$store.getters.allEnv[$store.getters.envKey].image_baseUrl+'/static/images/partner/home-kc.png'" mode="aspectFit"></image>
            <text class="menu-text">我的库存</text>
          </view>
          <!-- 新建合伙人 - 只有一级合伙人才显示 -->
          <view v-if="$store.getters.isFirstPartner" class="menu-item" @click="navigateTo('/partner/secondAdd/index')">
            <image class="menu-icon partner-icon" :src="$store.getters.allEnv[$store.getters.envKey].image_baseUrl+'/static/images/partner/home-add.png'" mode="aspectFit"></image>
            <text class="menu-text">新建合伙人</text>
          </view>
          <!-- 我的合伙人 - 只有一级合伙人才显示 -->
          <view v-if="$store.getters.isFirstPartner" class="menu-item" @click="navigateTo('/partner/secondList/index')">
            <image class="menu-icon my-partner-icon" :src="$store.getters.allEnv[$store.getters.envKey].image_baseUrl+'/static/images/partner/home-hhr.png'" mode="aspectFit"></image>
            <text class="menu-text">我的合伙人</text>
          </view>
          <view class="menu-item" @click="navigateTo('/partner/more/index')">
            <image class="menu-icon more-icon" :src="$store.getters.allEnv[$store.getters.envKey].image_baseUrl+'/static/images/partner/home-more.png'" mode="aspectFit"></image>
            <text class="menu-text">更多</text>
          </view>
        </view>
      </view>

      <!-- 我的合伙人数据 - 只有一级合伙人才显示 -->
      <view v-if="homeData.level==1" class="partner-data">
        <view class="section-title">
          <text>我的合伙人</text>
        </view>
        <view class="partner-grid">
          <view class="partner-item">
            <text class="partner-value">{{ homeData.myPartner.partnerNum }}</text>
            <text class="partner-label">合伙人数量</text>
          </view>
          <view class="partner-item">
            <text class="partner-value">{{ homeData.myPartner.merchantNum }}</text>
            <text class="partner-label">合作商户</text>
          </view>
          <view class="partner-item">
            <text class="partner-value">{{ homeData.myPartner.cdkeyNum }}</text>
            <text class="partner-label">激活码</text>
          </view>
          <view class="partner-item">
            <text class="partner-value">{{ homeData.myPartner.expireCdkeyNum }}</text>
            <text class="partner-label">到期激活码</text>
          </view>
        </view>
      </view>

      <!-- 我的商户数据 -->
      <view class="merchant-data">
        <view class="section-title">
          <text>我的商户</text>
        </view>
        <view class="merchant-grid">
          <view class="merchant-item">
            <text class="merchant-value">{{ homeData.cdkeySt.merchantNum }}</text>
            <text class="merchant-label">商户数</text>
          </view>
          <view class="merchant-item">
            <text class="merchant-value">{{ homeData.cdkeySt.cdkeyNum }}</text>
            <text class="merchant-label">激活码</text>
          </view>
          <view class="merchant-item">
            <text class="merchant-value">{{ homeData.cdkeySt.expireCdkeyNum }}</text>
            <text class="merchant-label">到期激活码</text>
          </view>
        </view>
      </view>

      <!-- 我的激活码数据 -->
      <view class="activation-data">
        <view class="section-title">
          <text>我的激活码</text>
        </view>
        <view class="activation-grid">
          <view class="activation-item">
            <text class="activation-value">{{ homeData.cdkey.total }}</text>
            <text class="activation-label">共计</text>
          </view>
          <view class="activation-item">
            <text class="activation-value">{{ homeData.cdkey.unActiveCount }}</text>
            <text class="activation-label">未激活</text>
          </view>
          <view class="activation-item">
            <text class="activation-value">{{ homeData.cdkey.expirdCount }}</text>
            <text class="activation-label">已到期</text>
          </view>
          <view class="activation-item">
            <text class="activation-value">{{ homeData.cdkey.inStockCount }}</text>
            <text class="activation-label">未划拨</text>
          </view>
          <view v-if="homeData.level==1" class="activation-item">
            <text class="activation-value">{{ homeData.cdkey.notInStockCount }}</text>
            <text class="activation-label">已划拨</text>
          </view>
          <view class="activation-item">
            <text class="activation-value">{{ homeData.cdkey.usedCount }}</text>
            <text class="activation-label">已售出</text>
          </view>
        </view>

        <!-- 到期提醒卡片 -->
        <view class="expiry-alerts">
          <view class="alert-card warning" @click="navigateToMerchantWithExpiry('upcoming')">
            <view class="alert-content">
              <text class="alert-title">31日内到期商户</text>
              <text class="alert-number">{{ homeData.expireIn31 }}</text>
            </view>
          </view>
          <view class="alert-card danger" @click="navigateToMerchantWithExpiry('expired')">
            <view class="alert-content">
              <text class="alert-title">已到期不超过31天的商户</text>
              <text class="alert-number">{{ homeData.expiredNot31 }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 日期范围选择弹框 -->
    <uni-popup ref="dateRangePopup" type="center" :mask-click="false">
      <view class="date-range-popup">
        <view class="popup-header">
          <text class="popup-title">选择日期范围</text>
          <view class="popup-close" @click="closeDateRangePopup">×</view>
        </view>
        <view class="popup-content">
          <uni-datetime-picker
            v-model="customDateRange"
            type="daterange"
            rangeSeparator="至"
            placeholder="选择日期范围"
            @change="onCustomDateChange"
          />
        </view>
        <view class="popup-footer">
          <view class="popup-btn cancel" @click="closeDateRangePopup">取消</view>
          <view class="popup-btn confirm" @click="confirmCustomDate">确定</view>
        </view>
      </view>
    </uni-popup>
  </snowy-layout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useStore } from 'vuex'
import bizInventoryApi from '@/api/biz/bizInventoryApi'

const store = useStore()

// 当前选中的时间筛选标签
const activeTab = ref('week')

// 日期范围选择相关
const dateRangePopup = ref(null)
const customDateRange = ref('')

// 主页统计数据
const homeData = ref({
  addMerchant: 0,  // 新增商户
  level: 0,        // 合伙人级别：1-一级合伙人；2-二级合伙人
  myPartner: {     // 我的合伙人
    partnerNum: 0,    // 合伙人数量
    merchantNum: 0,   // 商户数
    cdkeyNum: 0,      // 激活码数
    expireCdkeyNum: 0 // 到期激活码
  },
  cdkeySt: {       // 激活码统计
    merchantNum: 0,   // 商户数
    cdkeyNum: 0,      // 激活码
    expireCdkeyNum: 0 // 到期激活码
  },
  cdkey: {         // 激活码详情
    total: 0,           // 总数
    unActiveCount: 0,   // 未激活数
    expirdCount: 0,     // 已到期总数
    inStockCount: 0,    // 在库总数
    notInStockCount: 0, // 已出库总数
    usedCount: 0        // 已售出总数
  },
  expireIn31: 0,    // 31日内到期商户
  expiredNot31: 0   // 已到期不超过31天的商户
})

// 切换时间筛选标签
const switchTab = (tab) => {
  if (tab === 'more') {
    // 显示日期范围选择弹框
    dateRangePopup.value.open()
  } else {
    activeTab.value = tab
    loadData()
  }
}

// 日期范围选择相关函数
const onCustomDateChange = (value) => {
  customDateRange.value = value
}

const closeDateRangePopup = () => {
  dateRangePopup.value.close()
  // 重置为之前的选中状态
  if (activeTab.value === 'more') {
    activeTab.value = 'month'
  }
}

const confirmCustomDate = () => {
  if (!customDateRange.value) {
    uni.showToast({
      title: '请选择日期范围',
      icon: 'none'
    })
    return
  }

  activeTab.value = 'more'
  dateRangePopup.value.close()
  loadData()
}

// 页面跳转
const navigateTo = (url) => {
  uni.navigateTo({
    url: url,
    fail: () => {
      uni.showToast({
        title: '页面开发中',
        icon: 'none'
      })
    }
  })
}

// 跳转到商户管理页面并设置到期筛选
const navigateToMerchantWithExpiry = (type) => {
  // 计算1个月后的日期
  const now = new Date()
  const oneMonthLater = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)

  // 格式化日期为 YYYY-MM-DD
  const formatDate = (date) => {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day} 00:00:00`
  }


  let params = ''
  if (type === 'upcoming') {
    // 31天内到期：设置激活码到期日范围为今天到1个月后
    params = `?cpEffectEndTimeStart=${formatDate(now)}&cpEffectEndTimeEnd=${formatDate(oneMonthLater)}`
  } else if (type === 'expired') {
    // 已到期不超过31天：设置激活码到期日范围为1个月前到今天
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    params = `?cpEffectEndTimeStart=${formatDate(oneMonthAgo)}&cpEffectEndTimeEnd=${formatDate(now)}`
  }

  uni.navigateTo({
    url: `/partner/merchant/index${params}`
  })
}

// 获取数据
const loadData = async () => {
  try {
    const roleApp = store.getters.roleApp || {}
    const params = {
      partnerId: roleApp.entityId
    }

    // 根据选中的时间类型设置参数
    if (activeTab.value === 'more') {
      // 自定义日期范围
      if (customDateRange.value && Array.isArray(customDateRange.value)) {
        params.dateTimeStart = customDateRange.value[0]
        params.dateTimeEnd = customDateRange.value[1]
      }
    } else {
      // 预设时间类型
      const dateTypeMap = {
        'week': 0,    // 本周
        'month': 1,   // 近30日
        'quarter': 2  // 近三月
      }
      params.dateType2 = dateTypeMap[activeTab.value]
    }

    const response = await bizInventoryApi.getHomeStatistics(params)

    if (response) {
      homeData.value = response
    } else {
      uni.showToast({
        title: '获取数据失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  }
}

onMounted(() => {
  // 加载数据
  loadData()
})
</script>

<style lang="scss" scoped>
.partner-home {
  min-height: 100vh;
  background-color: #f5f6fa;
  background: url('https://www.antscard.com/minio/art-cloud-prod/static/images/partner/home-bg.png') no-repeat -600rpx -240rpx;
}

.time-filter {
  padding: 20rpx 30rpx;

  .filter-tabs {
    display: flex;
    justify-content: space-around;

    .filter-tab {
      position: relative;
      padding: 15rpx 20rpx 0rpx 20rpx;
      font-size: 28rpx;
      color: #666;
      text-align: center;

      &.active {
        color: #4A90E2;
        font-weight: 600;

        &::after {
          content: '';
          position: absolute;
          bottom: -10rpx;
          left: 50%;
          transform: translateX(-50%);
          width: 60rpx;
          height: 6rpx;
          background: #4A90E2;
          border-radius: 3rpx;
        }
      }
    }
  }
}

.merchant-card {
  margin: 30rpx;
  color: white;
  text-align: center;
  position: relative;
  .card-header {
    margin-bottom: 20rpx;

    .card-title {
      font-size: 36rpx;
      font-weight: 500;
    }
  }

  .card-number {
    .number {
      font-size: 52rpx;
      font-weight: bold;
    }
  }
}

.function-menu {
  margin: 30rpx;

  .menu-grid {
    display: flex;
    // justify-content: space-around;
    flex-wrap: nowrap;

    .menu-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      // padding: 20rpx 10rpx;
      flex: 0 0 20%;
      min-width: 0;

      .menu-icon {
        width: 96rpx;
        height: 96rpx;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 15rpx;
        position: relative;

        .icon-bg {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          .icon-text {
            font-size: 36rpx;
          }
        }

        &.merchant-icon .icon-bg {
          background: #FFD93D;
        }

        &.inventory-icon .icon-bg {
          background: #1DD1A1;
        }

        &.partner-icon .icon-bg {
          background: #54A0FF;
        }

        &.my-partner-icon .icon-bg {
          background: #FF9F43;
        }

        &.more-icon .icon-bg {
          background: #A4B0BE;
        }
      }

      .menu-text {
        font-size: 24rpx;
        color: #666;
        text-align: center;
        line-height: 1.2;
      }
    }
  }
}

.partner-data,
.merchant-data {
  margin: 30rpx;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  // box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 30rpx;
  }
}

.partner-data {
  .partner-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20rpx;

    .partner-item {
      text-align: center;
      padding: 20rpx 10rpx;

      .partner-value {
        display: block;
        font-size: 40rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 8rpx;
      }

      .partner-label {
        font-size: 24rpx;
        color: #666;
        line-height: 1.2;
      }
    }
  }
}

.merchant-data {
  .merchant-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;

    .merchant-item {
      text-align: center;
      padding: 20rpx 10rpx;

      .merchant-value {
        display: block;
        font-size: 40rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 8rpx;
      }

      .merchant-label {
        font-size: 24rpx;
        color: #666;
        line-height: 1.2;
      }
    }
  }
}

.activation-data {
  margin: 30rpx;
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  // box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 30rpx;
  }

  .activation-grid {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 30rpx;

    .activation-item {
      text-align: center;
      padding: 15rpx 8rpx;

      .activation-value {
        display: block;
        font-size: 36rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 8rpx;
      }

      .activation-label {
        font-size: 22rpx;
        color: #666;
        line-height: 1.2;
      }
    }
  }

  .expiry-alerts {
    display: flex;
    gap: 20rpx;

    .alert-card {
      flex: 1;
      border-radius: 15rpx;
      overflow: hidden;

      .alert-content {
        padding: 25rpx 20rpx;
        text-align: center;

        .alert-title {
          display: block;
          font-size: 24rpx;
          margin-bottom: 15rpx;
          line-height: 1.3;
        }

        .alert-number {
          font-size: 48rpx;
          font-weight: bold;
        }
      }

      &.warning {
        background: #FFF2CC;

        .alert-content {
          .alert-title {
            color: #8B6914;
          }

          .alert-number {
            color: #8B6914;
          }
        }
      }

      &.danger {
        background: #FFE4E1;

        .alert-content {
          .alert-title {
            color: #D73527;
          }

          .alert-number {
            color: #D73527;
          }
        }
      }
    }
  }
}

.banner {
  width: 100%;
}
.card-content{
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 日期范围选择弹框样式
.date-range-popup {
  width: 600rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1px solid #f0f0f0;

    .popup-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .popup-close {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 40rpx;
      color: #999;
      cursor: pointer;
    }
  }

  .popup-content {
    padding: 40rpx 30rpx;
  }

  .popup-footer {
    display: flex;
    border-top: 1px solid #f0f0f0;

    .popup-btn {
      flex: 1;
      height: 100rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      cursor: pointer;

      &.cancel {
        color: #666;
        border-right: 1px solid #f0f0f0;
      }

      &.confirm {
        color: #007aff;
        font-weight: 600;
      }
    }
  }
}
</style>
