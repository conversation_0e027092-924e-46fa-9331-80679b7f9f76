import config from "@/config";
import storage from "@/utils/storage";
import { STORE_KEY_ENUM } from "@/enum/store-key";
import smCrypto from "@/utils/sm-crypto";
import loginApi from "@/api/auth/login-api";
import userCenterApi from "@/api/sys/user-center-api";
import { setToken, removeToken } from "@/utils/auth";
import configApi from "@/api/dev/config-api";
import dictApi from "@/api/dev/dict-api";
import env from "@/env";
import store from "@/store";
export default {
  state: {
    // 环境key
    envKey: storage.get(STORE_KEY_ENUM.envKey) || env.DEFAULT_ENV_KEY,
    // 所有环境
    allEnv: storage.get(STORE_KEY_ENUM.allEnv) || env.DEFAULT_ALL_ENV,
    // 当前租户域
    tenantDomain:
      storage.get(STORE_KEY_ENUM.tenantDomain) ||
      env.DEFAULT_ALL_ENV[env.DEFAULT_ENV_KEY].tenantDomain,
    // token信息
    token: uni.getStorageSync('token'),
    // 首页配置
    homeConfigs: storage.get(STORE_KEY_ENUM.homeConfigs) || config.HOME_CONFIGS,
    // 用户移动端菜单（用户菜单处理后的结果）
    userMobileMenus: storage.get(STORE_KEY_ENUM.userMobileMenus),
    // 用户信息
    userInfo: storage.get(STORE_KEY_ENUM.userInfo),
    // 用户角色应用信息
    roleApp: storage.get(STORE_KEY_ENUM.roleApp),
    // 系统配置
    sysBaseConfig:
      storage.get(STORE_KEY_ENUM.sysBaseConfig) || config.SYS_BASE_CONFIG,
    // 字典数据
    dictTypeTreeData: storage.get(STORE_KEY_ENUM.dictTypeTreeData),
    // 刷新KEY
    refreshKey: storage.get(STORE_KEY_ENUM.refreshKey) || "page",
    // 刷新标记
    refreshFlag: storage.get(STORE_KEY_ENUM.refreshFlag) || false,
    // 刷新参数
    refreshParam: storage.get(STORE_KEY_ENUM.refreshParam) || null,
    // 通用参数
    commonParam: storage.get(STORE_KEY_ENUM.commonParam) || null,
    menus: [],
    currentMenu: {
      path: '',
      text: '',
      icon: '',
      activeIcon: '',
      extJson: '',
    },
    cardInfo:{},
  },
  mutations: {
    SET_envKey: (state, envKey) => {
      state.envKey = envKey;
      storage.set(STORE_KEY_ENUM.envKey, envKey);
    },
    SET_allEnv: (state, allEnv) => {
      state.allEnv = allEnv;
      storage.set(STORE_KEY_ENUM.allEnv, allEnv);
    },
    SET_tenantDomain: (state, tenantDomain) => {
      state.tenantDomain = tenantDomain;
      storage.set(STORE_KEY_ENUM.tenantDomain, tenantDomain);
    },
    SET_token: (state, token) => {
      state.token = token;
      setToken(token);
    },
    SET_homeConfigs: (state, homeConfigs) => {
      state.homeConfigs = homeConfigs;
      storage.set(STORE_KEY_ENUM.homeConfigs, homeConfigs);
    },
    SET_userMobileMenus: (state, userMobileMenus) => {
      state.userMobileMenus = userMobileMenus;
      storage.set(STORE_KEY_ENUM.userMobileMenus, userMobileMenus);
    },
    SET_userInfo: (state, userInfo) => {
      state.userInfo = userInfo;
      storage.set(STORE_KEY_ENUM.userInfo, userInfo);
    },
    SET_roleApp: (state, roleApp) => {
      state.roleApp = roleApp;
      storage.set(STORE_KEY_ENUM.roleApp, roleApp);
    },
    SET_sysBaseConfig: (state, sysBaseConfig) => {
      state.sysBaseConfig = sysBaseConfig;
      storage.set(STORE_KEY_ENUM.sysBaseConfig, sysBaseConfig);
    },
    SET_dictTypeTreeData: (state, dictTypeTreeData) => {
      state.dictTypeTreeData = dictTypeTreeData;
      storage.set(STORE_KEY_ENUM.dictTypeTreeData, dictTypeTreeData);
    },
    SET_refreshKey: (state, refreshKey) => {
      state.refreshKey = refreshKey;
      storage.set(STORE_KEY_ENUM.refreshKey, refreshKey);
    },
    SET_refreshFlag: (state, refreshFlag) => {
      state.refreshFlag = refreshFlag;
      storage.set(STORE_KEY_ENUM.refreshFlag, refreshFlag);
    },
    SET_refreshParam: (state, refreshParam) => {
      state.refreshParam = refreshParam;
      storage.set(STORE_KEY_ENUM.refreshParam, refreshParam);
    },
    SET_commonParam: (state, commonParam) => {
      state.commonParam = commonParam;
      storage.set(STORE_KEY_ENUM.commonParam, commonParam);
    },
    SET_menus: (state, menus) => {
      state.menus = menus;
    },
    SET_currentMenu: (state, currentMenu) => {
      state.currentMenu = currentMenu;
    },
    SET_cardInfo: (state, cardInfo) => {
      state.cardInfo = cardInfo;
    },
    // 清除缓存
    CLEAR_cache: (state) => {
      // 租户域清理
      // state.tenantDomain = ''
      // storage.remove(STORE_KEY_ENUM.tenantDomain)
      // token
      state.token = "";
      uni.removeStorageSync('token')
      // 移动端用户菜单
      state.userMobileMenus = {};
      storage.remove(STORE_KEY_ENUM.userMobileMenus);
      // 用户信息
      state.userInfo = {};
      storage.remove(STORE_KEY_ENUM.userInfo);
      // 用户角色应用信息
      state.roleApp = {};
      storage.remove(STORE_KEY_ENUM.roleApp);
      // 字典信息
      state.dictTypeTreeData = {};
      storage.remove(STORE_KEY_ENUM.dictTypeTreeData);
      // 刷新标识
      state.refreshKey = null;
      storage.remove(STORE_KEY_ENUM.refreshKey);
      // 刷新标识
      state.refreshFlag = false;
      storage.remove(STORE_KEY_ENUM.refreshFlag);
      // 刷新参数
      state.refreshParam = null;
      storage.remove(STORE_KEY_ENUM.refreshParam);
      // 通用参数
      state.commonParam = null;
      storage.remove(STORE_KEY_ENUM.commonParam);
      // 配置信息
      // state.sysBaseConfig = {}
      // storage.remove(STORE_KEY_ENUM.sysBaseConfig)
      // storage.clean()
    },
  },
  actions: {
    async getMenus({ commit, state }, data) {
      commit("SET_menus", data);
    },
    updateMenusActive({ commit, state }, data) {
      const menus = state.menus;
      const index = menus.findIndex((v) => v.path === data);
      if (index > -1) {
        commit(
          "SET_menus",
          state.menus.map((item, idx) => {
            if (idx === index) {
              item.isActive = true;
            } else {
              item.isActive = false;
            }
            return item;
          }),
        );
      }
    },
    resetMenusActive({ commit, state }) {
      commit("SET_menus", state.menus.map((item) => {
        item.isActive = false;
        return item;
      }));
    },
    // 登录获取token
    Login({ commit, state }, userInfo) {
      const paramData = {
        account: userInfo.account.trim(),
        // 密码进行SM2加密，传输过程中看到的只有密文，后端存储使用hash
        password: smCrypto.doSm2Encrypt(userInfo.password),
        validCode: userInfo.validCode,
        validCodeReqNo: userInfo.validCodeReqNo,
      };
      return new Promise((resolve, reject) => {
        loginApi
          .login(paramData)
          .then((data) => {
            // 缓存token
            commit("SET_token", data);
            resolve(data);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    // 获取用户信息
    GetUserInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        loginApi
          .getLoginUser()
          .then((data) => {
            // 缓存用户信息
            commit("SET_userInfo", data);
            // 如果返回数据中包含roleApp信息，则缓存
            if (data && data.roleApp) {
              commit("SET_roleApp", data.roleApp);
            }
            resolve(data);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    // 获取登錄用戶菜單
    GetUserLoginMenu({ commit, state }) {
      return new Promise((resolve, reject) => {
        userCenterApi
          .userLoginMobileMenu()
          .then((data) => {
            // 缓存移动端用户菜单
            commit("SET_userMobileMenus", data);
            resolve(data);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    // 获取数据字典
    GetDictTypeTreeData({ commit, state }) {
      return new Promise((resolve, reject) => {
        dictApi
          .dictTree()
          .then((data) => {
            if (data) {
              // 缓存字典
              commit("SET_dictTypeTreeData", data);
              resolve(data);
            }
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    // 获取系统基础配置
    GetSysBaseConfig({ commit, state }) {
      return new Promise((resolve, reject) => {
        let sysBaseConfig = {};
        configApi.configSysBaseList().then((data) => {
          if (data) {
            data.forEach((item) => {
              sysBaseConfig[item.configKey] = item.configValue;
            });
            // 缓存配置
            commit("SET_sysBaseConfig", sysBaseConfig);
          }
          resolve(sysBaseConfig);
        });
      }).catch((error) => {
        reject(error);
      });
    },
    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        loginApi
          .logout()
          .then(() => {
            // 将当前租户重置至主租户中
            // commit('SET_tenantDomain', state.allEnv[state.envKey].tenantDomain)
            // 清除缓存
            commit("CLEAR_cache");
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
  },
};
