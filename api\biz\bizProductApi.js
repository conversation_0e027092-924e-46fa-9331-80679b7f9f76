import request from '@/utils/request'

export default {
  /**
   * 分页获取产品列表
   * @param {Object} data - 查询参数
   * @param {number} data.page - 页码
   * @param {number} data.pageSize - 每页条数
   * @param {string} [data.categoryId] - 分类ID
   * @returns {Promise} - 返回接口响应
   */
  bizProductPage(data) {
    return request({
      url: '/biz/mini/product_page',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取产品详情
   * @param {Object} data - 查询参数
   * @param {string|number} data.id - 产品ID
   * @returns {Promise} - 返回接口响应
   */
  bizProductDetail(data) {
    return request({
      url: '/biz/mini/product_detail',
      method: 'GET',
      data: data
    })
  }
} 