# 下拉菜单全屏宽度更新说明

## 更新内容

将自定义下拉菜单组件的下拉内容修改为铺满整个屏幕宽度，提供更好的用户体验。

## 主要修改

### 1. 样式修改

**下拉内容容器 (.dropdown-content)**
```scss
.dropdown-content {
  position: fixed;        // 改为固定定位
  left: 0;               // 左边距为0
  right: 0;              // 右边距为0
  width: 100vw;          // 宽度为视窗宽度
  margin: 0;             // 清除边距
  padding: 0;            // 清除内边距
  background: #fff;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.12);
  transform-origin: center top;
  transition: all 0.3s ease;
  border-radius: 0;      // 移除圆角
}
```

**选项列表 (.dropdown-options)**
```scss
.dropdown-options {
  max-height: 400rpx;
  width: 100%;           // 确保宽度100%
  box-sizing: border-box; // 盒模型为border-box
}
```

**选项项目 (.dropdown-option)**
```scss
.dropdown-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  color: #323233;
  font-size: 28rpx;
  line-height: 40rpx;
  cursor: pointer;
  width: 100%;           // 确保宽度100%
  box-sizing: border-box; // 盒模型为border-box
  border-bottom: 1rpx solid #f0f0f0; // 添加分隔线
  
  &:last-child {
    border-bottom: none;  // 最后一项不显示分隔线
  }
}
```

### 2. JavaScript 逻辑修改

**添加位置计算**
```javascript
// 新增状态
const contentTop = ref(0)

// 新增位置计算函数
const calculatePosition = () => {
  const query = uni.createSelectorQuery()
  query.select('.dropdown-title').boundingClientRect((rect) => {
    if (rect) {
      if (direction.value === 'up') {
        contentTop.value = rect.top - 400 // 向上展开
      } else {
        contentTop.value = rect.bottom     // 向下展开
      }
    }
  }).exec()
}

// 在打开下拉时调用位置计算
const open = () => {
  if (props.disabled) return
  
  calculatePosition() // 计算位置
  showWrapper.value = true
  
  setTimeout(() => {
    showPopup.value = true
    menuToggle?.(childIndex.value, true)
    emit('open')
    
    setTimeout(() => {
      emit('opened')
    }, duration.value)
  }, 10)
}
```

**模板中添加动态样式**
```vue
<view 
  class="dropdown-content"
  :style="{ 
    zIndex: zIndex,
    animationDuration: duration + 'ms',
    top: contentTop + 'px'  // 动态设置顶部位置
  }"
>
```

## 效果对比

### 修改前
- 下拉内容宽度受父容器限制
- 可能在某些布局中显示不完整
- 视觉效果局限

### 修改后
- 下拉内容铺满整个屏幕宽度
- 提供更好的视觉体验
- 与主流应用的交互体验一致
- 选项内容显示更充分

## 兼容性

- ✅ 微信小程序
- ✅ 支付宝小程序  
- ✅ 百度小程序
- ✅ H5
- ✅ App

## 使用方式

使用方式保持不变，组件会自动计算位置并铺满屏幕宽度：

```vue
<custom-dropdown-menu>
  <custom-dropdown-item
    :value="selectedValue"
    :options="options"
    @change="onChange"
    title="选择选项"
  />
</custom-dropdown-menu>
```

## 注意事项

1. **层级管理**: 下拉内容使用固定定位，确保 z-index 设置合理
2. **位置计算**: 组件会自动计算下拉内容的显示位置
3. **性能优化**: 位置计算使用 uni.createSelectorQuery()，性能良好
4. **样式适配**: 下拉内容会自动适配不同屏幕尺寸

## 测试建议

建议在以下场景中测试：
- 不同屏幕尺寸的设备
- 页面滚动状态下的使用
- 多个下拉菜单同时存在的情况
- 向上和向下展开的效果

通过这次更新，下拉菜单的用户体验得到了显著提升，更符合现代移动应用的交互标准。
