import request from '@/utils/request'

/**
 * 文章相关接口
 */
export default {
  /**
   * 分页获取文章列表
   * @param {Object} data - 查询参数
   * @param {number} data.page - 页码
   * @param {number} data.pageSize - 每页条数
   * @param {string} [data.title] - 标题搜索
   * @param {string} [data.source] - 来源
   * @param {string} [data.status] - 状态
   * @returns {Promise} - 返回接口响应
   */
  bizArticlePage(data) {
    return request({
      url: '/biz/article/page',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取文章详情
   * @param {Object} data - 查询参数
   * @param {string|number} data.id - 文章ID
   * @returns {Promise} - 返回接口响应
   */
  bizArticleDetail(data) {
    return request({
      url: '/biz/article/detail',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取热门文章列表
   * @param {Object} data - 查询参数
   * @param {number} [data.limit=5] - 获取数量
   * @returns {Promise} - 返回接口响应
   */
  bizArticleHot(data) {
    return request({
      url: '/biz/article/hot',
      method: 'GET',
      data: data
    })
  }
} 