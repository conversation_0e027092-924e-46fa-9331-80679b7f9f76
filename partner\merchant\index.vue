<template>
  <snowy-layout title="商户管理" :isTabbar="false" :isFirstPage="false">
    <view class="merchant-page">
      <!-- Tab切换 -->
      <view v-if="$store.getters.isFirstPartner" class="tab-section">
        <view class="tab-container">
          <view class="tab-item" :class="{ active: activeTab === 'my' }" @click="switchTab('my')">
            <text class="tab-text">我的商户</text>
          </view>
          <view class="tab-item" :class="{ active: activeTab === 'partner' }" @click="switchTab('partner')">
            <text class="tab-text">合伙人商户</text>
          </view>
        </view>
      </view>

      <!-- 查询条件 - 仅在我的商户Tab显示 -->
      <view class="filter-section" v-if="activeTab === 'my'">
        <!-- 第一行筛选条件 -->
        <custom-dropdown-menu>
          <custom-dropdown-item title="商户名称" style="width: 30%;">
            <view class="search-bar">
              <view class="search-input">
                <uni-icons type="search" size="18" color="#999"></uni-icons>
                <input type="text" placeholder="请输入合伙人或激活码" v-model="filterData.name" @input="onSearchInput" />
              </view>
            </view>
          </custom-dropdown-item>
          <custom-dropdown-item :value="filterData.type" :options="merchantTypeOptions" @change="onMerchantTypeChange"
            title="商户类型" style="width: 25%;" />
          <custom-dropdown-item :value="filterData.status" :options="statusOptions" @change="onStatusChange" title="状态"
            style="width: 40%;" />
        </custom-dropdown-menu>

        <!-- 第二行筛选条件 -->
        <custom-dropdown-menu>
          <custom-dropdown-item :value="filterData.cpackageName" :options="packageNameOptions"
            @change="onPackageNameChange" title="套餐名称" style="width: 30%;" />
          <custom-dropdown-item title="入驻时间" :show-selected-value="false" style="width: 25%;">
            <view class="datetime-picker-container">
              <uni-datetime-picker v-model="joinTimeRange" type="daterange" rangeSeparator="至" placeholder="选择入驻时间范围"
                @change="onJoinTimeChange" />
            </view>
          </custom-dropdown-item>
          <custom-dropdown-item title="激活码到期日" :show-selected-value="false" style="width: 40%;">
            <view class="datetime-picker-container">
              <uni-datetime-picker v-model="expireTimeRange" type="datetimerange" rangeSeparator="至"
                placeholder="选择激活码到期时间范围" @change="onExpireTimeChange" />
            </view>
          </custom-dropdown-item>
        </custom-dropdown-menu>
      </view>



      <!-- 商户列表 -->
      <scroll-view class="merchant-list" scroll-y @scrolltolower="loadMore" :refresher-enabled="true"
        :refresher-triggered="isRefreshing" @refresherrefresh="handleRefresh">
        <!-- 我的商户列表 -->
        <view v-if="activeTab === 'my'">
          <view class="merchant-item" v-for="(item, index) in merchantList" :key="item.id || index">
            <view class="merchant-header">
              <view class="merchant-avatar">
                <text class="avatar-text">{{ getMerchantTypeText(item.type) }}</text>
              </view>
              <view class="merchant-info">
                <view class="merchant-name">{{ item.name || '' }}</view>
                <view class="merchant-code">{{ item.code || '' }}</view>
              </view>
              <view class="merchant-status">
                <view class="status-tag" :class="getStatusClass(item)">
                  {{ getStatusText(item) }}
                </view>
                <view class="expire-info">{{ getExpireInfo(item.cpackage.effectDay) }}</view>
              </view>
            </view>

            <view class="package-name">{{ item.cpackage.cpackageName || '' }}</view>

            <view class="merchant-details">
              <view class="detail-row">
                <text class="detail-label">联系人</text>
                <text class="detail-value">{{ item.contactName || '' }}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">联系电话</text>
                <text class="detail-value">{{ item.contactPhone || '' }}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">入驻时间</text>
                <text class="detail-value">{{ item.enterTime || '' }}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">激活码</text>
                <text class="detail-value">{{ item.actviCode || '' }}</text>
              </view>
              <view class="detail-row">
                <text class="detail-label">激活码到期日</text>
                <text class="detail-value">{{ item.cpackage.effectEndTime || '' }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 合伙人商户列表 -->
        <view v-if="activeTab === 'partner'" class="partner-merchant-item">
          <view v-for="(item, index) in partnerMerchantList" class="partner-merchant-header" :key="item.id">
            <view class="partner-name">{{ item.name }}</view>
            <view class="merchant-count">{{ item.merchantCount }}个</view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more" v-if="hasMore">
          <text class="load-text">{{ isLoading ? '加载中...' : '上拉加载更多' }}</text>
        </view>

        <!-- 没有更多数据 -->
        <view class="no-more" v-if="!hasMore && merchantList.length > 0 && activeTab === 'my'">
          <text class="no-more-text">没有更多数据了</text>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="!isLoading && merchantList.length === 0 && activeTab === 'my'">
          <text class="empty-text">暂无商户数据</text>
        </view>
      </scroll-view>
    </view>
  </snowy-layout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import CustomDropdownMenu from '@/components/custom-dropdown/custom-dropdown-menu.vue'
import CustomDropdownItem from '@/components/custom-dropdown/custom-dropdown-item.vue'
import bizInventoryApi from '@/api/biz/bizInventoryApi'
import bizMerchantApi from '@/api/biz/bizMerchantApi'
import { onLoad } from "@dcloudio/uni-app"

const store = useStore()

// 当前选中的Tab
const activeTab = ref('my')

// 列表数据
const merchantList = ref([])
const partnerMerchantList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const isLoading = ref(false)
const isRefreshing = ref(false)

// 查询条件
const filterData = ref({
  name: '',
  type: '',
  status: '',
  cpackageName: '',
  enterTimeStart: '',
  enterTimeEnd: '',
  cpEffectEndTimeStart: '',
  cpEffectEndTimeEnd: ''
})

// 自定义下拉菜单数据格式 (与Vant格式一致)
const merchantNameOptions = ref([
  { text: '全部', value: null },
  { text: '商贸公司', value: '商贸公司' },
  { text: '科技有限公司', value: '科技有限公司' },
  { text: '餐饮管理公司', value: '餐饮管理公司' },
  { text: '零售连锁店', value: '零售连锁店' }
])

const merchantTypeOptions = ref([
  { text: '全部', value: null },
  { text: '企业', value: 'COMPANY' },
  { text: '个人', value: 'PERSON' },
  { text: '个体户', value: 'PRIVATE' }
])

const statusOptions = ref([
  { text: '全部', value: null },
  { text: '录入中', value: 'ENTERING' },
  { text: '已提交', value: 'SUBMITTED' },
  { text: '正常', value: 'NORMAL' },
  { text: '已冻结', value: 'FROZEN' }
])

const packageNameOptions = ref([
  { text: '全部', value: null },
  { text: '基础版', value: '基础版' },
  { text: '标准版', value: '标准版' },
  { text: '专业版', value: '专业版' },
  { text: '企业版', value: '企业版' }
])



// uni-datetime-picker 数据
const joinTimeRange = ref('')
const expireTimeRange = ref('')

// 是否还有更多数据
const hasMore = computed(() => {
  if (activeTab.value === 'my') {
    return merchantList.value.length < total.value
  } else if (activeTab.value === 'partner') {
    return partnerMerchantList.value.length < total.value
  }
  return false
})

// 切换Tab
const switchTab = (tab) => {
  activeTab.value = tab
  currentPage.value = 1

  if (tab === 'my') {
    merchantList.value = []
    loadMerchantList()
  } else if (tab === 'partner') {
    partnerMerchantList.value = []
    loadPartnerMerchantList()
  }
}

// 获取状态样式类 - 基于套餐到期状态
const getStatusClass = (item) => {
  // 检查套餐是否到期 - effectDay是剩余天数
  if (item.cpackage && typeof item.cpackage.effectDay === 'number') {
    return item.cpackage.effectDay > 0 ? 'status-active' : 'status-expired'
  }
  return 'status-expired'
}

// 获取状态文本 - 基于套餐到期状态
const getStatusText = (item) => {
  // 检查套餐是否到期 - effectDay是剩余天数
  if (item.cpackage && typeof item.cpackage.effectDay === 'number') {
    return item.cpackage.effectDay > 0 ? '有效' : '已到期'
  }
  return '已到期'
}

// 获取商户类型文本
const getMerchantTypeText = (type) => {
  switch (type) {
    case 'COMPANY': return '企业'
    case 'PERSON': return '个人'
    case 'PRIVATE': return '个体户'
    default: return '企业'
  }
}

// 获取到期信息
const getExpireInfo = (expireTime) => {
  if (!expireTime) return ''
  // expireTime	剩余有效期天数(正数表示有效天数,负数表示过期天数)
  if (expireTime > 0) {
    return `剩${expireTime}天到期`
  } else if (expireTime < 0) {
    return `已过期${Math.abs(expireTime)}天`
  } else {
    return '已过期'
  }
}
const onSearchInput = () => {
  refreshList()
}
// 自定义下拉菜单事件处理 (与Vant事件格式一致)
const onMerchantNameChange = (value) => {
  console.log(value);

  filterData.value.name = value.value
  refreshList()
}

const onMerchantTypeChange = (value) => {
  filterData.value.type = value.value
  refreshList()
}

const onStatusChange = (value) => {
  filterData.value.status = value.value
  refreshList()
}

const onPackageNameChange = (value) => {
  filterData.value.cpackageName = value.value
  refreshList()
}

// uni-datetime-picker 事件处理
const onJoinTimeChange = (value) => {
  console.log('入驻时间选择:', value)
  if (Array.isArray(value) && value.length === 2) {
    filterData.value.enterTimeStart = value[0]
    filterData.value.enterTimeEnd = value[1]
  } else {
    filterData.value.enterTimeStart = ''
    filterData.value.enterTimeEnd = ''
  }
  refreshList()
}

const onExpireTimeChange = (value) => {
  console.log('激活码到期时间选择:', value)
  if (Array.isArray(value) && value.length === 2) {
    filterData.value.cpEffectEndTimeStart = value[0]
    filterData.value.cpEffectEndTimeEnd = value[1]
  } else {
    filterData.value.cpEffectEndTimeStart = ''
    filterData.value.cpEffectEndTimeEnd = ''
  }
  refreshList()
}



// 刷新列表
const refreshList = () => {
  currentPage.value = 1
  if (activeTab.value === 'my') {
    merchantList.value = []
    loadMerchantList()
  } else if (activeTab.value === 'partner') {
    partnerMerchantList.value = []
    loadPartnerMerchantList()
  }
}

// 加载商户列表
const loadMerchantList = async (isLoadMore = false) => {
  if (isLoading.value) return

  try {
    isLoading.value = true

    const params = {
      current: currentPage.value,
      size: pageSize.value,
      ...filterData.value,
      partnerId: store.getters.roleApp.entityId
    }
    if (!params.status) {
      delete params.status
    }
    // 调用商户列表接口
    const response = await bizMerchantApi.queryAuditPassMerchantList(params)

    if (response) {
      const { records, total: totalCount } = response

      if (isLoadMore) {
        merchantList.value = [...merchantList.value, ...records].map(v => {
          v.cpackage = v.cpackage || {}
          return v
        })
      } else {
        merchantList.value = records.map(v => {
          v.cpackage = v.cpackage || {}
          return v
        })
      }

      total.value = totalCount
    } else {
      uni.showToast({
        title: '加载失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('加载商户列表失败:', error)
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
  } finally {
    isLoading.value = false
    isRefreshing.value = false
  }
}

// 加载合伙人商户列表
const loadPartnerMerchantList = async (isLoadMore = false) => {
  if (isLoading.value) return

  try {
    isLoading.value = true

    const roleApp = store.getters.roleApp || {}
    const params = {
      current: currentPage.value,
      size: 30,
      firstPartnerId: roleApp.entityId
    }

    const response = await store.dispatch('partner/getSecondPartnerList', params)
    if (response.success) {
      const { list, total: totalCount } = response.data

      if (isLoadMore) {
        partnerMerchantList.value = [...partnerMerchantList.value, ...list]
      } else {
        partnerMerchantList.value = list
      }

      total.value = totalCount
    } else {
      uni.showToast({
        title: response.message || '加载失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('加载合伙人商户列表失败:', error)
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
  } finally {
    isLoading.value = false
    isRefreshing.value = false
  }
}

// 加载更多
const loadMore = () => {
  console.log('loadMore');

  if (!hasMore.value || isLoading.value) return

  currentPage.value++
  if (activeTab.value === 'my') {
    loadMerchantList(true)
  } else if (activeTab.value === 'partner') {
    loadPartnerMerchantList(true)
  }
}

// 下拉刷新
const handleRefresh = () => {
  isRefreshing.value = true
  currentPage.value = 1

  if (activeTab.value === 'my') {
    merchantList.value = []
    loadMerchantList()
  } else if (activeTab.value === 'partner') {
    partnerMerchantList.value = []
    loadPartnerMerchantList()
  }
}

// 加载套餐选项
const loadPackageOptions = async () => {
  try {
    const roleApp = store.getters.roleApp || {}
    const response = await bizInventoryApi.getPackageOptions({
      partnerId: roleApp.entityId
    })

    if (response) {
      const packages = response || []
      packageNameOptions.value = [
        { text: '全部', value: null },
        ...packages.map(pkg => ({
          text: pkg.cpackageName,
          value: pkg.cpackageName
        }))
      ]
    }
  } catch (error) {
    console.error('加载套餐选项失败:', error)
  }
}
onLoad((options)=>{
   // 如果有激活码到期日参数，设置筛选条件
  if (options.cpEffectEndTimeStart && options.cpEffectEndTimeEnd) {
    filterData.value.cpEffectEndTimeStart = options.cpEffectEndTimeStart
    filterData.value.cpEffectEndTimeEnd = options.cpEffectEndTimeEnd
    // 设置日期选择器的值
    const dateRange = [options.cpEffectEndTimeStart, options.cpEffectEndTimeEnd]
    expireTimeRange.value = dateRange
  }
})
// 页面加载
onMounted(() => {
  loadPackageOptions()
  if (activeTab.value === 'my') {
    loadMerchantList()
  } else if (activeTab.value === 'partner') {
    loadPartnerMerchantList()
  }
})
</script>

<style lang="scss" scoped>
.merchant-page {
  height: 100%;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.tab-section {
  background: white;
  padding: 0 30rpx;
}

.tab-container {
  display: flex;
  height: 100rpx;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  &.active {
    .tab-text {
      color: rgba(82, 150, 255, 1);
      font-weight: 600;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60rpx;
      height: 4rpx;
      background: rgba(82, 150, 255, 1);
      border-radius: 2rpx;
    }
  }
}

.tab-text {
  font-size: 32rpx;
  color: #666;
  transition: all 0.3s;
}

.filter-section {
  background: white;
  // border-top: 1rpx solid #f0f0f0;

  // 自定义下拉菜单之间的间距
  .custom-dropdown-menu {
    margin-bottom: 2rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// uni-datetime-picker 容器样式
.datetime-picker-container {
  padding: 40rpx;
  background: white;

  :deep(.uni-datetime-picker) {
    width: 100%;
  }

  :deep(.uni-datetime-picker__input) {
    border: 1rpx solid #e9ecef;
    border-radius: 8rpx;
    padding: 20rpx 24rpx;
    font-size: 28rpx;
    background: #f8f9fa;
  }
}

.merchant-list {
  flex: 1;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  overflow: auto;
}

.merchant-item {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.merchant-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.merchant-avatar {
  width: 80rpx;
  height: 80rpx;
  background: rgba(82, 150, 255, 1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}

.avatar-text {
  color: white;
  font-size: 24rpx;
  font-weight: 600;
}

.merchant-info {
  flex: 1;
}

.merchant-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.merchant-code {
  font-size: 28rpx;
  color: #666;
}

.merchant-status {
  text-align: right;
}

.status-tag {
  display: inline-block;
  padding: 8rpx 16rpx;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  font-size: 24rpx;
  margin-bottom: 10rpx;

  &.status-active {
    background: rgba(85, 152, 255, 1);
    color: rgba(255, 255, 255, 1);
  }

  &.status-expired {
    background: rgba(216, 30, 6, 1);
    color: rgba(255, 255, 255, 1);
  }
}

.expire-info {
  font-size: 24rpx;
  color: rgba(82, 150, 255, 1);
}

.package-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
}

.merchant-details {
  .detail-row {
    display: flex;
    align-items: center;
    padding: 15rpx 0;

    &:last-child {
      border-bottom: none;
    }
  }

  .detail-label {
    font-size: 28rpx;
    color: #666;
    width: 200rpx;
    flex-shrink: 0;
  }

  .detail-value {
    font-size: 28rpx;
    color: #333;
    flex: 1;
    text-align: right;
  }
}

.load-more {
  padding: 40rpx 0;
  text-align: center;
}

.load-text {
  font-size: 28rpx;
  color: #999;
}

.no-more {
  padding: 40rpx 0;
  text-align: center;
}

.no-more-text {
  font-size: 28rpx;
  color: #999;
}

.empty-state {
  padding: 200rpx 0;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
  display: block;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

// 合伙人商户列表样式
.partner-merchant-item {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
}

.partner-merchant-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.partner-name {
  font-size: 30rpx;
  font-weight: 400;
  color: #333;
}

.merchant-count {
  font-size: 30rpx;
  color: rgba(0, 0, 0, 0.60);
  padding: 8rpx 16rpx;
}

.partner-merchant-details {
  .detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .detail-label {
    font-size: 28rpx;
    color: #666;
    flex-shrink: 0;
    width: 160rpx;
  }

  .detail-value {
    font-size: 28rpx;
    color: #333;
    text-align: right;
    flex: 1;
  }
}

:deep(.uni-calendar--fixed) {
  bottom: auto !important;
}

.search-bar {
  display: flex;
  align-items: center;
  padding: 30rpx 30rpx;
  background: white;
  flex: 1;

  .search-input {
    flex: 1;
    display: flex;
    align-items: center;
    background: #f5f6fa;
    border-radius: 50rpx;
    padding: 20rpx 30rpx;

    input {
      flex: 1;
      margin-left: 20rpx;
      font-size: 28rpx;
      color: #333;

      &::placeholder {
        color: #999;
      }
    }
  }
}
</style>
