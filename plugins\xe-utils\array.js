'use strict'

import map from './map'
import some from './some'
import every from './every'
import includeArrays from './includeArrays'
import arrayEach from './arrayEach'
import lastArrayEach from './lastArrayEach'
import uniq from './uniq'
import union from './union'
import toArray from './toArray'
import sortBy from './sortBy'
import orderBy from './orderBy'
import shuffle from './shuffle'
import sample from './sample'
import slice from './slice'
import filter from './filter'
import findKey from './findKey'
import includes from './includes'
import find from './find'
import findLast from './findLast'
import reduce from './reduce'
import copyWithin from './copyWithin'
import chunk from './chunk'
import zip from './zip'
import unzip from './unzip'
import zipObject from './zipObject'
import flatten from './flatten'
import pluck from './pluck'
import invoke from './invoke'
import toArrayTree from './toArrayTree'
import toTreeArray from './toTreeArray'
import findTree from './findTree'
import eachTree from './eachTree'
import mapTree from './mapTree'
import filterTree from './filterTree'
import searchTree from './searchTree'
import arrayIndexOf from './arrayIndexOf'
import arrayLastIndexOf from './arrayLastIndexOf'

var arrayExports = {
  uniq: uniq,
  union: union,
  sortBy: sortBy,
  orderBy: orderBy,
  shuffle: shuffle,
  sample: sample,
  some: some,
  every: every,
  slice: slice,
  filter: filter,
  find: find,
  findLast: findLast,
  findKey: findKey,
  includes: includes,
  arrayIndexOf: arrayIndexOf,
  arrayLastIndexOf: arrayLastIndexOf,
  map: map,
  reduce: reduce,
  copyWithin: copyWithin,
  chunk: chunk,
  zip: zip,
  unzip: unzip,
  zipObject: zipObject,
  flatten: flatten,
  toArray: toArray,
  includeArrays: includeArrays,
  pluck: pluck,
  invoke: invoke,
  arrayEach: arrayEach,
  lastArrayEach: lastArrayEach,
  toArrayTree: toArrayTree,
  toTreeArray: toTreeArray,
  findTree: findTree,
  eachTree: eachTree,
  mapTree: mapTree,
  filterTree: filterTree,
  searchTree: searchTree
}

export default arrayExports
