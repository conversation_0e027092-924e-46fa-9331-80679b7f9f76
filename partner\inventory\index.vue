<template>
  <snowy-layout title="我的库存" :isTabbar="false" :isFirstPage="false">
    <view class="inventory-page">
      <!-- 顶部标签栏 -->
      <view class="tab-bar">
        <view class="tab-item" :class="{ active: activeTab === 'inStock' }" @click="switchTab('inStock')">
          在库
        </view>
        <!-- 已出库 - 只有一级合伙人才显示 -->
        <view v-if="$store.getters.isFirstPartner" class="tab-item" :class="{ active: activeTab === 'outStock' }"
          @click="switchTab('outStock')">
          已出库
        </view>
        <view class="tab-item" :class="{ active: activeTab === 'sold' }" @click="switchTab('sold')">
          已售出
        </view>
      </view>

      <!-- 搜索栏 -->
      <view class="search-bar">
        <view class="search-input">
          <uni-icons type="search" size="18" color="#999"></uni-icons>
          <input type="text" placeholder="请输入激活码进行查询" v-model="searchKeyword" @input="onSearchInput" />
        </view>
        <view class="search-btn" @click="handleSearch">
          {{ getSearchButtonText() }}
        </view>
      </view>

      <!-- 主要内容区域 -->
      <view class="content-area">
        <view v-for="(cpackage, index) in packageList" :key="index" class="package-group">
          <!-- 左侧筛选区域 -->
          <view class="filter-sidebar">
            <view class="package-header">
              <image class="package-icon"
                :src="$store.getters.allEnv[$store.getters.envKey].image_baseUrl + '/static/images/partner/cpackage.png'"
                mode="widthFix"></image>
              <text class="package-name">{{ cpackage.name }}</text>
            </view>
            <view v-for="(condition, condIndex) in cpackage.conditions" :key="condIndex" class="condition-item"
              @click="toggleCondition(index, condIndex)">
              <view class="checkbox" :class="{ checked: condition.checked }">
                <uni-icons v-if="condition.checked" type="checkmarkempty" size="12" color="#fff"></uni-icons>
              </view>
              <text class="condition-text">{{ condition.name }}</text>
              <text class="condition-count">({{ condition.num || 0 }}个)</text>
            </view>
          </view>
          <!-- 右侧列表区域 -->
          <view class="list-area">
            <scroll-view class="inventory-list" scroll-y="true" :refresher-enabled="false">
              <view v-for="(item, index) in cpackage.children" :key="index" class="inventory-item">
                <view class="item-content">
                  <view class="activation-code">{{ item.code }}</view>
                  <view class="validity">有效期：{{ item.validity }}</view>
                </view>
                <view class="item-action">
                  <view v-if="activeTab === 'inStock'" class="action-btn" @click="handleSell(item)">
                    售出
                  </view>
                  <view v-if="activeTab === 'sold' && item.status === 0" class="action-btn"
                    @click="withdrawCdkeys(item)">
                    收回
                  </view>
                  <text v-if="activeTab === 'sold' && item.status === 0" class="status-text-gray">未激活</text>
                  <text v-if="activeTab === 'sold' && item.status !== 0" class="status-text-green">已激活</text>
                </view>
              </view>

              <!-- 加载更多提示 -->
              <view v-if="loading" class="loading-tip">
                <uni-load-more :status="loadStatus"></uni-load-more>
              </view>
            </scroll-view>
          </view>
        </view>
        <!-- 空状态 -->
        <view v-if="packageList.length === 0" class="empty-state">
          <text class="empty-text">暂无数据</text>
        </view>
      </view>
    </view>
  </snowy-layout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useStore } from 'vuex'
import bizInventoryApi from '@/api/biz/bizInventoryApi'

const store = useStore()

// 当前选中的标签
const activeTab = ref('inStock')

// 搜索关键词
const searchKeyword = ref('')

// 刷新状态
const refreshing = ref(false)

// 加载状态
const loading = ref(false)
const loadStatus = ref('more')

// 当前页码
const currentPage = ref(1)
const pageSize = ref(999)

// 套餐列表数据
const packageList = ref([])

// 获取库存状态对应的数值
const getInventoryStatus = (tab) => {
  switch (tab) {
    case 'inStock': return 0  // 在库
    case 'outStock': return 1 // 出库
    case 'sold': return 2     // 售出
    default: return 0
  }
}

// 获取搜索按钮文本
const getSearchButtonText = () => {
  if (activeTab.value === 'outStock') {
    return '划拨记录'
  } else if (activeTab.value === 'inStock' && !store.getters.isFirstPartner) {
    return '回拨' // 二级合伙人在库时显示回拨
  } else {
    return '划拨'
  }
}

// 切换标签
const switchTab = (tab) => {
  // 检查权限：只有一级合伙人才能访问已出库标签
  if (tab === 'outStock' && !store.getters.isFirstPartner) {
    uni.showToast({
      title: '无权限访问',
      icon: 'none'
    })
    return
  }

  activeTab.value = tab
  currentPage.value = 1
  loadData()
}

// 切换筛选条件
const toggleCondition = (packageIndex, conditionIndex) => {
  const pkg = packageList.value[packageIndex]
  const condition = pkg.conditions[conditionIndex]

  // 切换选中状态
  condition.checked = !condition.checked

  // 更新当前套餐的children列表
  updatePackageChildren(packageIndex)
}

// 更新套餐的children列表
const updatePackageChildren = (packageIndex) => {
  const pkg = packageList.value[packageIndex]
  const selectedConditions = pkg.conditions.filter(condition => condition.checked)

  // 清空当前children
  pkg.children = []

  // 根据选中的条件添加对应的激活码
  selectedConditions.forEach(condition => {
    if (condition.children && condition.children.length > 0) {
      pkg.children = [...pkg.children, ...condition.children]
    }
  })
}

// 搜索输入
const onSearchInput = () => {
  // 实时搜索可以在这里处理
  loadData()
}

// 处理搜索
const handleSearch = () => {
  if (activeTab.value === 'outStock') {
    // 跳转到划拨记录页面
    uni.navigateTo({
      url: '/partner/transfer/record'
    })
  } else if (activeTab.value === 'inStock' && !store.getters.isFirstPartner) {
    // 二级合伙人在库时跳转到回拨页面
    uni.navigateTo({
      url: '/partner/transfer/index?type=return'
    })
  } else {
    // 跳转到划拨页面
    uni.navigateTo({
      url: '/partner/transfer/index'
    })
  }
}

// 处理售出
// 激活码售出
const handleSell = async (item) => {
  uni.showModal({
    title: '确认售出',
    content: '确认将此激活码标记为已售出？',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({
            title: '处理中...'
          })

          const response = await bizInventoryApi.sellCdkey({
            id: item.id
          })

          uni.showToast({
            title: '售出成功',
            icon: 'success'
          })
          // 从当前数组中删除该项
          removeItemFromList(item)
        } catch (error) {
          console.error('售出失败:', error)
          uni.showToast({
            title: '售出失败',
            icon: 'none'
          })
        } finally {
          uni.hideLoading()
        }
      }
    }
  })
}

// 调用收回接口
const withdrawCdkeys = async (item) => {
  try {
    const data = [
      {
        id: item.id
      }
    ]
    const response = await bizInventoryApi.withdrawCdkey(data)

    uni.showToast({
      title: '收回成功',
      icon: 'success'
    })
    // 从当前数组中删除该项
    removeItemFromList(item)
  } catch (error) {
    console.error('收回失败:', error)
    uni.showToast({
      title: '收回失败',
      icon: 'none'
    })
  }
}

// 从列表中删除指定项
const removeItemFromList = (targetItem) => {
  packageList.value.forEach(pkg => {
    // 从套餐的children中删除
    if (pkg.children && pkg.children.length > 0) {
      const index = pkg.children.findIndex(item => item.id === targetItem.id)
      if (index > -1) {
        pkg.children.splice(index, 1)
      }
    }

    // 从条件的children中删除
    pkg.conditions.forEach(condition => {
      if (condition.children && condition.children.length > 0) {
        const conditionIndex = condition.children.findIndex(item => item.id === targetItem.id)
        if (conditionIndex > -1) {
          condition.children.splice(conditionIndex, 1)
          // 更新条件的数量
          condition.num = condition.children.length
        }
      }
    })
  })


}

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true
  try {
    await loadData()
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('刷新失败:', error)
  } finally {
    refreshing.value = false
  }
}

// 加载更多
const onLoadMore = async () => {

}

// 加载数据
const loadData = async () => {
  try {
    loading.value = true
    loadStatus.value = 'loading'

    // 重置页码
    currentPage.value = 1

    const roleApp = store.getters.roleApp || {}
    const response = await bizInventoryApi.getCdkeyList({
      current: currentPage.value,
      size: pageSize.value,
      searchKey: searchKeyword.value,
      partnerId: roleApp.entityId,
      inventoryStatus: getInventoryStatus(activeTab.value)
    })

    if (response) {
      const list = response || []
      // 重置套餐列表
      packageList.value = list.map(v => {
        v.children = []
        v.conditions = v.conditions.map(condition => ({
          ...condition,
          checked: false
        }))
        return v
      })
    } else {
      uni.showToast({
        title: response.message || '加载失败',
        icon: 'none'
      })
    }

  } catch (error) {
    console.error('加载数据失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
    loadStatus.value = 'more'
  }
}

onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.inventory-page {
  height: 100%;
  background-color: #f5f6fa;
  display: flex;
  flex-direction: column;
}

.tab-bar {
  display: flex;
  background: white;
  // border-bottom: 1rpx solid #eee;

  .tab-item {
    flex: 1;
    text-align: center;
    padding: 30rpx 0;
    font-size: 28rpx;
    color: #666;
    position: relative;

    &.active {
      color: rgba(82, 150, 255, 1);
      font-weight: 600;

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60rpx;
        height: 4rpx;
        background: rgba(82, 150, 255, 1);
        border-radius: 2rpx;
      }
    }
  }
}

.search-bar {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  background: white;
  border-bottom: 1rpx solid #eee;

  .search-input {
    flex: 1;
    display: flex;
    align-items: center;
    background: #f5f6fa;
    border-radius: 50rpx;
    padding: 20rpx 30rpx;
    margin-right: 20rpx;

    input {
      flex: 1;
      margin-left: 20rpx;
      font-size: 28rpx;
      color: #333;

      &::placeholder {
        color: #999;
      }
    }
  }

  .search-btn {
    background: rgba(82, 150, 255, 1);
    color: white;
    padding: 20rpx 40rpx;
    border-radius: 50rpx;
    font-size: 28rpx;
  }
}

.content-area {
  flex: 1;
  padding: 20rpx 30rpx;
  overflow-y: auto;
}

.filter-sidebar {
  background: rgba(239, 247, 255, 1);
  padding-bottom: 25rpx;
}

.package-group {
  background: white;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  overflow: hidden;

  // 左侧筛选区域
  >view:first-child {
    width: 220rpx;
    border-right: 1rpx solid #f0f0f0;

    .package-header {
      display: flex;
      align-items: center;
      padding: 30rpx 20rpx 20rpx 20rpx;

      .package-icon {
        width: 35rpx;
      }

      .package-name {
        margin-left: 15rpx;
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
      }
    }


  }
}

.condition-item {
  display: flex;
  align-items: center;
  padding: 13rpx 20rpx;
  border-bottom: 1rpx solid #f5f5f5;

  .checkbox {
    width: 32rpx;
    height: 32rpx;
    border: 2rpx solid #ddd;
    border-radius: 4rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20rpx;

    &.checked {
      background: rgba(82, 150, 255, 1);
      border-color: rgba(82, 150, 255, 1);
    }
  }

  .condition-text {
    flex: 1;
    font-size: 26rpx;
    color: #333;
    white-space: nowrap;
  }

  .condition-count {
    font-size: 24rpx;
    color: #999;
    white-space: nowrap;
  }
}

.list-area {
  flex: 1;
  overflow: hidden;
  padding: 20rpx;

  .inventory-list {
    max-height: 240px;
    box-sizing: border-box;
    overflow: auto;

    .inventory-item {
      display: flex;
      align-items: center;
      padding: 15rpx 0;
      // margin-bottom: 20rpx;
      border-bottom: 1rpx solid #f5f5f5;

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }

      .item-content {
        flex: 1;

        .activation-code {
          font-size: 26rpx;
          color: #333;
          font-weight: 500;
          margin-bottom: 15rpx;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .validity {
          font-size: 26rpx;
          color: #666;
        }
      }

      .item-action {
        display: flex;
        flex-direction: column;
        align-items: center;

        .action-btn {
          background: rgba(82, 150, 255, 1);
          color: white;
          padding: 15rpx 30rpx;
          border-radius: 8rpx;
          font-size: 26rpx;
          white-space: nowrap;
        }

        .status-text-gray {
          color: rgba(216, 30, 6, 1);
          font-size: 24rpx;
          margin-top: 6rpx;
        }

        .status-text-green {
          color: rgba(82, 150, 255, 1);
          font-size: 24rpx;
        }
      }
    }

    .loading-tip {
      padding: 30rpx;
      text-align: center;
    }
  }
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}
</style>
