'use strict'

import noop from './noop'
import property from './property'
import bind from './bind'
import once from './once'
import after from './after'
import before from './before'
import throttle from './throttle'
import debounce from './debounce'
import delay from './delay'

var functionExports = {
  noop: noop,
  property: property,
  bind: bind,
  once: once,
  after: after,
  before: before,
  throttle: throttle,
  debounce: debounce,
  delay: delay
}

export default functionExports
