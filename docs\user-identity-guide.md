# 用户身份管理系统使用指南

## 概述

本系统提供了完整的用户身份管理功能，支持根据登录用户的 `roleApp` 对象中的 `entityType` 和 `entitySubType` 字段来判断用户身份，并进行相应的权限控制。

## 身份类型说明

### entityType（实体类型）
- `MER`: 商户管理员
- `PARTNER`: 合伙人
- `STAFF`: 员工
- `REFERER`: 推荐官

### entitySubType（实体子类型）
- `GROUP`: 集团
- `FIRST`: 一级代理商
- `SECOND`: 二级代理商

### 组合身份
- **集团商户**: `entityType: 'MER'` + `entitySubType: 'GROUP'`
- **商户管理员**: `entityType: 'MER'` + `entitySubType: null`
- **一级合伙人**: `entityType: 'PARTNER'` + `entitySubType: 'FIRST'`
- **二级合伙人**: `entityType: 'PARTNER'` + `entitySubType: 'SECOND'`

## 使用方法

### 1. 在 Vuex Store 中使用

```javascript
import { useStore } from 'vuex'

const store = useStore()

// 获取身份信息
const entityType = store.getters.entityType
const entitySubType = store.getters.entitySubType

// 身份判断
const isMerchantAdmin = store.getters.isMerchantAdmin
const isPartner = store.getters.isPartner
const isFirstPartner = store.getters.isFirstPartner
```

### 2. 使用工具类

```javascript
import userIdentity from '@/utils/user-identity'

// 获取身份信息
const identityText = userIdentity.getIdentityText() // 返回：'一级合伙人'
const entityType = userIdentity.getEntityType() // 返回：'PARTNER'
const entitySubType = userIdentity.getEntitySubType() // 返回：'FIRST'

// 身份判断
const isPartner = userIdentity.isPartner() // 返回：true/false
const isFirstPartner = userIdentity.isFirstPartner() // 返回：true/false

// 权限级别
const permissionLevel = userIdentity.getPermissionLevel() // 返回：0-5
const hasHighPermission = userIdentity.hasPermissionLevel(3) // 返回：true/false
```

### 3. 在 Vue 组件中使用

```vue
<template>
  <view>
    <!-- 条件渲染 -->
    <view v-if="isMerchantAdmin">商户管理员专属内容</view>
    <view v-if="isPartner">合伙人专属内容</view>
    <view v-if="isFirstPartner || isGroupMerchant">高级权限内容</view>
    
    <!-- 显示身份信息 -->
    <text>当前身份：{{ identityText }}</text>
    <text>权限级别：{{ permissionLevel }}</text>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import userIdentity from '@/utils/user-identity'

const identityText = computed(() => userIdentity.getIdentityText())
const permissionLevel = computed(() => userIdentity.getPermissionLevel())
const isMerchantAdmin = computed(() => userIdentity.isMerchantAdmin())
const isPartner = computed(() => userIdentity.isPartner())
const isFirstPartner = computed(() => userIdentity.isFirstPartner())
const isGroupMerchant = computed(() => userIdentity.isGroupMerchant())
</script>
```

## API 参考

### Store Getters

| Getter | 返回类型 | 说明 |
|--------|----------|------|
| `roleApp` | Object | 完整的 roleApp 对象 |
| `entityType` | String | 实体类型 |
| `entitySubType` | String | 实体子类型 |
| `isMerchantAdmin` | Boolean | 是否为商户管理员 |
| `isPartner` | Boolean | 是否为合伙人 |
| `isStaff` | Boolean | 是否为员工 |
| `isReferer` | Boolean | 是否为推荐官 |
| `isGroupMerchant` | Boolean | 是否为集团商户 |
| `isFirstAgent` | Boolean | 是否为一级代理商 |
| `isSecondAgent` | Boolean | 是否为二级代理商 |
| `isFirstPartner` | Boolean | 是否为一级合伙人 |
| `isSecondPartner` | Boolean | 是否为二级合伙人 |

### 工具类方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `getRoleApp()` | - | Object\|null | 获取 roleApp 对象 |
| `getEntityType()` | - | String\|null | 获取实体类型 |
| `getEntitySubType()` | - | String\|null | 获取实体子类型 |
| `getIdentityText()` | - | String | 获取身份描述文本 |
| `getPermissionLevel()` | - | Number | 获取权限级别（0-5） |
| `hasIdentity(entityType, entitySubType?)` | String, String? | Boolean | 检查是否具有指定身份 |
| `hasAnyIdentity(identities)` | Array | Boolean | 检查是否具有任意指定身份 |
| `hasPermissionLevel(level)` | Number | Boolean | 检查是否具有指定权限级别 |

## 权限级别说明

| 级别 | 身份 | 说明 |
|------|------|------|
| 5 | 集团商户 | 最高权限 |
| 4 | 商户管理员 | 商户管理权限 |
| 3 | 一级合伙人 | 高级合伙人权限 |
| 2 | 二级合伙人 | 普通合伙人权限 |
| 1 | 员工/推荐官 | 基础权限 |
| 0 | 未知身份 | 无权限 |

## 最佳实践

1. **统一使用工具类**: 推荐使用 `userIdentity` 工具类而不是直接访问 store，这样可以确保一致性和类型安全。

2. **权限级别控制**: 对于需要层级权限控制的功能，使用 `hasPermissionLevel()` 方法。

3. **组合条件判断**: 对于复杂的权限判断，使用 `hasAnyIdentity()` 方法。

4. **响应式更新**: 在 Vue 组件中使用 `computed` 属性确保身份信息的响应式更新。

## 示例代码

完整的使用示例请参考 `examples/user-identity-usage.vue` 文件。
