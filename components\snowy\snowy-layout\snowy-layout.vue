<!--
 * @Descripttion: 
 * @version: 
 * @Author: zhengyangyang
 * @Date: 2025-02-18 15:00:57
 * @LastEditors: zhengyangyang
 * @LastEditTime: 2025-02-24 13:56:26
-->
<template>
  <view class="layout">
    <snowy-navBar :title="title" :isFirstPage="isFirstPage"></snowy-navBar>
    <view class="layout-content" :style="{ paddingTop: statusBarHeight + navBarHeight + 'px' }">
      <slot></slot>
    </view>
    <snowy-tabbar v-if="tabbar"></snowy-tabbar>
  </view>
</template>

<script setup>
import { reactive, ref, computed, watch } from 'vue'
import store from '@/store'
const systemInfo = uni.getSystemInfoSync()
const statusBarHeight = ref(systemInfo.statusBarHeight || 0)
const navBarHeight = ref(44)
const props = defineProps({
  isTabbar: {
    type: <PERSON>olean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  isFirstPage: {
    type: Boolean,
    default: false
  }
})
const tabbar = computed(() => {
  return props.isTabbar
})
const menus = computed(() => {
  return store.getters.menus
})
watch(
  () => props.isTabbar,
  (newVal) => {
    console.log(88)
  }
)
</script>

<style scoped lang="scss">
.layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .layout-content {
    flex: 1;
    overflow: auto;
  }
}
</style>
