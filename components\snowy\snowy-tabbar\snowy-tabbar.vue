<template>
  <view
    class="snowy-tabbar"
    :style="{
      height: height + 'px'
    }"
  >
    <view class="snowy-tabbar-box">
      <view class="snowy-tabbar-item" v-for="(item, index) in menus" :key="index" @tap="handleTo(item, index)">
        <image class="snowy-tabbar-item-image" :src="item.isActive ? item.activeIcon : item.icon"></image>
        <view
          :style="{
            color: item.isActive ? activeColor : inactiveColor,
            fontSize: '12px',
            marginTop: '4px'
          }"
          >{{ item.text }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import store from '@/store'
import bizMiniApi from '@/api/biz/bizMiniApi'
const props = defineProps({
  height: {
    type: Number,
    default: 72
  }
})
const activeColor = ref('#7d7e80')
const inactiveColor = ref('#7d7e80')
const menus = computed(() => {
  return store.getters.menus
})
const handleTo = async (item, index) => {
  if(!item.isTabbar){
    uni.$snowy.tab.navigateTo(item.path)
    return
  }
  store.dispatch('resetMenusActive')
  item.isActive = true
  if (index === 0) {
    uni.switchTab({
      url: item.path,
      success: () => {
        store.commit('SET_currentMenu', item)
      }
    })
  } else {
    // 检查本地存储是否有手机号
    const isAuthPhone = uni.getStorageSync('isAuthPhone')
    if (isAuthPhone != 1) {
      // 如果没有手机号，跳转到认证页面
      uni.$snowy.tab.reLaunch('/pages/auth/index')
      return false // 阻止默认分享行为
    }
    const page = getCurrentPages()
    const currentPage = page[page.length - 1]
    const currentPath = currentPage.__route__ || currentPage.route
    console.log(currentPath, currentPage)
    uni.switchTab({
      url: item.path,
      success: () => {
        store.commit('SET_currentMenu', item)
      }
    })
  }
}
</script>

<style scoped lang="scss">
.snowy-tabbar {
  background: #ffffff;
  box-shadow: 0rpx -1rpx 0rpx 0rpx rgba(0, 0, 0, 0.1);
}
.snowy-tabbar-box {
  height: 100%;
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  padding: 10px 30rpx;
  box-sizing: border-box;
}
.snowy-tabbar-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
}
.snowy-tabbar-item-image {
  width: 48rpx;
  height: 48rpx;
}
</style>
