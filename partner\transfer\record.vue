<template>
  <snowy-layout title="划拨记录" :isTabbar="false" :isFirstPage="false">
    <view class="transfer-record-page">
      <!-- 筛选条件 -->
      <view class="filter-section">
        <!-- 筛选条件 -->
        <custom-dropdown-menu>
          <!-- 搜索栏 -->
          <view class="search-bar">
            <view class="search-input">
              <uni-icons type="search" size="18" color="#999"></uni-icons>
              <input type="text" placeholder="请输入合伙人或激活码" v-model="searchKeyword" @input="onSearchInput" />
            </view>
          </view>
          <custom-dropdown-item title="划拨日期" :show-selected-value="false" style="width: 32%;">
            <view class="datetime-picker-container">
              <uni-datetime-picker v-model="transferDateRange" type="daterange" rangeSeparator="至"
                placeholder="选择划拨日期范围" @change="onTransferDateChange" />
            </view>
          </custom-dropdown-item>
        </custom-dropdown-menu>
        <custom-dropdown-menu>
          <custom-dropdown-item :value="filterData.type" :options="typeOptions" @change="onTypeChange" title="操作类型"
            style="width: 30%;" />
        </custom-dropdown-menu>
      </view>

      <!-- 记录列表 -->
      <scroll-view class="record-list" scroll-y @scrolltolower="onScrollToLower" :refresher-enabled="true"
        :refresher-triggered="isRefreshing" @refresherrefresh="onRefresh">
        <view class="record-item" v-for="(item, index) in recordList" :key="item.id || index">
          <!-- 合伙人信息 -->
          <view class="partner-info">
            <view class="partner-name">{{ item.targetPartnerName || '未知合伙人' }}</view>
            <view class="partner-phone">{{ item.createTime || '--' }}</view>
          </view>

          <!-- 操作标签 -->
          <view class="action-btn" :class="getTypeClass(item.type)">
            {{ getTypeText(item.type) }}
          </view>

          <!-- 详细信息 -->
          <view class="record-details">
            <view class="detail-row">
              <text class="label">操作时间</text>
              <text class="value">{{ item.createTime || '--' }}</text>
            </view>
            <view class="detail-row">
              <text class="label">数量</text>
              <text class="value">{{ item.number || 0 }}个</text>
            </view>
            <view class="detail-row">
              <text class="label">激活码</text>
              <text class="value multi-line">{{ item.cdkey || '--' }}</text>
            </view>
          </view>
        </view>

        <!-- 加载状态 -->
        <view class="load-status">
          <view class="loading" v-if="isLoading">
            <text>加载中...</text>
          </view>
          <view class="no-more" v-else-if="!hasMore && recordList.length > 0">
            <text>没有更多数据了</text>
          </view>
          <view class="empty" v-else-if="!hasMore && recordList.length === 0">
            <text>暂无数据</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </snowy-layout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useStore } from 'vuex'
import CustomDropdownMenu from '@/components/custom-dropdown/custom-dropdown-menu.vue'
import CustomDropdownItem from '@/components/custom-dropdown/custom-dropdown-item.vue'
import bizInventoryApi from '@/api/biz/bizInventoryApi'

const store = useStore()

// 筛选条件
const filterData = ref({
  type: '', // 操作类型：0-划拨，1-回拨
  createTimeStart: '',
  createTimeEnd: ''
})
const searchKeyword = ref('')
// 日期范围
const transferDateRange = ref('')

// 筛选选项
const typeOptions = ref([
  { text: '全部', value: '' },
  { text: '划拨', value: 0 },
  { text: '回拨', value: 1 }
])

// 记录列表
const recordList = ref([])

// 分页相关
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const hasMore = ref(true)
const isLoading = ref(false)
const isRefreshing = ref(false)

// 筛选条件变化处理
const onTypeChange = (value) => {
  filterData.value.type = value.value
  loadData()
}

const onTransferDateChange = (value) => {
  transferDateRange.value = value
  if (Array.isArray(value) && value.length === 2) {
    filterData.value.createTimeStart = value[0]
    filterData.value.createTimeEnd = value[1]
  } else {
    filterData.value.createTimeStart = ''
    filterData.value.createTimeEnd = ''
  }
  loadData()
}
const onSearchInput = () => {
  loadData()
}

// 获取操作类型文本
const getTypeText = (type) => {
  return type === 0 ? '划拨' : '回拨'
}

// 获取操作类型样式
const getTypeClass = (type) => {
  return type === 0 ? 'transfer' : 'return'
}

// 滚动到底部加载更多
const onScrollToLower = () => {
  if (!isLoading.value && hasMore.value) {
    currentPage.value++
    loadData(true)
  }
}

// 下拉刷新
const onRefresh = () => {
  isRefreshing.value = true
  currentPage.value = 1
  loadData(false).finally(() => {
    isRefreshing.value = false
  })
}

// 加载数据
const loadData = async (isLoadMore = false) => {
  if (isLoading.value) return

  try {
    isLoading.value = true

    if (!isLoadMore) {
      currentPage.value = 1
    }

    const params = {
      current: currentPage.value,
      size: pageSize.value,
      createTimeStart: filterData.value.createTimeStart,
      createTimeEnd: filterData.value.createTimeEnd,
      searchKey: searchKeyword.value
    }

    // 只有当type不为空时才传递
    if (filterData.value.type !== '') {
      params.type = filterData.value.type
    }

    const response = await bizInventoryApi.getTransferLog(params)

    const { records, total: totalCount } = response

    if (isLoadMore) {
      recordList.value = [...recordList.value, ...(records || [])]
    } else {
      recordList.value = records || []
    }

    total.value = totalCount
    hasMore.value = recordList.value.length < totalCount
  } catch (error) {
    console.error('加载数据失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.transfer-record-page {
  height: 100%;
  background-color: #f5f6fa;
  display: flex;
  flex-direction: column;
}

.filter-section {
  background: white;
  border-bottom: 1rpx solid #f0f0f0;

  .custom-dropdown-menu {
    margin-bottom: 2rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .datetime-picker-container {
    padding: 20rpx;
  }
}

.record-list {
  flex: 1;
  height: 100%;
  padding: 20rpx;
  box-sizing: border-box;
  overflow: auto;
}

.record-item {
  background: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  position: relative;

  .partner-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30rpx;
    padding: 30rpx;
    border-bottom: 1px solid rgba(238, 238, 238, 1);

    .partner-name {
      font-size: 32rpx;
      color: #333;
      font-weight: 600;
      flex: 1;
    }

    .partner-phone {
      font-size: 28rpx;
      color: #999;
      margin-left: 20rpx;
    }
  }

  .action-btn {
    position: absolute;
    top: 120rpx;
    left: 30rpx;
    padding: 8rpx 20rpx;
    border-radius: 8rpx;
    font-size: 24rpx;
    color: white;

    &.transfer {
      background: rgba(82, 150, 255, 1);
    }

    &.return {
      background: #ff4757;
    }
  }

  .record-details {
    margin-top: 50rpx;
    padding: 30rpx;

    .detail-row {
      display: flex;
      margin-bottom: 20rpx;
      align-items: flex-start;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        width: 150rpx;
        font-size: 28rpx;
        color: #666;
        flex-shrink: 0;
      }

      .value {
        flex: 1;
        font-size: 28rpx;
        color: #333;
        text-align: right;

        &.multi-line {
          word-break: break-all;
          line-height: 1.6;
          text-align: right;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}

.load-status {
  text-align: center;
  padding: 30rpx;
  color: #999;
  font-size: 28rpx;

  .loading,
  .no-more,
  .empty {
    padding: 20rpx 0;
  }

  .loading {
    color: rgba(82, 150, 255, 1);
  }

  .empty {
    color: #ccc;
    font-size: 32rpx;
    padding: 100rpx 0;
  }
}

:deep(.uni-calendar--fixed) {
  bottom: auto !important;
}
.search-bar {
  display: flex;
  align-items: center;
  padding: 0 0 0 30rpx;
  background: white;
  flex: 1;
  .search-input {
    flex: 1;
    display: flex;
    align-items: center;
    background: #f5f6fa;
    border-radius: 50rpx;
    padding: 20rpx 30rpx;

    input {
      flex: 1;
      margin-left: 20rpx;
      font-size: 28rpx;
      color: #333;

      &::placeholder {
        color: #999;
      }
    }
  }
}
</style>
