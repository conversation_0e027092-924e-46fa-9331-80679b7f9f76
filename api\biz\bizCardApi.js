import request from '@/utils/request'

export default {
  /**
   * 获取名片信息
   * @returns {Promise} - 返回名片相关信息
   */
  getOwner() {
    return request({
      url: '/biz/template/card/getOwner',
      method: 'GET'
    })
  },
  
  /**
   * 更新名片信息
   * @param {Object} data - 名片信息
   * @returns {Promise} - 返回更新结果
   */
  updateCard(data) {
    return request({
      url: '/biz/template/card/update',
      method: 'POST',
      data: data
    })
  }
} 