<template>
  <snowy-layout title="线索详情" :isTabbar="false" :isFirstPage="false">
    <view class="clue-detail-container">
      <!-- 用户基本信息区域 -->
      <view class="user-info-card">
        <view class="user-header">
          <view class="avatar-name">
            <image class="avatar" :src="clueInfo.avatar || '/static/images/default-avatar.png'" mode="aspectFill">
            </image>
            <view class="name-info">
              <view class="name">{{ clueInfo.taskName }}</view>
              <view class="clue-user">
                <view class="phone" style="margin-right: 36rpx;">{{ clueInfo.customer }}</view>
                <view class="phone">{{ clueInfo.customerPhone }}</view>
              </view>
            </view>
          </view>
          <view class="status" :class="getStatusClass(clueInfo.status)">{{ getStatusText(clueInfo.status) }}</view>
        </view>

        <!-- 详细信息 -->
        <view class="info-list">
          <view class="info-item">
            <text class="info-label">性别</text>
            <text class="info-value">{{ getGenderText(clueInfo.customerSex) }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">地址</text>
            <text class="info-value">{{ clueInfo.addr || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">兴趣产品</text>
            <text class="info-value">{{ clueInfo.interestedProduct || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">创建时间</text>
            <text class="info-value">{{ clueInfo.createTime || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">推荐官姓名</text>
            <text class="info-value">{{ clueInfo.referralName || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">推荐官手机号码</text>
            <text class="info-value">{{ clueInfo.referralPhone || '--' }}</text>
          </view>
        </view>

        <!-- 负责人信息 -->
        <view class="manager-info">
          <view class="manager-avatar">
            <image src="/static/images/default-avatar.png" mode="aspectFill"></image>
          </view>
          <text class="manager-name">{{ clueInfo.staffName || '--' }}</text>

          <!-- 操作按钮 -->
          <view class="action-buttons">
            <view class="action-btn new-task-btn" @click="action.newTask">新增工单</view>
            <view v-if="$store.getters.isStaffMERAdmin" class="action-btn new-follow-btn" @click="action.newAssign">新增分佣</view>
          </view>
        </view>
      </view>

      <!-- 标签页 -->
      <view class="tabs">
        <view class="tab-item" v-for="(tab, index) in tabs" :key="index" :class="{ active: currentTab === index }"
          @click="switchTab(index)">
          {{ tab.name }}
        </view>
      </view>

      <!-- 标签页内容 -->
      <scroll-view class="tab-content" scroll-y="true" @scrolltolower="onScrollToLower" refresher-enabled
        :refresher-triggered="refreshing" @refresherrefresh="onRefresh">
        <!-- 线索跟进 -->
        <view v-if="currentTab === 0" class="follow-list">
          <view class="follow-item" v-for="(item, index) in followList" :key="index">
            <image class="follow-dot" src="/static/images/my/time-jiedian.png" mode="aspectFit"></image>
            <view class="follow-line" v-if="index !== followList.length - 1"></view>
            <view class="follow-content">
              <view class="follow-header">
                <text class="follow-operator">{{ item.operator }}</text>
                <text class="follow-time">{{ item.time }}</text>
              </view>
              <view class="follow-desc">{{ item.desc }}</view>
            </view>
          </view>
        </view>

        <!-- 工单管理 -->
        <view v-if="currentTab === 1" class="task-list">
          <view class="task-item" v-for="(item, index) in taskList" :key="index">
            <view class="task-tag" v-if="item.tag">{{ item.tag }}</view>
            <view class="task-info">
              <view class="task-row">
                <text class="task-label">工单名称</text>
                <text class="task-value">{{ item.name }}</text>
              </view>
              <view class="task-row">
                <text class="task-label">工单编号</text>
                <text class="task-value">{{ item.code }}</text>
              </view>
              <view class="task-row">
                <text class="task-label">接单人员</text>
                <text class="task-value">{{ item.receiver }}</text>
              </view>
            </view>
            <view class="task-buttons">
              <view class="task-btn task-btn-edit" @click="editTask(item.id)">编辑</view>
              <view class="task-btn task-btn-view" @click="viewTask(item.id)">查看</view>
            </view>
          </view>
          <view class="empty-tip" v-if="taskList.length === 0">暂无工单数据</view>
          <view class="loading-more" v-if="taskLoading">加载中...</view>
          <!-- <view class="no-more" v-if="taskNoMore">没有更多数据了</view> -->
        </view>

        <!-- 分佣管理 -->
        <view v-if="currentTab === 2" class="profit-order-list">
          <view v-if="profitOrderLoading" class="loading-tip">加载中...</view>
          <view v-else-if="profitOrderList.length === 0" class="empty-tip">暂无分佣数据</view>
          <view v-else>
            <view v-for="item in profitOrderList" :key="item.id" class="profit-order-item">
              <!-- 日期和状态 -->
              <view class="profit-order-header">
                <text class="profit-order-date">{{ formatDate(item.createTime) }}</text>
                <view class="profit-order-status" :class="getAuditStatusClass(item.auditStatus)">
                  {{ getAuditStatusText(item.auditStatus) }}
                </view>
              </view>

              <!-- 详细信息 -->
              <view class="profit-order-content">
                <view class="profit-order-row">
                  <text class="profit-order-label">产品名称</text>
                  <text class="profit-order-value">{{ item.productName || '--' }}</text>
                </view>
                <view class="profit-order-row">
                  <text class="profit-order-label">交易金额</text>
                  <text class="profit-order-value">{{ item.tradeAmount || '0.00' }}</text>
                </view>
                <view class="profit-order-row">
                  <text class="profit-order-label">佣金比例</text>
                  <text class="profit-order-value">{{ item.returnRate || '0' }}%</text>
                </view>
                <view class="profit-order-row">
                  <text class="profit-order-label">佣金金额(元)</text>
                  <text class="profit-order-value">{{ item.returnAmount || '0.00' }}</text>
                </view>
                <view class="profit-order-row">
                  <text class="profit-order-label">备注</text>
                  <text class="profit-order-value">{{ item.remark || '不知道' }}</text>
                </view>
              </view>

              <!-- 操作按钮 -->
              <view class="profit-order-actions">
                <view class="action-btn edit-btn" @click="editProfitOrder(item)">编辑</view>
                <view v-if="item.auditStatus === 1 || item.auditStatus === 3 || item.auditStatus === 6"
                      class="action-btn reason-btn" @click="viewRejectReason(item)">查看退回原因</view>
                <view class="action-btn terminate-btn" @click="terminateProfitOrder(item)">终止</view>
              </view>
            </view>
          </view>

          <!-- 加载状态 -->
          <view class="loading-more" v-if="profitOrderLoading">加载中...</view>
          <view class="no-more" v-if="profitOrderNoMore && profitOrderList.length > 0">没有更多数据了</view>
        </view>
      </scroll-view>

      <!-- 底部按钮区域 -->
      <view class="bottom-buttons" v-if="shouldShowButtons">
        <!-- 待接单状态：显示接单和废弃按钮 -->
        <template v-if="clueInfo.status === 0">
          <view class="bottom-btn accept-btn" @click="handleAccept">接单</view>
          <view class="bottom-btn reject-btn" @click="handleAbandon">废弃</view>
        </template>

        <!-- 已接单状态：显示跟进和废弃按钮 -->
        <template v-if="clueInfo.status === 1">
          <view class="bottom-btn follow-btn" @click="handleFollow">跟进</view>
          <view class="bottom-btn reject-btn" @click="handleAbandon">废弃</view>
        </template>

        <!-- 跟进中状态：显示废弃按钮 -->
        <template v-if="clueInfo.status === 2">
          <view class="bottom-btn reject-btn single-btn" @click="handleAbandon">废弃</view>
        </template>

        <!-- 已失效状态：显示重新接单按钮 -->
        <template v-if="clueInfo.status === -1">
          <view class="bottom-btn accept-btn single-btn" @click="handleReAccept">重新接单</view>
        </template>
      </view>
    </view>

    <!-- 退回原因弹框 -->
    <reject-reason-modal
      :visible="rejectReasonModalVisible"
      :reject-time="currentRejectTime"
      :reject-reason="currentRejectReason"
      @close="closeRejectReasonModal"
    />

    <!-- 终止确认弹框 -->
    <terminate-confirm-modal
      :visible="terminateModalVisible"
      @close="closeTerminateModal"
      @confirm="confirmTerminate"
    />
  </snowy-layout>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { onLoad,onShow } from '@dcloudio/uni-app'
import bizInventoryApi from '@/api/biz/bizInventoryApi'
import bizProfitOrderApi from '@/api/biz/bizProfitOrderApi'
import RejectReasonModal from '@/components/custom-modal/reject-reason-modal.vue'
import TerminateConfirmModal from '@/components/custom-modal/terminate-confirm-modal.vue'

const clueId = ref('')
const clueInfo = ref({
  customer: '',
  customerPhone: '',
  customerSex: 0,
  interestedProduct: '',
  addr: '',
  status: 0,
  referralId: '',
  taskName: '',
  referralName: '',
  referralPhone: '',
  staffName: '',
  createTime: ''
})

// 标签页
const tabs = reactive([
  { name: '线索跟进', key: 'follow' },
  { name: '工单管理', key: 'task' },
  { name: '分佣管理', key: 'assign' }
])
const currentTab = ref(0)

// 计算是否显示底部按钮
const shouldShowButtons = computed(() => {
  const status = clueInfo.value.status
  // 显示按钮的状态：0-待接单、1-已接单、2-跟进中、-1-已失效
  return [0, 1, 2, -1].includes(status)
})

// 跟进记录
const followList = ref([])
const followLoading = ref(false)

// 工单列表
const taskList = ref([])
const taskPage = ref(1)
const taskLoading = ref(false)
const taskNoMore = ref(false)

// 分佣订单列表
const profitOrderList = ref([])
const profitOrderLoading = ref(false)
const profitOrderPage = ref(1)
const profitOrderNoMore = ref(false)

// 弹框状态
const rejectReasonModalVisible = ref(false)
const currentRejectTime = ref('')
const currentRejectReason = ref('')
const terminateModalVisible = ref(false)
const currentTerminateItem = ref(null)

// 新增工单
const addTask = () => {
  uni.navigateTo({
    url: '/pages/my-clue/task-edit?clueId=' + clueId.value
  })
}

// 编辑工单
const editTask = (taskId) => {
  uni.navigateTo({
    url: `/pages/my-clue/task-edit?clueId=${clueId.value}&taskId=${taskId}`
  })
}

// 查看工单
const viewTask = (taskId) => {
  uni.navigateTo({
    url: `/pages/my-clue/task-edit?taskId=${taskId}&preview=1`
  })
}

// 获取跟进记录
const loadFollowList = async () => {
  if (followLoading.value) return

  followLoading.value = true
  try {
    const params = {
      current: 1,
      size: 9999,
      taskClueId: clueId.value
    }

    const response = await bizInventoryApi.getTaskClueFollowupList(params)

    if (response) {
      // 处理跟进记录数据格式
      followList.value = (response || []).map(item => ({
        type: item.type === 0 ? 'system' : 'user',
        operator: item.staffName || '系统',
        time: item.createTime || '--',
        desc: item.desc || '--'
      }))
    } else {
      uni.showToast({
        title: '获取跟进记录失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取跟进记录失败：', error)
    uni.showToast({
      title: '获取跟进记录失败',
      icon: 'none'
    })
  } finally {
    followLoading.value = false
  }
}

// 加载工单列表
const loadTaskList = async (isRefresh = false) => {
  if (taskLoading.value || taskNoMore.value) return

  taskLoading.value = true
  try {
    const params = {
      current: taskPage.value,
      size: 20,
      taskClueId: clueId.value
    }

    const response = await bizInventoryApi.getTicketList(params)

    if (response) {
      const { records, total } = response

      if (isRefresh || taskPage.value === 1) {
        taskList.value = records || []
      } else {
        taskList.value = [...taskList.value, ...(records || [])]
      }

      // 处理工单数据格式
      taskList.value = taskList.value.map(item => ({
        id: item.id,
        name: item.name || '--',
        code: item.code || '--',
        receiver: item.staffName || '--',
        tag: getStageText(item.stage)
      }))

      taskNoMore.value = taskList.value.length >= total
      if (!isRefresh) {
        taskPage.value++
      }
    } else {
      uni.showToast({
        title: '获取工单列表失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取工单列表失败：', error)
    uni.showToast({
      title: '获取工单列表失败',
      icon: 'none'
    })
  } finally {
    taskLoading.value = false
  }
}

// 获取工单阶段文本
const getStageText = (stage) => {
  const stageMap = {
    0: '交易意向',
    1: '交易中',
    2: '交易完成',
    3: '放弃交易'
  }
  return stageMap[stage] || '--'
}

// 加载分佣订单列表
const loadProfitOrderList = async (isRefresh = false) => {
  if (profitOrderLoading.value || (!isRefresh && profitOrderNoMore.value)) return

  profitOrderLoading.value = true

  try {
    // 如果是刷新，重置页码
    if (isRefresh) {
      profitOrderPage.value = 1
      profitOrderNoMore.value = false
    }

    const response = await bizProfitOrderApi.profitOrderList4TaskClue({
      taskClueId: clueId.value,
      current: profitOrderPage.value,
      size: 10
    })

    if (response) {
      const data = response || {}
      const records = data.records || []

      if (isRefresh) {
        profitOrderList.value = records
      } else {
        profitOrderList.value = [...profitOrderList.value, ...records]
      }

      // 判断是否还有更多数据
      const total = data.total || 0
      const currentSize = profitOrderList.value.length
      profitOrderNoMore.value = currentSize >= total

      // 如果还有数据，页码+1
      if (!profitOrderNoMore.value) {
        profitOrderPage.value++
      }
    } else {
      console.error('获取分佣订单列表失败：', response)
      uni.showToast({
        title: '获取分佣订单列表失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取分佣订单列表失败：', error)
    uni.showToast({
      title: '获取分佣订单列表失败',
      icon: 'none'
    })
  } finally {
    profitOrderLoading.value = false
  }
}

// 获取审核状态文本
const getAuditStatusText = (status) => {
  const statusMap = {
    0: '待财务审核',
    1: '财务审核退回',
    2: '待出纳审核',
    3: '出纳审核退回',
    4: '审核通过',
    5: '终止',
    6: '出纳审核退回商户'
  }
  return statusMap[status] || '--'
}

// 获取审核状态样式
const getAuditStatusClass = (status) => {
  const statusMap = {
    0: 'pending',
    1: 'rejected',
    2: 'pending',
    3: 'rejected',
    4: 'approved',
    5: 'terminated',
    6: 'rejected'
  }
  return statusMap[status] || 'pending'
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '--'
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 编辑分佣订单
const editProfitOrder = (item) => {
  uni.navigateTo({
    url: `/pages/my-clue/profit-order-add?id=${item.id}&clueId=${clueId.value}`
  })
}

// 查看退回原因
const viewRejectReason = (item) => {
  if(!item.newAuditLog){
    uni.showToast({
      title: '暂无退回原因',
      icon: 'none'
    })
    return
  }
  currentRejectTime.value = item.newAuditLog.rejectTime || item.newAuditLog.updateTime || item.newAuditLog.createTime
  currentRejectReason.value = item.newAuditLog.description || '暂无退回原因'
  rejectReasonModalVisible.value = true
}

// 关闭退回原因弹框
const closeRejectReasonModal = () => {
  rejectReasonModalVisible.value = false
  currentRejectTime.value = ''
  currentRejectReason.value = ''
}

// 终止分佣订单
const terminateProfitOrder = (item) => {
  currentTerminateItem.value = item
  terminateModalVisible.value = true
}

// 关闭终止确认弹框
const closeTerminateModal = () => {
  terminateModalVisible.value = false
  currentTerminateItem.value = null
}

// 确认终止
const confirmTerminate = async () => {
  if (!currentTerminateItem.value) return

  try {
    uni.showLoading({
      title: '处理中...'
    })

    const response = await bizProfitOrderApi.profitOrderStop({
      id: currentTerminateItem.value.id
    })

    uni.hideLoading()

    if (response && response.code === 200) {
      uni.showToast({
        title: '终止成功',
        icon: 'success'
      })
      // 刷新列表
      loadProfitOrderList(true)
    } else {
      uni.showToast({
        title: response?.message || '终止失败',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('终止分佣订单失败：', error)
    uni.showToast({
      title: '终止失败，请重试',
      icon: 'none'
    })
  }
}

// 切换标签页
const switchTab = (index) => {
  currentTab.value = index

  // 根据标签页加载对应数据
  if (index === 1 && taskList.value.length === 0) {
    // 工单管理
    loadTaskList()
  } else if (index === 2 && profitOrderList.value.length === 0) {
    // 分佣管理
    loadProfitOrderList(true)
  }
  // 跟进记录在页面初始化时已加载，不需要重复加载
}

// 监听页面滚动到底部，加载更多数据
const onScrollToLower = () => {
  if (currentTab.value === 1) {
    // 加载更多工单数据
    loadTaskList()
  } else if (currentTab.value === 2) {
    // 加载更多分佣订单数据
    loadProfitOrderList()
  }
  // 跟进记录不需要分页，不处理
}

// 获取线索状态样式
const getStatusClass = (status) => {
  const statusMap = {
    0: 'status-pending',
    1: 'status-accepted',
    2: 'status-progress',
    3: 'status-completed',
    [-1]: 'status-failed',
    [-2]: 'status-failed'
  }
  return statusMap[status] || ''
}

// 获取状态文本
const getStatusText = (status) => {
  const statusTextMap = {
    0: '待接单',
    1: '已接单',
    2: '跟进中',
    3: '已完结',
    [-1]: '已失效',
    [-2]: '已废弃'
  }
  return statusTextMap[status] || '未知状态'
}

// 获取性别文本
const getGenderText = (gender) => {
  const genderMap = {
    0: '男',
    1: '女'
  }
  return genderMap[gender] || '--'
}

// 获取线索详情
const getClueDetail = async () => {
  try {
    const response = await bizInventoryApi.getTaskClueDetail({ id: clueId.value })

    if (response) {
      clueInfo.value = response || {}
    } else {
      uni.showToast({
        title: '获取详情失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取线索详情失败：', error)
    uni.showToast({
      title: '获取详情失败',
      icon: 'none'
    })
  }
}

// 更新线索状态的通用函数
const updateClueStatus = async (newStatus, successMessage) => {
  try {
    const response = await bizInventoryApi.updateTaskClueStatus({
      id: clueId.value,
      status: newStatus
    })

    uni.showToast({
      title: successMessage,
      icon: 'success'
    })

    // 更新本地状态
    clueInfo.value.status = newStatus

    // 刷新跟进记录
    loadFollowList()
  } catch (error) {
    console.error('更新状态失败：', error)
    uni.showToast({
      title: '操作失败',
      icon: 'none'
    })
  }
}

// 接单操作
const handleAccept = () => {
  uni.showModal({
    title: '确认',
    content: '确定要接单吗？',
    success: (res) => {
      if (res.confirm) {
        updateClueStatus(1, '接单成功')
      }
    }
  })
}

// 跟进操作
const handleFollow = () => {
  uni.showModal({
    title: '确认',
    content: '确定要开始跟进吗？',
    success: (res) => {
      if (res.confirm) {
        updateClueStatus(2, '已开始跟进')
      }
    }
  })
}

// 废弃操作
const handleAbandon = () => {
  uni.showModal({
    title: '确认',
    content: '确定要废弃此线索吗？',
    success: (res) => {
      if (res.confirm) {
        updateClueStatus(-2, '已废弃')
      }
    }
  })
}

// 重新接单操作
const handleReAccept = () => {
  uni.showModal({
    title: '确认',
    content: '确定要重新接单吗？',
    success: (res) => {
      if (res.confirm) {
        updateClueStatus(1, '重新接单成功')
      }
    }
  })
}

// 操作按钮
const action = {
  // 新增工单
  newTask: () => {
    addTask()
  },

  // 新增分佣
  newAssign: () => {
    uni.navigateTo({
      url: `/pages/my-clue/profit-order-add?clueId=${clueId.value}`
    })
  }
}

// 在script中添加刷新相关状态
const refreshing = ref(false)

// 刷新处理函数
const onRefresh = () => {
  refreshing.value = true

  // 根据当前标签页执行不同的刷新逻辑
  if (currentTab.value === 0) {
    // 刷新线索跟进
    loadFollowList().finally(() => {
      refreshing.value = false
    })
  } else if (currentTab.value === 1) {
    // 刷新工单列表
    taskPage.value = 1
    taskList.value = []
    taskNoMore.value = false
    loadTaskList(true).finally(() => {
      refreshing.value = false
    })
  } else if (currentTab.value === 2) {
    // 刷新分佣管理
    loadProfitOrderList(true).finally(() => {
      refreshing.value = false
    })
  }
}

onLoad((option) => {
  if (option.id) {
    clueId.value = option.id
    getClueDetail()
    // 默认加载跟进记录
    loadFollowList()
  }
})
onShow(() => {
  taskPage.value = 1
  taskList.value = []
  taskNoMore.value = false
  loadTaskList(true)
})
</script>

<style lang="scss" scoped>
.clue-detail-container {
  height: 100%;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  // padding-bottom: 120rpx;
}

// 用户信息卡片
.user-info-card {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.user-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.avatar-name {
  display: flex;
  align-items: center;
}

.avatar {
  width: 90rpx;
  height: 90rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.name-info {
  display: flex;
  flex-direction: column;
}

.name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.phone {
  font-size: 28rpx;
  color: #666;
}

.clue-user {
  display: flex;
  align-items: center;
  margin-top: 14rpx;
}

.status {
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  font-size: 26rpx;

  &.status-pending {
    color: #e53935;
    background-color: rgba(229, 57, 53, 0.1);
  }

  &.status-accepted {
    color: #ff9800;
    background-color: rgba(255, 152, 0, 0.1);
  }

  &.status-progress {
    color: #2196f3;
    background-color: rgba(33, 150, 243, 0.1);
  }

  &.status-completed {
    color: #4caf50;
    background-color: rgba(76, 175, 80, 0.1);
  }

  &.status-failed {
    color: #9e9e9e;
    background-color: rgba(158, 158, 158, 0.1);
  }
}

// 详细信息列表
.info-list {
  margin-bottom: 30rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  line-height: 1.5;
}

.info-label {
  color: #333;
  font-weight: 400;
}

.info-value {
  color: #666;
  text-align: right;
}

// 管理员信息
.manager-info {
  display: flex;
  align-items: center;
  padding: 20rpx 0 0 0;
  border-top: 1px solid #f0f0f0;
  margin-bottom: 0;
}

.manager-avatar {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10rpx;

  image {
    width: 100%;
    height: 100%;
  }
}

.manager-name {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

// 操作按钮
.action-buttons {
  display: flex;
}

.action-btn {
  margin-left: 20rpx;
  height: 66rpx;
  line-height: 66rpx;
  padding: 0 30rpx;
  border-radius: 40rpx;
  font-size: 26rpx;
  text-align: center;
}

.new-task-btn,
.new-follow-btn {
  color: #333;
  background-color: #ffcb00;
  font-weight: 500;
}

// 标签页
.tabs {
  display: flex;
  background-color: transparent;
  position: sticky;
  top: 0;
  z-index: 10;
  padding: 0 20rpx;
  border-bottom: 1px solid #f0f0f0;
}

.tab-item {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  position: relative;

  &.active {
    color: #ffcb00;
    font-weight: bold;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 40rpx;
      height: 4rpx;
      background-color: #ffcb00;
    }
  }
}

// 标签页内容
.tab-content {
  flex: 1;
  padding: 28rpx;
  margin-bottom: 20rpx;
  // height: calc(100% - 300rpx);
  /* 适当调整高度，减去顶部内容和底部按钮的高度 */
  box-sizing: border-box;
  overflow: auto;
}

// 线索跟进列表
.follow-list {
  position: relative;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-radius: 12rpx 12rpx 12rpx 12rpx;
}

.follow-item {
  position: relative;
  padding-left: 40rpx;
  margin-bottom: 40rpx;
}

.follow-dot {
  position: absolute;
  left: 0;
  top: 2rpx;
  width: 30rpx;
  height: 30rpx;
  // background-color: #ffcb00;
  border-radius: 50%;
  z-index: 2;

  &.system-dot {
    // background-color: #ffcb00;
  }
}

.follow-line {
  position: absolute;
  left: 7rpx;
  top: 26rpx;
  width: 0;
  height: calc(100% + 14rpx);
  z-index: 1;
  border-left: 2rpx dashed #ffcb00;
}

.follow-content {
  padding-bottom: 10rpx;
}

.follow-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.follow-operator {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.follow-time {
  font-size: 24rpx;
  color: #999;
}

.follow-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

// 空数据提示
.empty-tip {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

// 底部按钮区域
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.bottom-btn {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 32rpx;
  border-radius: 45rpx;
  font-weight: 500;
  margin: 0 10rpx;
}

.accept-btn {
  background-color: #ffcb00;
  color: #333;
}

.follow-btn {
  background-color: #2196f3;
  color: #fff;
}

.reject-btn {
  background-color: #e53935;
  color: #fff;
}

.single-btn {
  margin: 0 auto;
}

// 添加工单列表样式
.task-list {
  padding: 10rpx 0;
}

.task-item {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 30rpx 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.task-tag {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background-color: rgba(255, 203, 0, 0.1);
  color: #ffcb00;
  font-size: 26rpx;
  padding: 8rpx 20rpx;
  border-radius: 6rpx;
  z-index: 2;
  font-weight: 500;
}

.task-info {
  padding-bottom: 30rpx;
  margin-top: 60rpx;
}

.task-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  line-height: 1.6;
}

.task-row:last-child {
  margin-bottom: 0;
}

.task-label {
  color: #333;
  font-weight: 400;
  width: 140rpx;
}

.task-value {
  color: #666;
  text-align: right;
  flex: 1;
}

.task-buttons {
  display: flex;
  justify-content: flex-end;
  border-top: 1rpx solid #f0f0f0;
  padding-top: 30rpx;
}

.task-btn {
  width: 140rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border-radius: 30rpx;
  font-size: 28rpx;
  margin-left: 20rpx;
}

.task-btn-edit {
  background-color: #ffcb00;
  color: #333;
  font-weight: 500;
}

.task-btn-view {
  background-color: #f5f5f5;
  color: #666;
}

.loading-more,
.no-more {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 20rpx 0;
}

// 分佣订单列表样式
.profit-order-list {
}

.profit-order-item {
  background-color: #fff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.profit-order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.profit-order-date {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.profit-order-status {
  padding: 8rpx 0rpx 8rpx 16rpx;
  font-size: 28rpx;
  color: #fff;

  &.pending {
    color: #ff9500;
  }

  &.rejected {
    color: #ff4757;
  }

  &.approved {
    color: #2ed573;
  }

  &.terminated {
    color: #747d8c;
  }
}

.profit-order-content {
  margin-bottom: 30rpx;
}

.profit-order-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.profit-order-label {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.60);
  flex-shrink: 0;
}

.profit-order-value {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.60);
  text-align: right;
  flex: 1;
  margin-left: 20rpx;
}

.profit-order-actions {
  display: flex;
  gap: 20rpx;
  justify-content: flex-end;
}

.action-btn {
  font-size: 24rpx;
  text-align: center;
  min-width: 80rpx;

  &.edit-btn {
    background-color: rgba(243, 243, 243, 1);
    color: #666;
  }

  &.reason-btn {
    background-color: rgba(255, 209, 0, 1);
    color: #333;
  }

  &.terminate-btn {
    background-color: rgba(216, 30, 6, 1);
    color: #fff;
  }
}

.loading-tip,
.empty-tip {
  text-align: center;
  font-size: 28rpx;
  color: #999;
  padding: 60rpx 0;
}
</style>