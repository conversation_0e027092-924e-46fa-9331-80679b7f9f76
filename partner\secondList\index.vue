<template>
  <snowy-layout title="我的合伙人" :isTabbar="false" :isFirstPage="false">
    <view class="second-list-page">
      <!-- 搜索栏 -->
      <view class="search-section">
        <view class="search-box">
          <uni-icons class="search-icon" type="search" size="18" color="#999"></uni-icons>
          <input
            class="search-input"
            v-model="searchKeyword"
            placeholder="可输入姓名、证件号码、手机号进行查询"
            @input="handleSearch"
          />
        </view>
      </view>

      <!-- 合伙人列表 -->
      <scroll-view 
        class="partner-list" 
        scroll-y 
        @scrolltolower="loadMore"
        :refresher-enabled="true"
        :refresher-triggered="isRefreshing"
        @refresherrefresh="handleRefresh"
      >
        <view class="partner-item" v-for="(item, index) in partnerList" :key="item.id || index">
          <view class="partner-header">
            <view class="avatar-container">
              <image class="avatar" :src="item.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
            </view>
            <view class="partner-name">{{ item.name || '我是合伙人姓名' }}</view>
          </view>
          
          <view class="partner-info">
            <view class="info-row">
              <text class="info-label">证件号码</text>
              <text class="info-value">{{ item.idNum || '--' }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">手机号</text>
              <text class="info-value">{{ item.contactPhone || '--' }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">备注</text>
              <text class="info-value">{{ item.remark || '--' }}</text>
            </view>
            <view class="info-row">
              <text class="info-label">名下激活码数量</text>
              <text class="info-value">{{ item.cdkeyCount || '0' }}个</text>
            </view>
            <view class="info-row">
              <text class="info-label">名下商户数量</text>
              <text class="info-value">{{ item.merchantCount || '0' }}个</text>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view class="load-more" v-if="hasMore">
          <text class="load-text">{{ isLoading ? '加载中...' : '上拉加载更多' }}</text>
        </view>
        
        <!-- 没有更多数据 -->
        <view class="no-more" v-if="!hasMore && partnerList.length > 0">
          <text class="no-more-text">没有更多数据了</text>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="!isLoading && partnerList.length === 0">
          <text class="empty-icon">📋</text>
          <text class="empty-text">暂无合伙人数据</text>
        </view>
      </scroll-view>
    </view>
  </snowy-layout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'

const store = useStore()

// 搜索关键词
const searchKeyword = ref('')

// 列表数据
const partnerList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const isLoading = ref(false)
const isRefreshing = ref(false)

// 默认头像
const defaultAvatar = computed(() => {
  return store.getters.allEnv[store.getters.envKey].image_baseUrl + '/static/images/partner/default-avatar.png'
})

// 是否还有更多数据
const hasMore = computed(() => {
  return partnerList.value.length < total.value
})

// 搜索防抖
let searchTimer = null
const handleSearch = () => {
  clearTimeout(searchTimer)
  searchTimer = setTimeout(() => {
    currentPage.value = 1
    partnerList.value = []
    loadPartnerList()
  }, 500)
}

// 加载合伙人列表
const loadPartnerList = async (isLoadMore = false) => {
  if (isLoading.value) return

  try {
    isLoading.value = true
    const roleApp = store.getters.roleApp || {}
    const params = {
      current: currentPage.value,
      size: pageSize.value,
      keyword: searchKeyword.value.trim(),
      firstPartnerId: roleApp.entityId
    }
    
    const response = await store.dispatch('partner/getSecondPartnerList', params)
    
    if (response.success) {
      const { list, total: totalCount } = response.data
      
      if (isLoadMore) {
        partnerList.value = [...partnerList.value, ...list]
      } else {
        partnerList.value = list
      }
      
      total.value = totalCount
    } else {
      uni.showToast({
        title: response.message || '加载失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('加载合伙人列表失败:', error)
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
  } finally {
    isLoading.value = false
    isRefreshing.value = false
  }
}

// 加载更多
const loadMore = () => {
  if (!hasMore.value || isLoading.value) return
  
  currentPage.value++
  loadPartnerList(true)
}

// 下拉刷新
const handleRefresh = () => {
  isRefreshing.value = true
  currentPage.value = 1
  partnerList.value = []
  loadPartnerList()
}

// 页面加载
onMounted(() => {
  loadPartnerList()
})
</script>

<style lang="scss" scoped>
.second-list-page {
  height: 100%;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.search-section {
  padding: 30rpx;
  background: white;
  border-bottom: 1rpx solid #f0f0f0;
}

.search-box {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 50rpx;
  padding: 0 30rpx;
  height: 80rpx;
}

.search-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  opacity: 0.6;
  color: #999;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  
  &::placeholder {
    color: #999;
  }
}

.partner-list {
  flex: 1;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  overflow: auto;
}

.partner-item {
  background: white;
  border-radius: 20rpx;
  padding: 28rpx 18rpx 20rpx 18rpx;
  margin-bottom: 20rpx;
  // box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.partner-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.avatar-container {
  margin-right: 30rpx;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
}

.partner-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.partner-info {
  .info-row {
    display: flex;
    align-items: center;
    padding: 15rpx 0;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  .info-label {
    font-size: 28rpx;
    color: #666;
    width: 200rpx;
    flex-shrink: 0;
  }
  
  .info-value {
    font-size: 28rpx;
    color: #333;
    flex: 1;
    text-align: right;
  }
}

.load-more {
  padding: 40rpx 0;
  text-align: center;
}

.load-text {
  font-size: 28rpx;
  color: #999;
}

.no-more {
  padding: 40rpx 0;
  text-align: center;
}

.no-more-text {
  font-size: 28rpx;
  color: #999;
}

.empty-state {
  padding: 200rpx 0;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
  display: block;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}
</style>
