<template>
  <common-modal
    :visible="visible"
    title="审批意见"
    @close="handleClose"
    @confirm="handleConfirm"
  >
    <view class="content-row">
      <text class="content-label">退回时间</text>
      <text class="content-value">{{ formatDateTime(rejectTime) }}</text>
    </view>

    <view class="content-row reason-row">
      <text class="content-label">退回原因</text>
      <view class="reason-content">
        <text class="reason-text">{{ rejectReason || '暂无退回原因' }}</text>
      </view>
    </view>
  </common-modal>
</template>

<script setup>
import { ref, watch } from 'vue'
import CommonModal from './common-modal.vue'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  rejectTime: {
    type: String,
    default: ''
  },
  rejectReason: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['close', 'confirm'])

// 格式化日期时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return '--'
  const date = new Date(dateStr)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 关闭弹框
const handleClose = () => {
  emit('close')
}

// 确认
const handleConfirm = () => {
  emit('confirm')
  handleClose()
}
</script>

<style lang="scss" scoped>
.content-row {
  margin-bottom: 40rpx;

  &:last-child {
    margin-bottom: 0;
  }

  &.reason-row {
    align-items: flex-start;
  }
}

.content-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

.content-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
}

.reason-content {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 24rpx;
  min-height: 120rpx;
}

.reason-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  word-break: break-all;
}
</style>
