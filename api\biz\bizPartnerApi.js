import request from '@/utils/request'

export default {
  /**
   * 获取合伙人列表
   * @param {Object} params - 请求参数
   * @param {number} [params.page] - 页码
   * @param {number} [params.size] - 每页数量
   * @param {string} [params.keyword] - 搜索关键词
   * @param {string} [params.status] - 合伙人状态
   * @returns {Promise} - 返回合伙人列表数据
   */
  getPartnerList(params) {
    return request({
      url: '/partner/list',
      method: 'GET',
      params: params
    })
  },

  /**
   * 划拨激活码给合伙人
   * @param {Object} data - 请求参数
   * @param {string} data.partnerId - 合伙人ID
   * @param {Array} data.codes - 激活码列表
   * @param {number} data.count - 激活码数量
   * @param {string} [data.remark] - 备注
   * @returns {Promise} - 返回划拨结果
   */
  transferCodes(data) {
    return request({
      url: '/partner/transfer',
      method: 'POST',
      data: data
    })
  },

  /**
   * 获取合伙人详情
   * @param {Object} params - 请求参数
   * @param {string} params.id - 合伙人ID
   * @returns {Promise} - 返回合伙人详情
   */
  getPartnerDetail(params) {
    return request({
      url: '/partner/detail',
      method: 'GET',
      data: params
    })
  },

  /**
   * 创建合伙人
   * @param {Object} data - 请求参数
   * @param {string} data.name - 合伙人姓名
   * @param {string} data.phone - 手机号
   * @param {string} [data.email] - 邮箱
   * @param {string} [data.address] - 地址
   * @returns {Promise} - 返回创建结果
   */
  createPartner(data) {
    return request({
      url: '/partner/create',
      method: 'POST',
      data: data
    })
  },

  /**
   * 更新合伙人信息
   * @param {Object} data - 请求参数
   * @param {string} data.id - 合伙人ID
   * @param {string} [data.name] - 合伙人姓名
   * @param {string} [data.phone] - 手机号
   * @param {string} [data.email] - 邮箱
   * @param {string} [data.address] - 地址
   * @returns {Promise} - 返回更新结果
   */
  updatePartner(data) {
    return request({
      url: '/partner/update',
      method: 'PUT',
      data: data
    })
  },

  /**
   * 删除合伙人
   * @param {Object} data - 请求参数
   * @param {string} data.id - 合伙人ID
   * @returns {Promise} - 返回删除结果
   */
  deletePartner(data) {
    return request({
      url: '/partner/delete',
      method: 'DELETE',
      data: data
    })
  }
}
