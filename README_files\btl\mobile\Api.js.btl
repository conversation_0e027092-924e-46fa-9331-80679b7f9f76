import request from '@/utils/request'

export default {
    // 获取${functionName}分页
    ${classNameFirstLower}Page(data) {
        return request({
            url: '/${moduleName}/${busName}/page',
            method: 'GET',
            data: data
        })
    },
    // 提交${functionName}表单 add为false时为编辑，默认为新增
    ${classNameFirstLower}SubmitForm(data, add = true) {
        return request({
            url: '/${moduleName}/${busName}/'+ (add ? 'add' : 'edit'),
            method: 'POST',
            data: data
        })
    },
    // 删除${functionName}
    ${classNameFirstLower}Delete(data) {
        return request({
            url: '/${moduleName}/${busName}/delete',
            method: 'POST',
            data: data
        })
    },
    // 获取${functionName}详情
    ${classNameFirstLower}Detail(data) {
        return request({
            url: '/${moduleName}/${busName}/detail',
            method: 'GET',
            data: data
        })
    },
}
