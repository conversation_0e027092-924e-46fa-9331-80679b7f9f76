import { checkPermission } from "@/utils/auth";
import store from "@/store";
// 页面跳转验证拦截器
let list = ["navigateTo", "redirectTo", "reLaunch", "switchTab"];
list.forEach((item) => {
  uni.addInterceptor(item, {
    invoke(args) {
      const currentPath = args.url.split("?")[0];
      store.dispatch("updateMenusActive", currentPath);
      checkPermission(currentPath);
    },
    fail(err) {
      console.log(err);
    },
  });
});
