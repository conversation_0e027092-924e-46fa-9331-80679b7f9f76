<template>
  <view class="auth-container">
    <image class="login-bg" src="@/static/images/login/login-bg.jpg" mode="aspectFill"></image>
    <image class="logo" src="@/static/images/login/logo.png" mode="widthFix"></image>
    <view class="auth-content">
      <button class="auth-button" :loading="isAuthenticating" open-type="getPhoneNumber" @getphonenumber="handleUserAuth">
        手机号快捷登录
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import loginApi from '@/api/auth/login-api'
import { useStore } from 'vuex' 
const store=useStore()
const isAuthenticating = ref(false)
// 用户点击授权按钮
const handleUserAuth = (e) => {
  console.log(e)
  if (isAuthenticating.value) return
  if (!e.detail.code) return
  isAuthenticating.value = true
  wxLogin(e.detail)
  isAuthenticating.value = false
}

// 微信登录
const wxLogin = async (e) => {
  try {
    // 调用后端登录接口，获取token
    const result = await loginApi.getPhoneNumber({
      code: e.code,
      maType: 'B',
      thirdId: uni.getStorageSync('thirdId')
    })
	
    uni.setStorageSync('isAuthPhone', 1)
    uni.setStorageSync('token', result.token)
    uni.setStorageSync('userInfo', result.userInfo || {})
    // 如果返回结果中包含roleApp信息，也要存储
    if (result.role_app) {
      uni.setStorageSync('roleApp', result.role_app)
      store.commit('SET_roleApp', result.role_app)
    }
    // 小程序消息推送
    wx.requestSubscribeMessage({
      tmplIds: ['lzKoFNlvsA01ceIlvkB_Gr-ROhQC64t7BdJveoglFdw'],
      success: (res) => {
        console.log(res)
        // 登录成功，通知App进行初始化
        if (uni.$login && typeof uni.$login.success === 'function') {
          uni.$login.success()
          return
        }
        // 返回上一页或首页
        setTimeout(() => {
          uni.navigateBack({
            delta: 1,
            fail: () => {
              uni.switchTab({
                url: '/pages/my-clue/index'
              })
            }
          })
        }, 300)
      },
      fail: (err) => {
        console.log(err)
         // 登录成功，通知App进行初始化
         if (uni.$login && typeof uni.$login.success === 'function') {
          uni.$login.success()
          return
        }
        // 返回上一页或首页
        setTimeout(() => {
          uni.navigateBack({
            delta: 1,
            fail: () => {
              uni.switchTab({
                url: '/pages/my-clue/index'
              })
            }
          })
        }, 300)
      }
    })
  } catch (error) {
    console.error('登录请求失败:', error)
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
  }
}
</script>

<style lang="scss" scoped>
.auth-container {
  height: 100vh;
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;

  .login-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
  }

  .logo {
    position: absolute;
    top: 40rpx;
    left: 40rpx;
    width: 240rpx;
    z-index: 1;
  }

  .auth-content {
    position: absolute;
    bottom: 240rpx;
    z-index: 1;
    width: 100%;
    display: flex;
    justify-content: center;
    padding: 0 40rpx;
  }

  .auth-button {
    background-color: #07c160;
    color: #ffffff;
    height: 90rpx;
    line-height: 90rpx;
    width: 80%;
    max-width: 600rpx;
    border-radius: 45rpx;
    font-size: 30rpx;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.2);

    .wechat-icon {
      font-size: 28rpx;
      margin-right: 10rpx;
    }
  }
}
</style>
