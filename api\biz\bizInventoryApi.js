import request from '@/utils/request'

export default {
  /**
   * 获取库存列表
   * @param {Object} params - 请求参数
   * @param {string} params.status - 库存状态：inStock(在库), outStock(已出库), sold(已售出)
   * @param {number} params.page - 页码
   * @param {number} params.size - 每页数量
   * @param {string} [params.keyword] - 搜索关键词
   * @param {Array} [params.conditions] - 筛选条件
   * @returns {Promise} - 返回库存列表数据
   */
  getInventoryList(params) {
    return request({
      url: '/partner/inventory/list',
      method: 'GET',
      params: params
    })
  },

  /**
   * 获取套餐和筛选条件列表
   * @returns {Promise} - 返回套餐和筛选条件数据
   */
  getPackageConditions() {
    return request({
      url: '/partner/inventory/conditions',
      method: 'GET'
    })
  },

  /**
   * 划拨激活码
   * @param {Object} data - 请求参数
   * @param {Array} data.codes - 激活码列表
   * @param {string} data.targetId - 目标ID（合伙人ID或商户ID）
   * @param {string} data.targetType - 目标类型：partner(合伙人), merchant(商户)
   * @returns {Promise} - 返回划拨结果
   */
  allocateCodes(data) {
    return request({
      url: '/partner/inventory/allocate',
      method: 'POST',
      data: data
    })
  },

  /**
   * 售出激活码
   * @param {Object} data - 请求参数
   * @param {Array} data.codes - 激活码列表
   * @param {string} [data.remark] - 备注
   * @returns {Promise} - 返回售出结果
   */
  sellCodes(data) {
    return request({
      url: '/partner/inventory/sell',
      method: 'POST',
      data: data
    })
  },

  /**
   * 获取激活码库存列表
   * @param {Object} data - 请求参数
   * @param {number} [data.current] - 当前页码
   * @param {number} [data.size] - 每页条数
   * @param {string} [data.searchKey] - 搜索关键词
   * @param {string} [data.partnerId] - 合伙人ID
   * @param {number} [data.inventoryStatus] - 库存状态：0-在库，1-出库，2-售出
   * @returns {Promise} - 返回激活码库存列表数据
   */
  getCdkeyList(data) {
    return request({
      url: '/biz/partner/cdkey/list',
      method: 'GET',
      data: data
    })
  },

  /**
   * 激活码划拨
   * @param {Object} data - 请求参数
   * @param {Array} data.cdkeyId - 激活码id数组
   * @param {number} data.type - 操作类型：0-划拨，1-回拨
   * @param {string} [data.targetPartnerId] - 对象合伙人id，回拨时不传
   * @param {string} [data.extJson] - 扩展信息
   * @returns {Promise} - 返回划拨结果
   */
  transferCdkey(data) {
    return request({
      url: '/biz/partner/cdkey/transfer',
      method: 'POST',
      data: data
    })
  },

  /**
   * 获取划拨记录列表
   * @param {Object} data - 请求参数
   * @param {number} [data.current] - 当前页码
   * @param {number} [data.size] - 每页条数
   * @param {string} [data.createTimeStart] - 创建时间开始
   * @param {string} [data.createTimeEnd] - 创建时间结束
   * @param {number} [data.type] - 操作类型：0-划拨，1-回拨
   * @returns {Promise} - 返回划拨记录列表数据
   */
  getTransferLog(data) {
    return request({
      url: '/biz/partner/cdkey/transferLog',
      method: 'GET',
      data: data
    })
  },

  /**
   * 激活码收回
   * @param {Array} data - 请求参数，激活码ID数组
   * @param {string} data[].id - 激活码ID
   * @returns {Promise} - 返回收回结果
   */
  withdrawCdkey(data) {
    return request({
      url: '/biz/partner/cdkey/withdraw',
      method: 'POST',
      data: data
    })
  },

  /**
   * 激活码售出
   * @param {Object} data - 请求参数
   * @param {string} data.id - 激活码ID
   * @returns {Promise} - 返回售出结果
   */
  sellCdkey(data) {
    return request({
      url: '/biz/partner/cdkey/sell',
      method: 'POST',
      data: data
    })
  },

  /**
   * 获取套餐下拉列表
   * @param {Object} data - 请求参数
   * @param {string} data.partnerId - 合伙人ID
   * @returns {Promise} - 返回套餐列表数据
   */
  getPackageOptions(data) {
    return request({
      url: '/biz/cdkey/groupCpackageByPartnerId',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取合伙人主页统计数据
   * @param {Object} data - 请求参数
   * @param {number} [data.dateType2] - 日期类型2，0-本周，1-近30日，2-近三月（更多不传）
   * @param {string} [data.dateTimeStart] - 开始时间（tab选更多时传范围）
   * @param {string} [data.dateTimeEnd] - 结束时间（tab选更多时传范围）
   * @param {string} data.partnerId - 合伙人ID
   * @returns {Promise} - 返回主页统计数据
   */
  getHomeStatistics(data) {
    return request({
      url: '/partner/home',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取商户看板数据
   * @param {Object} data - 请求参数
   * @param {number} [data.dateType] - 日期类型，0-当天，1-最近7天，2-最近一个月（更多不传）
   * @param {string} [data.dateTimeStart] - 开始时间（tab选更多时传范围）
   * @param {string} [data.dateTimeEnd] - 结束时间（tab选更多时传范围）
   * @returns {Promise} - 返回商户看板数据
   */
  getMerchantBoard(data) {
    return request({
      url: '/biz/miniB/task/merchantBoard',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取未读消息数量
   * @returns {Promise} - 返回未读消息数量
   */
  getUnreadMessageCount() {
    return request({
      url: '/biz/taskclue/message/unreadCount',
      method: 'GET'
    })
  },

  /**
   * 获取任务消息分页
   * @param {Object} data - 请求参数
   * @param {number} data.current - 当前页码
   * @param {number} data.size - 每页条数
   * @returns {Promise} - 返回消息列表数据
   */
  getMessageList(data) {
    return request({
      url: '/biz/taskclue/message/page',
      method: 'GET',
      data: data
    })
  },

  /**
   * 标记消息为已读
   * @param {Array} data - 请求参数，消息ID数组
   * @param {string} data[].id - 消息ID
   * @returns {Promise} - 返回操作结果
   */
  markMessageAsRead(data) {
    return request({
      url: '/biz/taskclue/message/read',
      method: 'POST',
      data: data
    })
  },

  /**
   * 获取任务线索分页
   * @param {Object} data - 请求参数
   * @param {number} data.current - 当前页码
   * @param {number} data.size - 每页条数
   * @param {string} [data.searchKey] - 关键词，任务名称、姓名、手机号
   * @param {number} [data.status] - 线索状态，0-待接单、1-已接单、2-跟进中、3-已完结，-1-已失效、-2-已废弃
   * @param {string} [data.createTimeStart] - 创建时间开始
   * @param {string} [data.createTimeEnd] - 创建时间结束
   * @returns {Promise} - 返回任务线索列表数据
   */
  getTaskClueList(data) {
    return request({
      url: '/biz/taskclue/page',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取任务线索分配记录
   * @param {Object} data - 请求参数
   * @param {string} data.taskClueId - 任务线索ID
   * @returns {Promise} - 返回分配记录数据
   */
  getTaskClueDistributeList(data) {
    return request({
      url: '/biz/taskclue/distribute/list',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取可重新分配员工列表
   * @param {Object} data - 请求参数
   * @param {string} data.taskClueId - 任务线索ID
   * @returns {Promise} - 返回员工列表数据
   */
  getReassignStaffList(data) {
    return request({
      url: '/biz/taskclue/distribute/queryStaffs',
      method: 'GET',
      data: data
    })
  },

  /**
   * 任务线索重新分配
   * @param {Object} data - 请求参数
   * @param {string} data.taskClueId - 任务线索ID
   * @param {string} data.assignStaffId - 分配员工ID
   * @param {string} [data.remarks] - 备注
   * @param {string} [data.extJson] - 扩展信息
   * @returns {Promise} - 返回操作结果
   */
  reassignTaskClue(data) {
    return request({
      url: '/biz/taskclue/distribute/add',
      method: 'POST',
      data: data
    })
  },

  /**
   * 获取任务线索详情
   * @param {Object} data - 请求参数
   * @param {string} data.id - 线索ID
   * @returns {Promise} - 返回线索详情数据
   */
  getTaskClueDetail(data) {
    return request({
      url: '/biz/taskclue/detail',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取任务线索跟进记录
   * @param {Object} data - 请求参数
   * @param {number} data.current - 当前页码
   * @param {number} data.size - 每页条数
   * @param {string} data.taskClueId - 任务线索ID
   * @returns {Promise} - 返回跟进记录数据
   */
  getTaskClueFollowupList(data) {
    return request({
      url: '/biz/taskclue/followupList',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取工单管理分页
   * @param {Object} data - 请求参数
   * @param {number} data.current - 当前页码
   * @param {number} data.size - 每页条数
   * @param {string} data.taskClueId - 任务线索ID
   * @returns {Promise} - 返回工单列表数据
   */
  getTicketList(data) {
    return request({
      url: '/biz/ticket/page',
      method: 'GET',
      data: data
    })
  },

  /**
   * 修改任务线索状态
   * @param {Object} data - 请求参数
   * @param {string} data.id - 线索ID
   * @param {number} data.status - 状态：0-待接单、1-已接单、2-跟进中、3-已完结、-1-已失效、-2-已废弃
   * @returns {Promise} - 返回操作结果
   */
  updateTaskClueStatus(data) {
    return request({
      url: '/biz/taskclue/updateStatus',
      method: 'POST',
      data: data
    })
  },

  /**
   * 获取工单详情
   * @param {Object} data - 请求参数
   * @param {string} [data.id] - 工单ID（编辑时传入）
   * @param {string} [data.taskClueId] - 任务线索ID（新增时传入）
   * @returns {Promise} - 返回工单详情数据
   */
  getTicketDetail(data) {
    return request({
      url: '/biz/ticket/detail',
      method: 'GET',
      data: data
    })
  },

  /**
   * 新增工单
   * @param {Object} data - 工单数据
   * @returns {Promise} - 返回操作结果
   */
  createTicket(data) {
    return request({
      url: '/biz/ticket/add',
      method: 'POST',
      data: data
    })
  },

  /**
   * 编辑工单
   * @param {Object} data - 工单数据
   * @returns {Promise} - 返回操作结果
   */
  updateTicket(data) {
    return request({
      url: '/biz/ticket/edit',
      method: 'POST',
      data: data
    })
  },

  /**
   * 删除工单
   * @param {Object} data - 请求参数
   * @param {string} data.id - 工单ID
   * @returns {Promise} - 返回操作结果
   */
  deleteTicket(data) {
    return request({
      url: '/biz/ticket/delete',
      method: 'POST',
      data: data
    })
  },

  /**
   * 查询单个激活码详情
   * @param {Object} params - 请求参数
   * @param {string} params.code - 激活码
   * @returns {Promise} - 返回激活码详情
   */
  getCodeDetail(params) {
    return request({
      url: '/partner/inventory/detail',
      method: 'GET',
      params: params
    })
  },

  /**
   * 批量导入激活码
   * @param {Object} data - 请求参数
   * @param {Array} data.codes - 激活码列表
   * @param {string} data.packageType - 套餐类型
   * @param {string} data.validity - 有效期类型
   * @returns {Promise} - 返回导入结果
   */
  importCodes(data) {
    return request({
      url: '/partner/inventory/import',
      method: 'POST',
      data: data
    })
  }
}
