<template>
  <view class="demo-page">
    <view class="demo-title">商户管理页面演示</view>
    <view class="demo-subtitle">我的商户（有筛选条件） vs 合伙人商户（纯列表）</view>
    
    <!-- Tab 切换 -->
    <view class="tab-section">
      <view class="tab-container">
        <view 
          class="tab-item"
          :class="{ 'active': activeTab === 'my' }"
          @click="switchTab('my')"
        >
          我的商户
        </view>
        <view 
          class="tab-item"
          :class="{ 'active': activeTab === 'partner' }"
          @click="switchTab('partner')"
        >
          合伙人商户
        </view>
      </view>
    </view>

    <!-- 查询条件 - 仅在我的商户Tab显示 -->
    <view class="filter-section" v-if="activeTab === 'my'">
      <view class="filter-title">筛选条件</view>
      
      <!-- 第一行筛选条件 -->
      <custom-dropdown-menu>
        <custom-dropdown-item
          :value="filterData.merchantName"
          :options="merchantNameOptions"
          @change="onMerchantNameChange"
          title="商户名称"
          style="width: 30%;"
        />
        <custom-dropdown-item
          :value="filterData.merchantType"
          :options="merchantTypeOptions"
          @change="onMerchantTypeChange"
          title="商户类型"
          style="width: 25%;"
        />
        <custom-dropdown-item
          :value="filterData.status"
          :options="statusOptions"
          @change="onStatusChange"
          title="状态"
          style="width: 40%;"
        />
      </custom-dropdown-menu>

      <!-- 第二行筛选条件 -->
      <custom-dropdown-menu>
        <custom-dropdown-item
          :value="filterData.packageName"
          :options="packageNameOptions"
          @change="onPackageNameChange"
          title="套餐名称"
          style="width: 30%;"
        />
        <custom-dropdown-item
          title="入驻时间"
          :show-selected-value="false"
          style="width: 25%;"
        >
          <view class="datetime-picker-container">
            <uni-datetime-picker
              v-model="joinTimeRange"
              type="daterange"
              rangeSeparator="至"
              placeholder="选择入驻时间范围"
              @change="onJoinTimeChange"
            />
          </view>
        </custom-dropdown-item>
        <custom-dropdown-item
          title="激活码到期日"
          :show-selected-value="false"
          style="width: 40%;"
        >
          <view class="datetime-picker-container">
            <uni-datetime-picker
              v-model="expireTimeRange"
              type="datetimerange"
              rangeSeparator="至"
              placeholder="选择激活码到期时间范围"
              @change="onExpireTimeChange"
            />
          </view>
        </custom-dropdown-item>
      </custom-dropdown-menu>
    </view>

    <!-- 商户列表 -->
    <view class="merchant-list-section">
      <view class="list-title">
        {{ activeTab === 'my' ? '我的商户列表' : '合伙人商户列表' }}
        <text class="list-count">({{ merchantList.length }}个)</text>
      </view>
      
      <view class="merchant-list">
        <view 
          class="merchant-item"
          v-for="(item, index) in merchantList"
          :key="index"
        >
          <view class="merchant-info">
            <view class="merchant-name">{{ item.name }}</view>
            <view class="merchant-detail">{{ item.detail }}</view>
          </view>
          <view class="merchant-count">{{ item.count }}个</view>
        </view>
      </view>
    </view>

    <!-- 功能说明 -->
    <view class="feature-section">
      <view class="feature-title">页面特点</view>
      
      <view class="feature-item">
        <view class="feature-name">我的商户</view>
        <view class="feature-desc">
          包含完整的筛选条件：商户名称、类型、状态、套餐、入驻时间、激活码到期日
        </view>
      </view>
      
      <view class="feature-item">
        <view class="feature-name">合伙人商户</view>
        <view class="feature-desc">
          纯列表展示，无筛选条件，界面简洁清爽
        </view>
      </view>
      
      <view class="feature-item">
        <view class="feature-name">动态显示</view>
        <view class="feature-desc">
          根据选中的 Tab 动态显示或隐藏筛选条件区域
        </view>
      </view>
    </view>

    <!-- 当前状态显示 -->
    <view class="status-section">
      <view class="status-title">当前状态</view>
      <view class="status-content">
        <view class="status-item">
          <text class="status-label">当前Tab:</text>
          <text class="status-value">{{ activeTab === 'my' ? '我的商户' : '合伙人商户' }}</text>
        </view>
        <view class="status-item">
          <text class="status-label">筛选条件:</text>
          <text class="status-value">{{ activeTab === 'my' ? '显示' : '隐藏' }}</text>
        </view>
        <view class="status-item">
          <text class="status-label">列表类型:</text>
          <text class="status-value">{{ activeTab === 'my' ? '可筛选列表' : '简单分页列表' }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import CustomDropdownMenu from '@/components/custom-dropdown/custom-dropdown-menu.vue'
import CustomDropdownItem from '@/components/custom-dropdown/custom-dropdown-item.vue'

// 当前选中的Tab
const activeTab = ref('my')

// 筛选数据
const filterData = ref({
  merchantName: '',
  merchantType: '',
  status: '',
  packageName: ''
})

// uni-datetime-picker 数据
const joinTimeRange = ref('')
const expireTimeRange = ref('')

// 商户列表数据
const merchantList = ref([
  { name: '我是二级合伙人名称', detail: '商户详情信息', count: '20' },
  { name: '我是二级合伙人名称', detail: '商户详情信息', count: '20' },
  { name: '我是二级合伙人名称', detail: '商户详情信息', count: '20' },
  { name: '我是二级合伙人名称', detail: '商户详情信息', count: '20' },
  { name: '我是二级合伙人名称', detail: '商户详情信息', count: '20' }
])

// 筛选选项
const merchantNameOptions = ref([
  { text: '全部', value: '' },
  { text: '商贸公司', value: '商贸公司' },
  { text: '科技有限公司', value: '科技有限公司' },
  { text: '餐饮管理公司', value: '餐饮管理公司' }
])

const merchantTypeOptions = ref([
  { text: '全部', value: '' },
  { text: '企业', value: '企业' },
  { text: '个体工商户', value: '个体工商户' },
  { text: '合作社', value: '合作社' }
])

const statusOptions = ref([
  { text: '全部', value: '' },
  { text: '有效', value: '有效' },
  { text: '已到期', value: '已到期' },
  { text: '待审核', value: '待审核' }
])

const packageNameOptions = ref([
  { text: '全部', value: '' },
  { text: '基础版', value: '基础版' },
  { text: '标准版', value: '标准版' },
  { text: '专业版', value: '专业版' }
])

// Tab 切换
const switchTab = (tab) => {
  activeTab.value = tab
  console.log('切换到:', tab === 'my' ? '我的商户' : '合伙人商户')
}

// 筛选事件处理
const onMerchantNameChange = (value) => {
  filterData.value.merchantName = value.text
  console.log('商户名称筛选:', value.text)
}

const onMerchantTypeChange = (value) => {
  filterData.value.merchantType = value.text
  console.log('商户类型筛选:', value.text)
}

const onStatusChange = (value) => {
  filterData.value.status = value.text
  console.log('状态筛选:', value.text)
}

const onPackageNameChange = (value) => {
  filterData.value.packageName = value.text
  console.log('套餐名称筛选:', value.text)
}

const onJoinTimeChange = (value) => {
  console.log('入驻时间选择:', value)
}

const onExpireTimeChange = (value) => {
  console.log('激活码到期时间选择:', value)
}
</script>

<style lang="scss" scoped>
.demo-page {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.demo-title {
  font-size: 40rpx;
  font-weight: bold;
  text-align: center;
  color: #333;
  margin-bottom: 10rpx;
}

.demo-subtitle {
  font-size: 28rpx;
  text-align: center;
  color: #666;
  margin-bottom: 30rpx;
}

.tab-section {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
}

.tab-container {
  display: flex;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 8rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  font-size: 28rpx;
  color: #666;
  border-radius: 8rpx;
  transition: all 0.3s;
  
  &.active {
    background: white;
    color: rgba(85, 152, 255, 1);
    font-weight: 600;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  }
}

.filter-section {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.filter-title {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  background: #f8f9fa;
  border-bottom: 1rpx solid #eee;
}

.merchant-list-section {
  background: white;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.list-title {
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  background: #f8f9fa;
  border-bottom: 1rpx solid #eee;
  display: flex;
  align-items: center;
}

.list-count {
  font-size: 26rpx;
  color: #666;
  font-weight: normal;
  margin-left: 10rpx;
}

.merchant-list {
  padding: 20rpx;
}

.merchant-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  
  &:last-child {
    border-bottom: none;
  }
}

.merchant-info {
  flex: 1;
}

.merchant-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.merchant-detail {
  font-size: 26rpx;
  color: #666;
}

.merchant-count {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.feature-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.feature-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.feature-item {
  margin-bottom: 20rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.feature-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.feature-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
}

.status-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
}

.status-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.status-content {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.status-label {
  font-size: 26rpx;
  color: #666;
}

.status-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

// uni-datetime-picker 容器样式
.datetime-picker-container {
  padding: 40rpx;
  background: white;
  
  :deep(.uni-datetime-picker__input) {
    border: 1rpx solid #e9ecef;
    border-radius: 8rpx;
    padding: 20rpx 24rpx;
    font-size: 28rpx;
    background: #f8f9fa;
  }
}
</style>
