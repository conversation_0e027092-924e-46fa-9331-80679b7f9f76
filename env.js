const defaultEnvKey = "local";
const defaultAllEnv = {
  local: {
    name: "本地环境",
    // 服务端baseUrl
    // baseUrl: "https://antscard.com", 
    baseUrl: "https://addnow.tech",
    // baseUrl: "http://211.149.146.95:8003",
    // 主租户域（企业版多租户使用：注意与后端snowy.config.common.front-url进行对应）
    mainTenantDomain: "https://antscard.com:8003",
    // 默认当前租户域（可以是子租户域也可以是主租户域）
    tenantDomain: "https://antscard.com/:8003",
    image_baseUrl:'https://www.antscard.com/minio/art-cloud-prod',
    // 登录切换租户域
    loginSwitchTenant: false,
    appId: "wxe7d9fe5d85c47197",
  },
  pro: {
    name: "生产环境",
    // 服务端baseUrl
    baseUrl: "https://antscard.com:8003",
    // 主租户域（企业版多租户使用：注意与后端snowy.config.common.front-url进行对应）
    mainTenantDomain: "https://antscard.com:8003",
    // 默认当前租户域（可以是子租户域也可以是主租户域）
    tenantDomain: "https://antscard.com:8003",
    // 登录切换租户域
    loginSwitchTenant: false,
    appId: "wx5b7cfe926580dcdf",
  },
};
// 应用全局配置
export default {
  DEFAULT_ENV_KEY: defaultEnvKey,
  DEFAULT_ALL_ENV: defaultAllEnv,
};
export const OSS_URL = "https://antscard.com/minio/art-cloud-prod/";
