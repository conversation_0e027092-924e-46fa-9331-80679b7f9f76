<!--
 * @Descripttion: 我的推荐官列表页面
 * @Author: <PERSON>
 * @Date: 2025-07-15 16:18:00
 * @LastEditors: <PERSON>
 * @LastEditTime: 2025-07-15 16:18:00
-->
<template>
  <snowy-layout title="我的推荐官" :isTabbar="false" :isFirstPage="false">
    <view class="container">
      <!-- 搜索框 -->
      <view class="search-box">
        <view class="search-input">
          <uni-icons class="search-icon" type="search" size="18" color="#999"></uni-icons>
          <input 
            class="input" 
            type="text" 
            v-model="keyword"
            placeholder="可输入姓名、证件号码、手机号进行查询"
            placeholder-style="color: #999;"
            confirm-type="search"
            @confirm="handleSearch"
          />
          <text v-if="keyword" class="clear-icon" @click="clearKeyword">×</text>
        </view>
      </view>

      <!-- 列表为空时 -->
      <view v-if="recommenderList.length === 0" class="empty-container">
        <image class="empty-image" src="/static/images/common/empty.png" mode="aspectFit"></image>
        <text class="empty-text">暂无推荐官数据</text>
      </view>
      
      <!-- 推荐官列表 -->
      <scroll-view 
        v-else 
        class="recommender-list"
        scroll-y
        @scrolltolower="loadMore"
        refresher-enabled
        :refresher-triggered="refreshing"
        @refresherrefresh="onRefresh"
        :style="{ height: scrollHeight + 'px' }"
      >
        <view 
          v-for="(item, index) in recommenderList" 
          :key="index" 
          class="recommender-item"
        >
          <view class="recommender-header">
            <view class="recommender-avatar-name">
              <image class="recommender-avatar" src="/static/images/default-avatar.png" mode="aspectFill"></image>
              <text class="recommender-name">{{ item.name }}</text>
            </view>
            <text v-if="item.registerStatus === 1" class="registered-tag">已注册</text>
            <text v-else class="unregistered-tag">未注册</text>
          </view>
          
          <view class="recommender-detail">
            <view class="detail-row">
              <text class="detail-label">性别</text>
              <text class="detail-value">{{ item.sex === 0 ? '男' : '女' }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">证件号码</text>
              <text class="detail-value">{{ item.idCard }}</text>
            </view>
            <view class="detail-row">
              <text class="detail-label">手机号码</text>
              <text class="detail-value">{{ item.mobile }}</text>
            </view>
            <view class="detail-row" v-if="item.merchantName">
              <text class="detail-label">商户名称</text>
              <text class="detail-value">{{ item.merchantName }}</text>
            </view>
          </view>
          
          <!-- 操作按钮 -->
          <view class="recommender-actions" v-if="item.registerStatus === 0">
            <view class="action-button delete-button" @click.stop="handleDelete(item)">
              <text>删除</text>
            </view>
            <view class="action-button edit-button" @click.stop="handleEdit(item)">
              <text>信息修改</text>
            </view>
          </view>
        </view>
        
        <!-- 加载状态 -->
        <view class="loading-more" v-if="recommenderList.length > 0">
          <text v-if="loading">加载中...</text>
          <text v-else-if="hasMore === false">没有更多数据了</text>
        </view>
      </scroll-view>
      
    </view>
  </snowy-layout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import bizMiniApi from '@/api/biz/bizMiniApi'
import uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue'
// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const totalCount = ref(0)
const keyword = ref('')
const loading = ref(false)
const refreshing = ref(false)
const hasMore = ref(true)
const scrollHeight = ref(0)

// 推荐官列表
const recommenderList = ref([])

// 计算滚动区域高度
const calculateScrollHeight = () => {
  const systemInfo = uni.getSystemInfoSync()
  const windowHeight = systemInfo.windowHeight
  // 搜索框高度 + 顶部状态栏高度 + 页面padding + 底部安全区域
  const otherHeight = 80 + systemInfo.statusBarHeight + 40 + 20
  scrollHeight.value = windowHeight - uni.upx2px(otherHeight)
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  recommenderList.value = []
  hasMore.value = true
  getRecommenderList()
}

// 清除关键词
const clearKeyword = () => {
  keyword.value = ''
  currentPage.value = 1
  recommenderList.value = []
  hasMore.value = true
  getRecommenderList()
}

// 加载更多
const loadMore = () => {
  if (loading.value || !hasMore.value) return
  currentPage.value++
  getRecommenderList(true)
}

// 刷新
const onRefresh = () => {
  refreshing.value = true
  currentPage.value = 1
  recommenderList.value = []
  hasMore.value = true
  getRecommenderList().finally(() => {
    refreshing.value = false
  })
}

// 编辑推荐官
const handleEdit = (item) => {
  uni.navigateTo({
    url: `/pages/mine/recommender/edit?id=${item.id}`
  })
}

// 删除推荐官
const handleDelete = (item) => {
  uni.showModal({
    title: '提示',
    content: '确定要删除该推荐官吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({
            title: '删除中...'
          })
          await bizMiniApi.bizMiniReferralDelete({ id: item.id })
          uni.hideLoading()
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          })
          // 重新加载列表
          currentPage.value = 1
          recommenderList.value = []
          hasMore.value = true
          getRecommenderList()
        } catch (error) {
          uni.hideLoading()
          uni.showToast({
            title: error.message || '删除失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 获取推荐官列表
const getRecommenderList = async (isLoadMore = false) => {
  if (loading.value) return
  loading.value = true
  
  try {
    if (!isLoadMore) {
      uni.showLoading({
        title: '加载中...'
      })
    }
    
    const params = {
      current: currentPage.value,
      size: pageSize.value
    }
    
    // 如果有关键词，加入搜索条件
    if (keyword.value.trim()) {
      params.keyword = keyword.value.trim()
      // params.name = keyword.value.trim()
    }
    
    const res = await bizMiniApi.bizMiniReferralList(params)
    
    if (res && res.records) {
      // 如果是加载更多，追加数据
      if (isLoadMore) {
        recommenderList.value = [...recommenderList.value, ...res.records]
      } else {
        recommenderList.value = res.records
      }
      
      totalCount.value = res.total || 0
      hasMore.value = recommenderList.value.length < totalCount.value
    } else {
      if (!isLoadMore) {
        recommenderList.value = []
      }
      hasMore.value = false
    }
    
    uni.hideLoading()
  } catch (error) {
    console.error('获取推荐官列表失败:', error)
    uni.hideLoading()
    uni.showToast({
      title: error.message || '获取数据失败',
      icon: 'none'
    })
    if (!isLoadMore) {
      recommenderList.value = []
    }
    hasMore.value = false
  } finally {
    loading.value = false
    uni.stopPullDownRefresh()
  }
}

onShow(() => {
  getRecommenderList()
})
// 页面加载
onMounted(() => {
  calculateScrollHeight()
  // 监听窗口大小变化
  window.addEventListener('resize', calculateScrollHeight)
})
</script>

<style lang="scss" scoped>
.container {
  background-color: #f1f4f9;
  height: 100%;
  position: relative;
  box-sizing: border-box;
}

.search-box {
  padding: 20rpx 28rpx;
  background-color: #fff;
}

.search-input {
  display: flex;
  align-items: center;
  background-color: rgba(246, 246, 246, 1);
  border-radius: 40rpx;
  padding: 0 20rpx;
  height: 70rpx;
}

.search-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.input {
  flex: 1;
  font-size: 28rpx;
  height: 70rpx;
}

.clear-icon {
  font-size: 40rpx;
  color: #999;
  padding: 0 10rpx;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 200rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

  .recommender-list {
    width: 100%;
    box-sizing: border-box;
  }

.recommender-item {
  background-color: #fff;
  padding: 30rpx;
  margin: 20rpx 26rpx;
  border-radius: 12rpx;
}

.recommender-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.recommender-avatar-name {
  display: flex;
  align-items: center;
}

.recommender-avatar {
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.recommender-name {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.registered-tag {
  font-size: 28rpx;
  color: rgba(255, 209, 0, 1);
  padding: 4rpx 12rpx;
}

.unregistered-tag {
  font-size: 28rpx;
  color: rgba(232, 49, 62, 1);
  padding: 4rpx 12rpx;
}

.recommender-detail {
  padding-top: 20rpx;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 22rpx;
}

.detail-label {
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.60);
}

.detail-value {
  font-size: 28rpx;
  color: #333;
  text-align: right;
}

.recommender-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 30rpx;
}

.action-button {
  height: 60rpx;
  display: flex;
  padding: 0 26rpx;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  margin-left: 20rpx;
  border-radius: 60rpx 60rpx 60rpx 60rpx;
}

.delete-button {
  color: rgba(0, 0, 0, 0.60);
  background-color: rgba(243, 243, 243, 1);
}

.edit-button {
  background-color: #ffcc00;
  color: #333;
}

.loading-more {
  text-align: center;
  padding: 20rpx 0;
  color: #999;
  font-size: 26rpx;
}
</style> 