<template>
	<view>
		<tui-time-axis>
			<tui-timeaxis-item backgroundColor="transparent" v-for="(item,index) in commentList" :key="index">
				<template v-slot:node>
					<view class="st-node">
						<icon :type="getType(item.operateType)" size="20" :color="getColor(item.operateType)" />
					</view>
				</template>
				<template v-slot:content>
					<view>
						<view class="st-title">{{item.taskName}}</view>
						<view class="st-desc">
							{{item.userName}} <text :style="{color: getColor(item.operateType)}">{{item.operateText}}</text>
						</view>
						<view class="st-desc st-gray">
							{{item.comment}}
						</view>
						<view class="st-desc">
							<snowy-file-view :fileList="getAttachmentList(item.attachmentList)"></snowy-file-view>
						</view>
						<view class="st-time st-gray">{{item.approveTime}}</view>
					</view>
				</template>
			</tui-timeaxis-item>
		</tui-time-axis>
	</view>
</template>
<script setup>
	import { ref } from "vue"
	import fileApi from '@/api/dev/file-api'
	const props = defineProps({
		commentList: {
			type: Array,
			default: [],
			required: true
		}
	})
	const getType = (value) => {
		if (value === 'START') {
			return 'success'
		} else if (value === 'PASS') {
			return 'success'
		} else if (value === 'REJECT') {
			return 'warn'
		} else if (value === 'BACK') {
			return 'warn'
		} else if (value === 'REVOKE') {
			return 'warn'
		} else if (value === 'JUMP') {
			return 'waiting'
		} else if (value === 'TURN') {
			return 'waiting'
		} else if (value === 'END') {
			return 'success'
		} else {
			return 'success'
		}
	}
	// 获取颜色
	const getColor = (value) => {
		if (value === 'START') {
			return '#1890FF'
		} else if (value === 'PASS') {
			return '#52C41A'
		} else if (value === 'REJECT') {
			return '#FF4D4F'
		} else if (value === 'BACK') {
			return '#BFBFBF'
		} else if (value === 'REVOKE') {
			return '#BFBFBF'
		} else if (value === 'JUMP') {
			return '#BFBFBF'
		} else if (value === 'TURN') {
			return '#BFBFBF'
		} else if (value === 'END') {
			return '#FF4D4F'
		} else {
			return '#1890FF'
		}
	}
	const getAttachmentList = (attachmentList) => {
		if (attachmentList && attachmentList.length > 0) {
			return attachmentList.map(item => {
				return {
					id: uni.$xeu.parseUrl(item.attachmentUrl)?.searchQuery?.id,
					name: item.attachmentName,
					url: item.attachmentUrl,
					ext: item.attachmentName.split('.').pop()
				}
			})
		} else {
			return []
		}
	}
</script>
<style lang="scss" scoped>
	.st-node {
		height: 40rpx;
		width: 40rpx;
		border-radius: 50%;
		background-color: #ddd;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
		flex-shrink: 0;
	}

	.st-title {
		padding-bottom: 12rpx;
		font-size: 32rpx;
		color: #333;
		font-weight: bold;
	}

	.st-desc {
		padding-bottom: 12rpx;
		font-size: 28rpx;
		color: #333;
	}

	.st-time {
		font-size: 24rpx;
		font-weight: bold;
	}

	.st-gray {
		color: #848484 !important;
	}

	.st-light-gray {
		color: #888 !important;
	}

	.st-primary {
		color: #5677fc;
	}
</style>