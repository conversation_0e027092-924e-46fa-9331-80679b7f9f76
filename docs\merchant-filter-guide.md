# 商户管理筛选条件使用指南

## 概述

商户管理页面根据不同的 Tab（我的商户 vs 合伙人商户）显示不同的筛选条件。我的商户包含完整的筛选选项，而合伙人商户只显示简化的筛选条件。

## 功能特点

- ✅ **动态筛选**: 根据选中的 Tab 动态显示不同的筛选条件
- ✅ **我的商户**: 包含完整的筛选选项（6个筛选条件）
- ✅ **合伙人商户**: 简化的筛选条件（2个筛选条件）
- ✅ **日期时间选择**: 使用 uni-datetime-picker 组件

## 筛选条件对比

### 我的商户筛选条件

**第一行筛选条件**：
- 商户名称（下拉选择）
- 商户类型（下拉选择）
- 状态（下拉选择）

**第二行筛选条件**：
- 套餐名称（下拉选择）
- 入驻时间（日期范围选择）
- 激活码到期日（日期时间范围选择）

### 合伙人商户筛选条件

**单行筛选条件**：
- 合伙人名称（下拉选择）
- 状态（下拉选择）

## 实现方式

### 模板结构

```vue
<template>
  <!-- 查询条件 -->
  <view class="filter-section">
    <!-- 我的商户筛选条件 -->
    <template v-if="activeTab === 'my'">
      <!-- 第一行筛选条件 -->
      <custom-dropdown-menu>
        <custom-dropdown-item
          :value="filterData.merchantName"
          :options="merchantNameOptions"
          @change="onMerchantNameChange"
          title="商户名称"
          style="width: 30%;"
        />
        <custom-dropdown-item
          :value="filterData.merchantType"
          :options="merchantTypeOptions"
          @change="onMerchantTypeChange"
          title="商户类型"
          style="width: 25%;"
        />
        <custom-dropdown-item
          :value="filterData.status"
          :options="statusOptions"
          @change="onStatusChange"
          title="状态"
          style="width: 40%;"
        />
      </custom-dropdown-menu>

      <!-- 第二行筛选条件 -->
      <custom-dropdown-menu>
        <custom-dropdown-item
          :value="filterData.packageName"
          :options="packageNameOptions"
          @change="onPackageNameChange"
          title="套餐名称"
          style="width: 30%;"
        />
        <custom-dropdown-item
          title="入驻时间"
          :show-selected-value="false"
          style="width: 25%;"
        >
          <view class="datetime-picker-container">
            <uni-datetime-picker
              v-model="joinTimeRange"
              type="daterange"
              rangeSeparator="至"
              placeholder="选择入驻时间范围"
              @change="onJoinTimeChange"
            />
          </view>
        </custom-dropdown-item>
        <custom-dropdown-item
          title="激活码到期日"
          :show-selected-value="false"
          style="width: 40%;"
        >
          <view class="datetime-picker-container">
            <uni-datetime-picker
              v-model="expireTimeRange"
              type="datetimerange"
              rangeSeparator="至"
              placeholder="选择激活码到期时间范围"
              @change="onExpireTimeChange"
            />
          </view>
        </custom-dropdown-item>
      </custom-dropdown-menu>
    </template>

    <!-- 合伙人商户筛选条件 -->
    <template v-else-if="activeTab === 'partner'">
      <custom-dropdown-menu>
        <custom-dropdown-item
          :value="filterData.partnerName"
          :options="partnerNameOptions"
          @change="onPartnerNameChange"
          title="合伙人名称"
          style="width: 50%;"
        />
        <custom-dropdown-item
          :value="filterData.partnerStatus"
          :options="partnerStatusOptions"
          @change="onPartnerStatusChange"
          title="状态"
          style="width: 50%;"
        />
      </custom-dropdown-menu>
    </template>
  </view>
</template>
```

### 数据结构

```javascript
// 当前选中的Tab
const activeTab = ref('my')

// 查询条件
const filterData = ref({
  // 我的商户筛选条件
  merchantName: '商贸公司',
  merchantType: '',
  status: '',
  packageName: '',
  joinTime: '',
  expireTime: '',
  // 合伙人商户筛选条件
  partnerName: '',
  partnerStatus: ''
})

// uni-datetime-picker 数据
const joinTimeRange = ref('')
const expireTimeRange = ref('')
```

### 筛选选项配置

```javascript
// 我的商户筛选选项
const merchantNameOptions = ref([
  { text: '全部', value: '' },
  { text: '商贸公司', value: '商贸公司' },
  { text: '科技有限公司', value: '科技有限公司' },
  { text: '餐饮管理公司', value: '餐饮管理公司' },
  { text: '零售连锁店', value: '零售连锁店' }
])

const merchantTypeOptions = ref([
  { text: '全部', value: '' },
  { text: '企业', value: '企业' },
  { text: '个体工商户', value: '个体工商户' },
  { text: '合作社', value: '合作社' },
  { text: '其他', value: '其他' }
])

const statusOptions = ref([
  { text: '全部', value: '' },
  { text: '有效', value: '有效' },
  { text: '已到期', value: '已到期' },
  { text: '待审核', value: '待审核' },
  { text: '已停用', value: '已停用' }
])

const packageNameOptions = ref([
  { text: '全部', value: '' },
  { text: '基础版', value: '基础版' },
  { text: '标准版', value: '标准版' },
  { text: '专业版', value: '专业版' },
  { text: '企业版', value: '企业版' }
])

// 合伙人商户筛选选项
const partnerNameOptions = ref([
  { text: '全部', value: '' },
  { text: '张三合伙人', value: 'zhangsan' },
  { text: '李四合伙人', value: 'lisi' },
  { text: '王五合伙人', value: 'wangwu' },
  { text: '赵六合伙人', value: 'zhaoliu' }
])

const partnerStatusOptions = ref([
  { text: '全部', value: '' },
  { text: '正常', value: 'active' },
  { text: '停用', value: 'inactive' },
  { text: '待审核', value: 'pending' }
])
```

### 事件处理

```javascript
// 我的商户筛选事件处理
const onMerchantNameChange = (value) => {
  filterData.value.merchantName = value.text
  refreshList()
}

const onMerchantTypeChange = (value) => {
  filterData.value.merchantType = value.text
  refreshList()
}

const onStatusChange = (value) => {
  filterData.value.status = value.text
  refreshList()
}

const onPackageNameChange = (value) => {
  filterData.value.packageName = value.text
  refreshList()
}

const onJoinTimeChange = (value) => {
  console.log('入驻时间选择:', value)
  filterData.value.joinTime = value
  refreshList()
}

const onExpireTimeChange = (value) => {
  console.log('激活码到期时间选择:', value)
  filterData.value.expireTime = value
  refreshList()
}

// 合伙人商户筛选事件处理
const onPartnerNameChange = (value) => {
  filterData.value.partnerName = value.text
  refreshList()
}

const onPartnerStatusChange = (value) => {
  filterData.value.partnerStatus = value.text
  refreshList()
}
```

## 样式配置

### uni-datetime-picker 容器样式

```scss
.datetime-picker-container {
  padding: 40rpx;
  background: white;
  
  :deep(.uni-datetime-picker) {
    width: 100%;
  }
  
  :deep(.uni-datetime-picker__input) {
    border: 1rpx solid #e9ecef;
    border-radius: 8rpx;
    padding: 20rpx 24rpx;
    font-size: 28rpx;
    background: #f8f9fa;
  }
}
```

### 筛选区域样式

```scss
.filter-section {
  background: white;
  border-top: 1rpx solid #f0f0f0;
  
  .custom-dropdown-menu {
    margin-bottom: 2rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}
```

## 使用场景

### 我的商户页面
- 需要详细的筛选条件
- 包含商户基本信息筛选
- 包含时间范围筛选
- 适用于复杂的商户管理场景

### 合伙人商户页面
- 简化的筛选条件
- 只关注合伙人和状态
- 界面更简洁
- 适用于合伙人商户查看场景

## 最佳实践

### 1. 条件式渲染
使用 `v-if` 和 `v-else-if` 根据 Tab 状态显示不同的筛选条件：

```vue
<template v-if="activeTab === 'my'">
  <!-- 我的商户筛选条件 -->
</template>

<template v-else-if="activeTab === 'partner'">
  <!-- 合伙人商户筛选条件 -->
</template>
```

### 2. 数据结构设计
将不同类型的筛选条件放在同一个对象中，便于管理：

```javascript
const filterData = ref({
  // 我的商户筛选条件
  merchantName: '',
  merchantType: '',
  // 合伙人商户筛选条件
  partnerName: '',
  partnerStatus: ''
})
```

### 3. 事件处理统一
为不同类型的筛选条件创建对应的事件处理函数，保持代码清晰。

## 示例文件

完整的使用示例请参考：
- `examples/merchant-filter-demo.vue` - 功能演示页面
- `partner/merchant/index.vue` - 实际应用示例

通过这种设计，可以为不同类型的商户提供合适的筛选体验，既满足了复杂筛选的需求，又保持了界面的简洁性。
