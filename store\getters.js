const getters = {
  envKey: (state) => state.global.envKey,
  allEnv: (state) => state.global.allEnv,
  curEnv: (state) => state.global.allEnv[state.global.envKey],
  tenantDomain: (state) => state.global.tenantDomain,
  homeConfigs: (state) => state.global.homeConfigs,
  token: (state) => state.global.token,
  userMobileMenus: (state) => state.global.userMobileMenus,
  userInfo: (state) => state.global.userInfo,
  roleApp: (state) => state.global.roleApp,
  sysBaseConfig: (state) => state.global.sysBaseConfig,
  dictTypeTreeData: (state) => state.global.dictTypeTreeData,
  refreshKey: (state) => state.global.refreshKey,
  refreshFlag: (state) => state.global.refreshFlag,
  refreshParam: (state) => state.global.refreshParam,
  commonParam: (state) => state.global.commonParam,
  menus: (state) => state.global.menus,
  currentMenu: (state) => state.global.currentMenu,
  cardInfo: (state) => state.global.cardInfo,
  // 用户身份相关的 getter
  entityType: (state) => state.global.roleApp?.entityType,
  entitySubType: (state) => state.global.roleApp?.entitySubType,
  // 判断是否为商户管理员
  isMerchantAdmin: (state) => state.global.roleApp?.entityType === 'MER',
  // 判断是否为合伙人
  isPartner: (state) => state.global.roleApp?.entityType === 'PARTNER',
  // 判断是否为员工
  isStaff: (state) => state.global.roleApp?.entityType === 'STAFF',
  // 判断是否为员工商户管理员
  isStaffMERAdmin: (state) => state.global.roleApp?.entityType === 'STAFF'&& state.global.roleApp?.entitySubType === 'MERADMIN',
  // 判断是否为员工部门管理员
  isStaffDEPTAdmin: (state) => state.global.roleApp?.entityType === 'STAFF'&& state.global.roleApp?.entitySubType === 'COMMANDER',
  // 判断是否为推荐官
  isReferer: (state) => state.global.roleApp?.entityType === 'REFERER',
  // 判断是否为集团商户
  isGroupMerchant: (state) => state.global.roleApp?.entityType === 'MER' && state.global.roleApp?.entitySubType === 'GROUP',
  // 判断是否为一级代理商
  isFirstAgent: (state) => state.global.roleApp?.entitySubType === 'FIRST',
  // 判断是否为二级代理商
  isSecondAgent: (state) => state.global.roleApp?.entitySubType === 'SECOND',
  // 判断是否为一级合伙人
  isFirstPartner: (state) => state.global.roleApp?.entityType === 'PARTNER' && state.global.roleApp?.entitySubType === 'FIRST',
  // 判断是否为二级合伙人
  isSecondPartner: (state) => state.global.roleApp?.entityType === 'PARTNER' && state.global.roleApp?.entitySubType === 'SECOND',
};
export default getters;
