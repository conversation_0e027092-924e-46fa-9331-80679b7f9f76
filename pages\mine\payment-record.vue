<template>
  <snowy-layout title="付款记录" :isTabbar="false" :isFirstPage="false">
    <view class="payment-record-container">
      <!-- 搜索栏 -->
      <view class="search-container">
        <view class="search-box">
          <uni-icons type="search" size="18" color="#999"></uni-icons>
          <input class="search-input" placeholder="可输入任务名称、姓名、手机号进行查询" v-model="searchKey" @confirm="onSearch" />
        </view>
      </view>

      <!-- 日期筛选 -->
      <view class="date-filter">
        <uni-datetime-picker
          type="daterange"
          :value="dateRange"
          start-placeholder="创建时间开始日"
          end-placeholder="创建时间结束日"
          rangeSeparator="—"
          @change="onDateRangeChange"
          @clear="onDateRangeClear"
        />
      </view>

      <!-- 付款记录列表 -->
      <scroll-view class="record-list" scroll-y="true" @scrolltolower="loadMore" refresher-enabled
        :refresher-triggered="refreshing" @refresherrefresh="onRefresh">
        <view class="payment-list">
          <view v-for="(item, index) in paymentList" :key="index" class="payment-item">
            <!-- 日期标题 -->
            <view class="payment-date">{{ formatDate(item.createTime) }}</view>

            <!-- 工单名称 -->
            <view class="payment-row">
              <view class="payment-label">工单名称</view>
              <view class="payment-value">{{ item.ticketName || '--' }}</view>
            </view>

            <!-- 产品名称 -->
            <view class="payment-row">
              <view class="payment-label">产品名称</view>
              <view class="payment-value">{{ item.productName || '--' }}</view>
            </view>

            <!-- 项目名称 -->
            <view class="payment-row">
              <view class="payment-label">项目名称</view>
              <view class="payment-value">{{ item.projectName || '--' }}</view>
            </view>

            <!-- 付款金额 -->
            <view class="payment-row">
              <view class="payment-label">付款金额(元)</view>
              <view class="payment-amount">{{ item.amount || '--' }}</view>
            </view>

            <!-- 付款日期 -->
            <view class="payment-row">
              <view class="payment-label">付款日期</view>
              <view class="payment-value">{{ formatDateTime(item.payTime) }}</view>
            </view>

            <!-- 创建人 -->
            <view class="payment-row">
              <view class="payment-label">创建人</view>
              <view class="payment-value">{{ item.createUser || '--' }}</view>
            </view>

            <!-- 付款凭证 -->
            <view class="payment-row">
              <view class="payment-label">付款凭证</view>
            </view>
            <view class="payment-images" v-if="item.paths && item.paths.length > 0">
              <image v-for="(path, imgIndex) in item.paths" :key="imgIndex" :src="path" class="payment-image"
                mode="aspectFill" @click="previewImage(path, item.paths)" />
            </view>
          </view>

          <view v-if="paymentList.length === 0 && !loading" class="empty-state">
            <text>暂无付款记录</text>
          </view>
        </view>

        <!-- 加载状态 -->
        <view v-if="loading" class="loading-state">
          <uni-load-more :status="loadingStatus"></uni-load-more>
        </view>
      </scroll-view>
    </view>

  </snowy-layout>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import bizPaymentApi from '@/api/biz/bizPaymentApi'

// 响应式数据
const searchKey = ref('')
const dateRange = ref([])
const paymentList = ref([])
const loading = ref(false)
const refreshing = ref(false)
const loadingStatus = ref('more')

// 分页参数
const pagination = reactive({
  current: 1,
  size: 10,
  total: 0
})

// 页面加载
onLoad(() => {
  loadPaymentList()
})

// 加载付款记录列表
const loadPaymentList = async (isRefresh = false) => {
  if (loading.value && !isRefresh) return

  loading.value = true
  loadingStatus.value = 'loading'

  try {
    const params = {
      current: isRefresh ? 1 : pagination.current,
      size: pagination.size,
      searchKey: searchKey.value || '',
      startDate: dateRange.value[0] || '',
      endDate: dateRange.value[1] || ''
    }

    const response = await bizPaymentApi.getPaymentPage(params)

    if (response) {
      const newData = response.records || []

      if (isRefresh) {
        paymentList.value = newData
        pagination.current = 1
      } else {
        paymentList.value = [...paymentList.value, ...newData]
      }

      pagination.total = response.total || 0

      // 判断是否还有更多数据
      if (paymentList.value.length >= pagination.total) {
        loadingStatus.value = 'noMore'
      } else {
        loadingStatus.value = 'more'
        pagination.current++
      }
    }
  } catch (error) {
    console.error('加载付款记录失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
    loadingStatus.value = 'more'
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

// 搜索
const onSearch = () => {
  pagination.current = 1
  loadPaymentList(true)
}

// 下拉刷新
const onRefresh = () => {
  refreshing.value = true
  pagination.current = 1
  loadPaymentList(true)
}

// 加载更多
const loadMore = () => {
  if (loadingStatus.value === 'more') {
    loadPaymentList()
  }
}

// 日期范围变化
const onDateRangeChange = (e) => {
  dateRange.value = e
  onSearch()
}

// 日期范围清除
const onDateRangeClear = () => {
  dateRange.value = []
  onSearch()
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '--'
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
}

// 格式化日期时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return '--'
  const date = new Date(dateStr)
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${month}-${day} ${hours}:${minutes}`
}

// 预览图片
const previewImage = (current, urls) => {
  uni.previewImage({
    current,
    urls
  })
}
</script>

<style lang="scss" scoped>
.payment-record-container{
  display: flex;
  flex-direction: column;
  height: 100%;
}
// 搜索容器
.search-container {
  padding: 20rpx;
  background-color: white;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 50rpx;
  padding: 20rpx 24rpx;
  gap: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;

  &::placeholder {
    color: #999;
  }
}

// 日期筛选
.date-filter {
  padding: 20rpx;
  background-color: white;
  border-top: 1rpx solid #f5f5f5;
}

// 记录列表
.record-list {
  flex: 1;
  padding: 20rpx;
  box-sizing: border-box;
  overflow: auto;
}

// 付款记录项样式
.payment-item {
  background-color: white;
  padding: 32rpx 24rpx;
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
}

.payment-date {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
  line-height: 1.4;
}

.payment-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.payment-label {
  font-size: 28rpx;
  color: #666;
  font-weight: 400;
}

.payment-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 400;
  text-align: right;
  flex: 1;
  margin-left: 20rpx;
}

.payment-amount {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  text-align: right;
}

// 付款凭证图片
.payment-images {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 16rpx;
}

.payment-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  border: 1rpx solid #eee;
}

// 空状态
.empty-state {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

// 加载状态
.loading-state {
  padding: 40rpx 0;
}
  :deep(.uni-date-x--border) {
    border-radius: 40rpx !important;
    overflow: hidden;
    padding-left: 20rpx;
  }
</style>
