<template>
  <snowy-layout title="选择合伙人" :isTabbar="false" :isFirstPage="false">
    <view class="partner-select-page">
      <!-- 合伙人列表 -->
      <view class="partner-list">
        <view v-for="(partner, index) in partnerList" :key="index" class="partner-item" @click="selectPartner(index)">
          <view class="radio" :class="{ checked: selectedPartnerIndex === index }">
            <view v-if="selectedPartnerIndex === index" class="radio-dot"></view>
          </view>
          <view class="partner-info">
            <view class="partner-avatar">
              <image :src="partner.avatar || defaultAvatar" mode="aspectFill"></image>
            </view>
            <view class="partner-details">
              <view class="partner-name">{{ partner.name || '未知' }}</view>
              <view class="partner-phone">{{ partner.contactPhone || '未知' }}</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 底部操作栏 -->
      <view class="bottom-bar">
        <text class="selected-count">已选择{{ selectedCodesCount }}个激活码</text>
        <view class="action-btn" :class="{ disabled: selectedPartnerIndex === -1 }" @click="confirmTransfer">
          确认划拨
        </view>
      </view>
    </view>
  </snowy-layout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import bizInventoryApi from '@/api/biz/bizInventoryApi'

const store = useStore()

// 选中的合伙人索引
const selectedPartnerIndex = ref(-1)

// 已选择的激活码ID（从上一页传递过来）
const selectedCdkeyIds = ref([])
const selectedCodesCount = ref(0)

// 合伙人列表数据
const partnerList = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const isLoading = ref(false)

// 默认头像
const defaultAvatar = computed(() => {
  return store.getters.allEnv[store.getters.envKey].image_baseUrl + '/static/images/partner/default-avatar.png'
})

// 选择合伙人
const selectPartner = (index) => {
  selectedPartnerIndex.value = index
}

// 确认划拨
const confirmTransfer = async () => {
  if (selectedPartnerIndex.value === -1) {
    uni.showToast({
      title: '请选择合伙人',
      icon: 'none'
    })
    return
  }

  const selectedPartner = partnerList.value[selectedPartnerIndex.value]

  uni.showModal({
    title: '确认划拨',
    content: `确认将${selectedCodesCount.value}个激活码划拨给${selectedPartner.name}？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          // 调用激活码划拨接口
          const result = await bizInventoryApi.transferCdkey({
            cdkeyId: selectedCdkeyIds.value,
            type: 0, // 0-划拨
            targetPartnerId: selectedPartner.id,
            extJson: ""
          })

          uni.showToast({
            title: '划拨成功',
            icon: 'success'
          })

          // 清除存储的数据
          uni.removeStorageSync('selectedCdkeyIds')

          // 返回到库存页面
          setTimeout(() => {
            uni.navigateBack({
              delta: 2
            })
          }, 1500)
        } catch (error) {
          console.error('划拨失败:', error)
          uni.showToast({
            title: '划拨失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 加载合伙人列表
const loadPartnerList = async () => {
  if (isLoading.value) return

  try {
    isLoading.value = true
    const roleApp = store.getters.roleApp || {}
    const params = {
      current: currentPage.value,
      size: 9999,
      keyword: '', // 合伙人选择页面不需要搜索
      firstPartnerId: roleApp.entityId
    }

    const response = await store.dispatch('partner/getSecondPartnerList', params)

    if (response.success) {
      const { list, total: totalCount } = response.data
      partnerList.value = list || []
      total.value = totalCount
    } else {
      uni.showToast({
        title: response.message || '加载失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('加载合伙人列表失败:', error)
    uni.showToast({
      title: '网络错误，请重试',
      icon: 'none'
    })
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  // 获取从上一页传递的选中激活码ID
  const cdkeyIds = uni.getStorageSync('selectedCdkeyIds') || []
  selectedCdkeyIds.value = cdkeyIds
  selectedCodesCount.value = cdkeyIds.length

  loadPartnerList()
})
</script>

<style lang="scss" scoped>
.partner-select-page {
  height: 100%;
  background-color: #f5f6fa;
  display: flex;
  flex-direction: column;
}

.partner-list {
  flex: 1;
  padding: 20rpx;
  padding-bottom: 120rpx; // 为底部操作栏留出空间
  overflow-y: auto;
  background: rgba(242, 242, 242, 1);
  border-radius: 12rpx 12rpx 12rpx 12rpx;

  .partner-item {
    display: flex;
    align-items: center;
    background: white;
    padding: 15rpx 30rpx;
    margin-bottom: 1rpx;
    border-bottom: 1rpx solid #f5f5f5;

    .radio {
      width: 35rpx;
      height: 35rpx;
      border: 3rpx solid rgba(82, 150, 255, 1);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 30rpx;

      &.checked {
        border-color: rgba(82, 150, 255, 1);

        .radio-dot {
          width: 20rpx;
          height: 20rpx;
          background: rgba(82, 150, 255, 1);
          border-radius: 50%;
        }
      }
    }

    .partner-info {
      flex: 1;
      display: flex;
      align-items: center;

      .partner-avatar {
        width: 64rpx;
        height: 64rpx;
        border-radius: 15rpx;
        overflow: hidden;
        margin-right: 30rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 30rpx;
        image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .partner-details {
        flex: 1;

        .partner-name {
          font-size: 34rpx;
          color: #333;
          font-weight: 500;
          margin-bottom: 12rpx;
        }

        .partner-phone {
          font-size: 28rpx;
          color: #999;
        }
      }
    }
  }
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 100;

  .selected-count {
    font-size: 26rpx;
    color: rgba(82, 150, 255, 1);
  }

  .action-btn {
    background: rgba(82, 150, 255, 1);
    color: white;
    padding: 20rpx 40rpx;
    border-radius: 50rpx;
    font-size: 28rpx;

    &.disabled {
      background: #ccc;
      color: #999;
    }
  }
}
</style>
