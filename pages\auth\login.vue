<template>
	<view class="snowy-page">
		
	</view>
</template>

<script setup>
import { ref, reactive } from 'vue'

const form = ref(null)
const formData = reactive({
	account: '',
	password: ''
})

const rules = {
	account: {
		rules: [{
			required: true,
			errorMessage: '请输入账号'
		}]
	},
	password: {
		rules: [{
			required: true,
			errorMessage: '请输入密码'
		}]
	}
}

const handleLogin = async () => {
	try {
		await form.value?.validate()
		// TODO: 实现登录逻辑
		console.log('登录信息：', formData)
	} catch (e) {
		console.error(e)
	}
}
</script>

<style lang="scss" scoped>
page {
	background-color: #ffffff;
}

.login-container {
	padding: 40rpx;
	height: 100vh;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.login-header {
	text-align: center;
	margin-bottom: 60rpx;

	.logo {
		width: 160rpx;
		height: 160rpx;
		margin-bottom: 20rpx;
	}

	.title {
		font-size: 36rpx;
		color: $snowy-primary;
		font-weight: bold;
	}
}

.login-form {
	width: 100%;
	padding: 40rpx;
	box-sizing: border-box;
}

.login-button {
	width: 100%;
	height: 80rpx;
	line-height: 80rpx;
	background-color: $snowy-primary;
	color: #ffffff;
	border-radius: 10rpx;
	margin-top: 40rpx;
}
</style>
