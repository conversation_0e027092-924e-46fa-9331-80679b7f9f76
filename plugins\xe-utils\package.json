{"name": "xe-utils", "version": "3.5.12", "description": "JavaScript 函数库、工具类", "main": "index.js", "unpkg": "dist/xe-utils.umd.min.js", "jsdelivr": "dist/xe-utils.umd.min.js", "typings": "index.d.ts", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lib": "gulp build", "format": "eslint --fix dist/*.ts", "test": "npm run lib && jest"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^4.18.0", "@typescript-eslint/parser": "^4.18.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^5.1.2", "@vue/eslint-config-typescript": "^7.0.0", "core-js": "^3.6.5", "gulp": "^4.0.2", "gulp-autoprefixer": "^6.1.0", "gulp-babel": "^8.0.0", "gulp-clean-css": "^4.2.0", "gulp-concat": "^2.6.1", "gulp-rename": "^1.4.0", "gulp-replace": "^1.0.0", "gulp-sass": "^4.0.2", "gulp-sourcemaps": "^2.6.5", "gulp-typescript": "^5.0.1", "gulp-uglify": "^3.0.2", "jest": "^25.1.0", "markdown-doctest": "^1.0.0", "highlight.js": "^10.5.0", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^7.0.0", "sass": "^1.52.1", "sass-loader": "^10.0.5", "typescript": "~3.9.3", "vue": "^2.6.14", "vue-router": "^3.2.0", "vuex": "^3.4.0", "vue-template-compiler": "^2.6.14"}, "repository": {"type": "git", "url": "git+https://github.com/x-extends/xe-utils.git"}, "keywords": ["utils", "js-util", "js-tool", "xe-utils", "tools"], "author": {"name": "<PERSON>", "email": "xu_liang<PERSON><PERSON>@163.com"}, "license": "MIT", "bugs": {"url": "https://github.com/x-extends/xe-utils/issues"}, "homepage": "https://github.com/x-extends/xe-utils#readme", "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}