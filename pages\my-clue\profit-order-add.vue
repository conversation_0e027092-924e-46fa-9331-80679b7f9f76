<template>
  <snowy-layout title="新增分佣" :isTabbar="false" :isFirstPage="false">
    <view class="container">
      <view class="form-container">
        <!-- 产品名称 -->
        <view class="form-item">
          <view class="form-item-left">
            <text class="required-mark">*</text>
            <text class="form-label">产品名称</text>
          </view>
          <input class="form-input" type="text" v-model="formData.productName" placeholder="请输入产品名称"
            placeholder-style="color: #999;" />
        </view>

        <!-- 交易金额 -->
        <view class="form-item">
          <view class="form-item-left">
            <text class="required-mark">*</text>
            <text class="form-label">交易金额</text>
          </view>
          <input class="form-input" type="number" v-model="formData.tradeAmount" placeholder="请输入交易金额"
            placeholder-style="color: #999;" @input="calculateReturnAmount" />
        </view>

        <!-- 佣金比例 -->
        <view class="form-item">
          <view class="form-item-left">
            <text class="required-mark">*</text>
            <text class="form-label">佣金比例</text>
          </view>
          <input class="form-input" type="number" v-model="formData.returnRate" placeholder="请输入佣金比例"
            placeholder-style="color: #999;" @input="calculateReturnAmount" />
        </view>

        <!-- 佣金金额 -->
        <view class="form-item">
          <view class="form-item-left">
            <text class="required-mark">*</text>
            <text class="form-label">佣金金额(元)</text>
          </view>
          <input class="form-input" type="number" v-model="formData.returnAmount" placeholder="请输入佣金金额"
            placeholder-style="color: #999;" />
        </view>

        <!-- 备注 -->
        <view class="form-item">
          <view class="form-item-left">
            <text class="form-label">备注</text>
          </view>
        </view>
        <view class="form-input-box textarea-box">
          <textarea class="form-textarea" v-model="formData.remark" placeholder="请输入备注" />
        </view>
      </view>

      <!-- 底部占位，确保内容不被按钮遮挡 -->
      <view class="bottom-placeholder"></view>

      <!-- 提交按钮 -->
      <view class="submit-button-container">
        <view class="submit-button" @click="submitForm">
          <text>确认</text>
        </view>
      </view>
    </view>
  </snowy-layout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import bizProfitOrderApi from '@/api/biz/bizProfitOrderApi'
import { onLoad } from '@dcloudio/uni-app'

// 获取页面参数
let clueId = ''
onMounted(() => {

})
onLoad((option) => {
  clueId = option.clueId || ''
  formData.value.taskClueId = clueId
})
// 表单数据
const formData = ref({
  tradeOrderId: '',
  tradeAmount: '',
  taskId: '',
  taskClueId: '',
  productName: '',
  returnRate: '',
  returnAmount: '',
  remark: ''
})

// 提交中状态
const submitting = ref(false)

// 计算佣金金额
const calculateReturnAmount = () => {
  const tradeAmount = parseFloat(formData.value.tradeAmount) || 0
  const returnRate = parseFloat(formData.value.returnRate) || 0

  if (tradeAmount > 0 && returnRate > 0) {
    formData.value.returnAmount = (tradeAmount * returnRate / 100).toFixed(2)
  }
}

// 提交表单
const submitForm = async () => {
  // 表单验证
  if (!formData.value.productName) {
    uni.showToast({
      title: '请输入产品名称',
      icon: 'none'
    })
    return
  }

  if (!formData.value.tradeAmount || formData.value.tradeAmount <= 0) {
    uni.showToast({
      title: '请输入正确的交易金额',
      icon: 'none'
    })
    return
  }

  if (!formData.value.returnRate || formData.value.returnRate <= 0) {
    uni.showToast({
      title: '请输入正确的佣金比例',
      icon: 'none'
    })
    return
  }

  if (!formData.value.returnAmount || formData.value.returnAmount <= 0) {
    uni.showToast({
      title: '请输入正确的佣金金额',
      icon: 'none'
    })
    return
  }

  // 防止重复提交
  if (submitting.value) return
  submitting.value = true

  try {
    uni.showLoading({
      title: '提交中...'
    })

    // 准备提交参数
    const params = {
      tradeOrderId: formData.value.tradeOrderId,
      tradeAmount: parseFloat(formData.value.tradeAmount),
      taskId: formData.value.taskId,
      taskClueId: formData.value.taskClueId,
      productName: formData.value.productName,
      returnRate: parseFloat(formData.value.returnRate),
      returnAmount: parseFloat(formData.value.returnAmount)
    }

    // 如果有备注则添加
    if (formData.value.remark) {
      params.remark = formData.value.remark
    }

    // 调用接口
    await bizProfitOrderApi.profitOrderApply(params)

    // 提交成功
    uni.hideLoading()
    uni.showToast({
      title: '提交成功',
      icon: 'success',
      success: () => {
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    })
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: error.message || '提交失败',
      icon: 'none'
    })
    console.error('新增分佣失败:', error)
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f1f4f9;
  height: 100%;
  padding: 20rpx 0;
  padding-bottom: 140rpx;
  /* 为固定按钮留出空间 */
  box-sizing: border-box;
}

.form-container {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 0 30rpx 30rpx 30rpx;
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 94rpx;
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }
}

.form-item-left {
  display: flex;
  align-items: center;
}

.required-mark {
  color: #ff0000;
  font-size: 28rpx;
  margin-right: 6rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
}

.form-input {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  flex: 1;
}

.placeholder {
  color: #999;
}

.arrow {
  margin-left: 10rpx;
  color: #999;
}

.bottom-placeholder {
  height: 40rpx;
}

.submit-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.submit-button {
  background-color: #ffcc00;
  color: #333;
  text-align: center;
  height: 92rpx;
  line-height: 92rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 500;
}
.textarea-box {
  padding: 0;
}

.form-textarea {
  width: 100%;
  height: 180rpx;
  font-size: 28rpx;
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  box-sizing: border-box;

  &::placeholder {
    color: #999;
  }

  &:disabled {
    color: #999;
    background-color: #f0f0f0;
  }
}
</style>
