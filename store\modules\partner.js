import partnerApi from '@/api/partner/partner-api'
import storage from '@/utils/storage'
import { STORE_KEY_ENUM } from '@/enum/store-key'

export default {
  namespaced: true,

  state: {
    // 合伙人统计数据
    partnerStats: storage.get(STORE_KEY_ENUM.partnerStats) || {},
    // 二级合伙人列表
    secondPartnerList: [],
    // 合伙人收益数据
    partnerEarnings: storage.get(STORE_KEY_ENUM.partnerEarnings) || {}
  },

  mutations: {
    SET_PARTNER_STATS: (state, data) => {
      state.partnerStats = data
      storage.set(STORE_KEY_ENUM.partnerStats, data)
    },

    SET_SECOND_PARTNER_LIST: (state, data) => {
      state.secondPartnerList = data
    },

    SET_PARTNER_EARNINGS: (state, data) => {
      state.partnerEarnings = data
      storage.set(STORE_KEY_ENUM.partnerEarnings, data)
    }
  },

  actions: {
    /**
     * 新增二级合伙人
     */
    async addSecondPartner ({ commit }, data) {
      try {
        const response = await partnerApi.addSecondPartner(data)
        console.log(response, 'addSecondPartner');
        return {
          success: true,
          data: response,
          message: '新建成功'
        }
      } catch (error) {
        console.error('新增二级合伙人失败:', error)
        return {
          success: false,
          message: '网络错误，请重试'
        }
      }
    },

    /**
     * 获取二级合伙人列表
     */
    async getSecondPartnerList ({ commit }, params) {
      try {
        const response = await partnerApi.getSecondPartnerList(params)

        if (response) {
          commit('SET_SECOND_PARTNER_LIST', response.records || [])
          return {
            success: true,
            data: {
              list: response.records || [],
              total: response.total || 0,
              current: response.current || 1,
              size: response.size || 10
            },
            message: '获取成功'
          }
        } else {
          return {
            success: false,
            message: '获取失败'
          }
        }
      } catch (error) {
        console.error('获取二级合伙人列表失败:', error)
        return {
          success: false,
          message: '网络错误，请重试'
        }
      }
    },

    /**
     * 获取二级合伙人详情
     */
    async getSecondPartnerDetail ({ commit }, params) {
      try {
        const response = await partnerApi.getSecondPartnerDetail(params)

        if (response) {
          return {
            success: true,
            data: response,
            message: '获取成功'
          }
        } else {
          return {
            success: false,
            message: '获取失败'
          }
        }
      } catch (error) {
        console.error('获取二级合伙人详情失败:', error)
        return {
          success: false,
          message: '网络错误，请重试'
        }
      }
    },

    /**
     * 编辑二级合伙人
     */
    async editSecondPartner ({ commit }, data) {
      try {
        const response = await partnerApi.editSecondPartner(data)
        return {
          success: true,
          data: response,
          message: '编辑成功'
        }
      } catch (error) {
        console.error('编辑二级合伙人失败:', error)
        return {
          success: false,
          message: '网络错误，请重试'
        }
      }
    },

    /**
     * 删除二级合伙人
     */
    async deleteSecondPartner ({ commit }, params) {
      try {
        const response = await partnerApi.deleteSecondPartner(params)

        return {
          success: true,
          data: response,
          message: '删除成功'
        }
      } catch (error) {
        console.error('删除二级合伙人失败:', error)
        return {
          success: false,
          message: '网络错误，请重试'
        }
      }
    },

    /**
     * 获取合伙人统计数据
     */
    async getPartnerStats ({ commit }) {
      try {
        const response = await partnerApi.getPartnerStats()

        commit('SET_PARTNER_STATS', response.data)
        return {
          success: true,
          data: response,
          message: '获取成功'
        }
      } catch (error) {
        console.error('获取合伙人统计数据失败:', error)
        return {
          success: false,
          message: '网络错误，请重试'
        }
      }
    },

    /**
     * 获取合伙人收益数据
     */
    async getPartnerEarnings ({ commit }, params) {
      try {
        const response = await partnerApi.getPartnerEarnings(params)

        commit('SET_PARTNER_EARNINGS', response.data)
        return {
          success: true,
          data: response,
          message: '获取成功'
        }
      } catch (error) {
        console.error('获取合伙人收益数据失败:', error)
        return {
          success: false,
          message: '网络错误，请重试'
        }
      }
    }
  },

  getters: {
    // 获取二级合伙人列表
    secondPartnerList: state => state.secondPartnerList,
    // 获取合伙人统计数据
    partnerStats: state => state.partnerStats,
    // 获取合伙人收益数据
    partnerEarnings: state => state.partnerEarnings
  }
}
