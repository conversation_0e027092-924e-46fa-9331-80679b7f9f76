<template>
  <snowy-layout title="我的线索" :isTabbar="true" :isFirstPage="true">
    <view class="clue-container">
      <!-- 顶部标签栏 -->
      <scroll-view class="scroll-view_H" scroll-x="true" scroll-with-animation :show-scrollbar="false">
        <view class="scroll-view-item_H">
          <view v-for="(item, index) in tabList" :key="index" class="tab-item" :class="{ active: currentTab === index }"
            @click="switchTab(index)">
            {{ item.name }}
          </view>
        </view>
      </scroll-view>

      <!-- 搜索区域 -->
      <view class="search-area">
        <view class="search-box">
          <uni-icons type="search" size="18" color="#999"></uni-icons>
          <input type="text" v-model="searchKeyword" placeholder="可输入任务名称、姓名、手机号进行查询"
            placeholder-class="placeholder-style" @confirm="onSearchConfirm" />
        </view>

        <!-- 日期选择区域 -->
        <view class="date-picker">
          <uni-datetime-picker
            type="daterange"
            :value="dateRange"
            start-placeholder="创建时间开始日"
            end-placeholder="创建时间结束日"
            rangeSeparator="-"
            @change="onDateRangeChange"
            @clear="onDateRangeClear"
          />
        </view>
      </view>

      <!-- 线索列表 -->
      <scroll-view class="clue-list" scroll-y="true" @scrolltolower="loadMore" refresher-enabled
        :refresher-triggered="refreshing" @refresherrefresh="onRefresh">
        <view class="clue-item" v-for="(item, index) in clueList" :key="index">
          <view class="clue-header">
            <view class="clue-avatar-name">
              <image class="clue-avatar" :src="item.avatar || '/static/images/default-avatar.png'"></image>
              <view class="clue-info">
                <view class="clue-name">{{ item.taskName }}</view>
                <view class="clue-user">
                  <view class="clue-phone" style="margin-right: 36rpx;">{{ item.customer }}</view>
                  <view class="clue-phone">{{ item.customerPhone }}</view>
                </view>
              </view>
            </view>
            <view class="clue-status" :class="getStatusClass(item.status)">{{ getStatusText(item.status) }}</view>
          </view>

          <!-- 线索详细信息 -->
          <view class="clue-details">
            <view class="detail-row">
              <view class="detail-label">性别</view>
              <view class="detail-value">{{ item.customerSex == 0 ? '男' : '女' }}</view>
            </view>
            <view class="detail-row">
              <view class="detail-label">地址</view>
              <view class="detail-value">{{ item.addr }}</view>
            </view>
            <view class="detail-row">
              <view class="detail-label">兴趣产品</view>
              <view class="detail-value">{{ item.interestProduct }}</view>
            </view>
            <view class="detail-row">
              <view class="detail-label">备注</view>
              <view class="detail-value">{{ item.remark }}</view>
            </view>
            <view class="detail-row">
              <view class="detail-label">创建时间</view>
              <view class="detail-value">{{ item.createTime }}</view>
            </view>
            <view class="detail-row">
              <view class="detail-label">推荐官姓名</view>
              <view class="detail-value">{{ item.referralName }}</view>
            </view>
            <view class="detail-row">
              <view class="detail-label">推荐官手机号码</view>
              <view class="detail-value">{{ item.referralPhone }}</view>
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="clue-actions">
            <view class="action-left">
              <view class="avatar-small">
                <image :src="item.managerAvatar"></image>
              </view>
              <text>{{ item.staffName }}</text>
            </view>
            <view class="action-right">
              <view class="action-btn edit-btn" @click="goToClueDetail(item.id)">编辑</view>
              <view class="action-btn history-btn" v-if="currentTab === 0 || currentTab === 1"
                @click="handleReassign(item.id)">重新分配</view>
              <view class="action-btn history-btn" v-if="currentTab === 0 || currentTab === 2"
                @click="handleHistory(item.id)">分配记录</view>
            </view>
          </view>
        </view>

        <!-- 加载状态 -->
        <view class="loading-more" v-if="loading">加载中...</view>
        <view class="no-more" v-if="noMore">没有更多数据了</view>
      </scroll-view>
    </view>

    <!-- 重新分配弹窗 -->
    <view class="modal-overlay" v-if="showReassignModal" @click.stop="closeReassignModal">
      <view class="reassign-modal" @click.stop>
        <view class="reassign-header">
          <text class="reassign-title">重新分配</text>
          <text class="reassign-close" @click="closeReassignModal">×</text>
        </view>
        <view class="reassign-body">
          <view class="reassign-form-item">
            <text class="reassign-label">分配人员</text>
            <view class="reassign-select" @click="toggleStaffSelector">
              <text class="reassign-select-text">{{ selectedStaff || '此处可单选除自己以外的管理' }}</text>
              <view class="reassign-select-arrow">
                <uni-icons type="bottom" size="16" color="#999"></uni-icons>
              </view>
            </view>
          </view>
          <view class="reassign-form-item reassign-form-item-textarea">
            <text class="reassign-label">备注</text>
            <textarea class="reassign-textarea" v-model="reassignRemark" placeholder="请输入备注"></textarea>
          </view>
        </view>
        <view class="reassign-footer">
          <view class="reassign-btn reassign-btn-cancel" @click="closeReassignModal">取消</view>
          <view class="reassign-btn reassign-btn-confirm" @click="confirmReassign">确定</view>
        </view>
      </view>
    </view>

    <!-- 分配记录弹窗 -->
    <view class="modal-overlay" v-if="showHistoryModal" @click.stop="closeHistoryModal">
      <view class="history-modal" @click.stop>
        <view class="history-header">
          <text class="history-title">分配记录</text>
          <text class="history-close" @click="closeHistoryModal">×</text>
        </view>
        <view class="history-body">
          <view class="history-timeline">
            <view class="history-item" v-for="(item, index) in assignmentHistory" :key="index">
              <!-- <view class="history-dot"></view> -->
              <image class="history-dot" src="/static/images/my/time-jiedian.png" mode="aspectFit"></image>
              <view class="history-line" v-if="index !== assignmentHistory.length - 1"></view>
              <view class="history-time">{{ item.createTime }}</view>
              <view class="history-content">
                <view class="history-text">{{ item.staffName }}分配给{{ item.assignStaffName }}</view>
                <view class="history-remark">
                  <text>备注：{{ item.remarks }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </snowy-layout>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import bizInventoryApi from '@/api/biz/bizInventoryApi'
uni.hideTabBar()
// 标签页数据
const tabList = reactive([
  { name: '全部', status: -1 },
  { name: '待接单', status: 0 },
  { name: '已接单', status: 1 },
  { name: '跟进中', status: 2 },
  { name: '已完结', status: 3 },
  { name: '已失效', status: 4 }
])
const currentTab = ref(0)

// 搜索和筛选条件
const searchKeyword = ref('')
const dateRange = ref([])
const startDate = ref('')
const endDate = ref('')

// 列表数据和分页
const clueList = ref([])
const page = ref(1)
const pageSize = ref(10)
const loading = ref(false)
const noMore = ref(false)
const refreshing = ref(false)

// 重新分配弹窗数据
const showReassignModal = ref(false)
const selectedStaff = ref('')
const selectedStaffId = ref('')
const selectedStaffEntityType = ref('')
const reassignRemark = ref('')
const currentClueId = ref('')
const staffList = ref([])

// 分配记录弹窗数据
const showHistoryModal = ref(false)
const assignmentHistory = ref([])

// 切换标签页
const switchTab = (index) => {
  if (currentTab.value === index) return
  currentTab.value = index
  resetList()
  getClueList()
}

// 重置列表
const resetList = () => {
  page.value = 1
  clueList.value = []
  noMore.value = false
}

// 搜索确认事件
const onSearchConfirm = () => {
  resetList()
  getClueList()
}

// 日期范围选择器事件
const onDateRangeChange = (e) => {
  dateRange.value = e
  if (e && e.length === 2) {
    startDate.value = e[0]
    endDate.value = e[1]
  } else {
    startDate.value = ''
    endDate.value = ''
  }
  resetList()
  getClueList()
}

// 清空日期范围
const onDateRangeClear = () => {
  dateRange.value = []
  startDate.value = ''
  endDate.value = ''
  resetList()
  getClueList()
}

// 下拉刷新
const onRefresh = () => {
  refreshing.value = true
  resetList()
  getClueList().finally(() => {
    refreshing.value = false
  })
}

// 加载更多
const loadMore = () => {
  if (loading.value || noMore.value) return
  page.value++
  getClueList()
}

// 获取线索状态样式
const getStatusClass = (status) => {
  const statusMap = {
    0: 'status-pending',
    1: 'status-accepted',
    2: 'status-progress',
    3: 'status-completed',
    [-1]: 'status-failed',
    [-2]: 'status-failed'
  }
  return statusMap[status] || ''
}

// 获取状态文本
const getStatusText = (status) => {
  const statusTextMap = {
    0: '待接单',
    1: '已接单',
    2: '跟进中',
    3: '已完结',
    [-1]: '已失效',
    [-2]: '已废弃'
  }
  return statusTextMap[status] || '未知状态'
}

// 获取线索列表数据
const getClueList = async () => {
  loading.value = true
  try {
    const params = {
      current: page.value,
      size: pageSize.value
    }

    // 添加搜索关键词
    if (searchKeyword.value) {
      params.searchKey = searchKeyword.value
    }

    // 添加状态筛选（全部时不传status）
    const currentStatus = tabList[currentTab.value].status
    if (currentStatus !== -1) {
      // 处理状态映射
      const statusMap = {
        0: 0,  // 待接单
        1: 1,  // 已接单
        2: 2,  // 跟进中
        3: 3,  // 已完结
        4: -1  // 已失效
      }
      params.status = statusMap[currentStatus]
    }

    // 添加日期筛选
    if (startDate.value) {
      params.createTimeStart = startDate.value
    }
    if (endDate.value) {
      params.createTimeEnd = endDate.value
    }
    params.personal=true
    const response = await bizInventoryApi.getTaskClueList(params)

    if (response) {
      const { records, total } = response

      if (page.value === 1) {
        clueList.value = records || []
      } else {
        clueList.value = [...clueList.value, ...(records || [])]
      }

      // 判断是否还有更多数据
      noMore.value = clueList.value.length >= total
    } else {
      uni.showToast({
        title: response.message || '获取数据失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取线索列表失败', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}



// 打开重新分配弹窗
const openReassignModal = async (clueId) => {
  currentClueId.value = clueId

  try {
    // 获取可分配的员工列表
    const response = await bizInventoryApi.getReassignStaffList({ taskClueId: clueId })

    if (response) {
      staffList.value = response || []
      showReassignModal.value = true
    } else {
      uni.showToast({
        title: response.message || '获取员工列表失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取员工列表失败', error)
    uni.showToast({
      title: '获取员工列表失败',
      icon: 'none'
    })
  }
}

// 关闭重新分配弹窗
const closeReassignModal = () => {
  showReassignModal.value = false
  selectedStaff.value = ''
  selectedStaffId.value = ''
  reassignRemark.value = ''
  staffList.value = []
}

// 切换员工选择器
const toggleStaffSelector = () => {
  if (staffList.value.length === 0) {
    uni.showToast({
      title: '暂无可分配员工',
      icon: 'none'
    })
    return
  }

  // 显示员工选择列表
  const staffNames = staffList.value.map(staff => staff.name)

  uni.showActionSheet({
    itemList: staffNames,
    success: (res) => {
      const selectedIndex = res.tapIndex
      const selectedStaffInfo = staffList.value[selectedIndex]
      selectedStaff.value = selectedStaffInfo.name
      selectedStaffId.value = selectedStaffInfo.id
      selectedStaffEntityType.value = selectedStaffInfo.entityType
    }
  })
}

// 确认重新分配
const confirmReassign = async () => {
  if (!selectedStaffId.value) {
    uni.showToast({
      title: '请选择分配人员',
      icon: 'none'
    })
    return
  }

  try {
    const params = {
      taskClueId: currentClueId.value,
      assignStaffId: selectedStaffId.value,
      remarks: reassignRemark.value || '',
      assignEntityType:selectedStaffEntityType.value,
      extJson: ''
    }

    const response = await bizInventoryApi.reassignTaskClue(params)

    uni.showToast({
      title: '分配成功',
      icon: 'success'
    })
    closeReassignModal()
    // 刷新列表数据
    resetList()
    getClueList()
  } catch (error) {
    console.error('重新分配失败', error)
    uni.showToast({
      title: '分配失败',
      icon: 'none'
    })
  }
}

// 打开分配记录弹窗
const openHistoryModal = async (clueId) => {
  try {
    const response = await bizInventoryApi.getTaskClueDistributeList({ taskClueId: clueId })

    if (response) {
      // 处理分配记录数据
      assignmentHistory.value = (response || []).map(item => ({
        ...item,
        time: item.createTime || '--',
        text: `${item.createUserName || '--'}分配给${item.assignStaffName || '--'}`,
        remark: item.remarks || '无备注'
      }))
    } else {
      uni.showToast({
        title: '获取分配记录失败',
        icon: 'none'
      })
      return
    }
  } catch (error) {
    console.error('获取分配记录失败', error)
    uni.showToast({
      title: '获取分配记录失败',
      icon: 'none'
    })
    return
  }

  showHistoryModal.value = true
}

// 关闭分配记录弹窗
const closeHistoryModal = () => {
  showHistoryModal.value = false
}

// 修改操作按钮的点击事件
const handleReassign = (clueId) => {
  openReassignModal(clueId)
}

const handleHistory = (clueId) => {
  openHistoryModal(clueId)
}

// 跳转到线索详情页
const goToClueDetail = (clueId) => {
  uni.navigateTo({
    url: `/pages/my-clue/detail?id=${clueId}`
  })
}

onMounted(() => {
  getClueList()
})
</script>

<style lang="scss" scoped>
.clue-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f5f5;
}

// 顶部标签栏
.scroll-view_H {
  width: 100%;
  white-space: nowrap;
  background-color: #fff;
  // border-bottom: 1px solid #eee;
}

.scroll-view-item_H {
  display: inline-flex;
}

.tab-item {
  display: inline-block;
  padding: 0 30rpx;
  height: 73rpx;
  line-height: 88rpx;
  font-size: 28rpx;
  color: #333;
  position: relative;
  white-space: nowrap;

  &.active {
    color: #ffaa00;
    font-weight: bold;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 30rpx;
      right: 30rpx;
      height: 4rpx;
      background-color: #ffaa00;
    }
  }
}

// 搜索区域
.search-area {
  padding: 20rpx;
  background-color: #fff;
}

.search-box {
  display: flex;
  align-items: center;
  height: 70rpx;
  background-color: #f5f5f5;
  border-radius: 35rpx;
  padding: 0 30rpx;
  margin-bottom: 20rpx;

  input {
    flex: 1;
    height: 70rpx;
    font-size: 26rpx;
    margin-left: 10rpx;
  }

  .placeholder-style {
    color: #999;
    font-size: 26rpx;
  }
}

// 日期选择区域
.date-picker {
  margin-top: 10rpx;

  :deep(.uni-datetime-picker) {
    width: 100%;
  }

  :deep(.uni-datetime-picker--btn) {
    height: 70rpx;
    background-color: #f5f5f5;
    border-radius: 4rpx;
    border: none;
    font-size: 26rpx;
    color: #666;
    padding: 0 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  :deep(.uni-datetime-picker--btn:after) {
    border: none;
  }

  :deep(.uni-datetime-picker__input-text) {
    font-size: 26rpx;
    color: #666;
  }

  :deep(.uni-datetime-picker__input-placeholder) {
    font-size: 26rpx;
    color: #999;
  }
}

// 线索列表
.clue-list {
  flex: 1;
  overflow: hidden;
  padding: 26rpx;
  box-sizing: border-box;
}

.clue-item {
  background-color: #fff;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.clue-header {
  padding: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f5f5f5;
}

.clue-avatar-name {
  display: flex;
  align-items: center;
}

.clue-avatar {
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.clue-info {
  display: flex;
  flex-direction: column;
}

.clue-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.clue-user {
  display: flex;
  align-items: center;
  margin-top: 14rpx;
}

.clue-phone {
  font-size: 24rpx;
  color: #666;
}

.clue-status {
  font-size: 26rpx;
  padding: 6rpx 20rpx;
  border-radius: 30rpx;

  &.status-pending {
    color: #ff5722;
    // background-color: rgba(255, 87, 34, 0.1);
  }

  &.status-accepted {
    color: #ff9800;
    // background-color: rgba(255, 152, 0, 0.1);
  }

  &.status-progress {
    color: #2196f3;
    // background-color: rgba(33, 150, 243, 0.1);
  }

  &.status-completed {
    color: #4caf50;
    // background-color: rgba(76, 175, 80, 0.1);
  }

  &.status-failed {
    color: #9e9e9e;
    // background-color: rgba(158, 158, 158, 0.1);
  }
}

// 线索详细信息
.clue-details {
  padding: 10rpx 20rpx;
}

.detail-row {
  display: flex;
  font-size: 26rpx;
  padding: 12rpx 0;
}

.detail-label {
  width: 180rpx;
  color: #666;
}

.detail-value {
  flex: 1;
  color: #333;
  text-align: right;
}

// 操作按钮
.clue-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-top: 1px solid #f5f5f5;
}

.action-left {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
}

.avatar-small {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 10rpx;

  image {
    width: 100%;
    height: 100%;
  }
}

.action-right {
  display: flex;
}

.action-btn {
  height: 60rpx;
  line-height: 60rpx;
  padding: 0 30rpx;
  font-size: 26rpx;
  border-radius: 30rpx;
  margin-left: 15rpx;
}

.edit-btn {
  color: #666;
  background-color: #f5f5f5;
}

.reassign-btn {
  color: #fff;
  background-color: #ffaa00;
}

.history-btn {
  color: #fff;
  background-color: #ff9500;
}

// 加载状态
.loading-more,
.no-more {
  text-align: center;
  font-size: 24rpx;
  color: #999;
  padding: 20rpx 0;
}

// 弹窗样式
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 重新分配弹窗样式
.reassign-modal {
  width: 87%;
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.reassign-header {
  position: relative;
  padding: 40rpx 0 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.reassign-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
}

.reassign-close {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  font-size: 38rpx;
  color: #999;
  line-height: 1;
}

.reassign-body {
  padding: 20rpx 40rpx 60rpx;
}

.reassign-form-item {
  margin-bottom: 40rpx;
}

.reassign-form-item-textarea {
  margin-bottom: 0;
}

.reassign-label {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.reassign-select {
  position: relative;
  height: 90rpx;
  border: 1px solid #e6e6e6;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
}

.reassign-select-text {
  font-size: 28rpx;
  color: #999;
}

.reassign-select-arrow {
  display: flex;
  align-items: center;
}

.reassign-textarea {
  width: 100%;
  height: 180rpx;
  border: 1px solid #e6e6e6;
  border-radius: 12rpx;
  font-size: 28rpx;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
}

.reassign-footer {
  display: flex;
  padding: 0 40rpx 40rpx;
  justify-content: space-between;
}

.reassign-btn {
  width: 48%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 32rpx;
  border-radius: 45rpx;
}

.reassign-btn-cancel {
  background-color: #f5f5f5;
  color: #333;
}

.reassign-btn-confirm {
  background-color: #ffcb00;
  color: #333;
}

// 分配记录弹窗样式
.history-modal {
  width: 88%;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  max-height: 75vh;
}

.history-header {
  position: relative;
  height: 90rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.history-title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333;
}

.history-close {
  position: absolute;
  right: 30rpx;
  top: 20rpx;
  font-size: 36rpx;
  color: #999;
  line-height: 1;
}

.history-body {
  padding: 20rpx 30rpx;
  overflow-y: auto;
  max-height: calc(75vh - 90rpx);
}

.history-timeline {
  position: relative;
}

.history-item {
  position: relative;
  padding-left: 50rpx;
  margin-bottom: 40rpx;
}

.history-dot {
  position: absolute;
  left: 2rpx;
  top: 12rpx;
  width: 30rpx;
  height: 30rpx;
  // background-color: #ffcb00;
  border-radius: 50%;
  z-index: 2;
}

.history-line {
  position: absolute;
  left: 17rpx;
  top: 28rpx;
  width: 1rpx;
  height: calc(100% + 20rpx);
  z-index: 1;
  border-left: 1rpx dashed #e5e5e5;
}

.history-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 15rpx;
}

.history-text {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
}

.history-remark {
  background-color: #f5f5f5;
  padding: 20rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}
:deep(.uni-date-x--border){
  border-radius: 40rpx !important;
  overflow: hidden;
  padding-left: 20rpx;
}
</style>
