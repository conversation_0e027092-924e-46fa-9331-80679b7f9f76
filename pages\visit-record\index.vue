<!--
 * @Descripttion: 访问记录页面
 * @version:
 * @Author: 
 * @Date: 2025-02-19 15:00:00
 * @LastEditors: 
 * @LastEditTime: 2025-02-19 15:00:00
-->
<template>
  <snowy-layout title="访问记录" :isTabbar="true" :isFirstPage="true">
    <view class="visit-record-container">
      <!-- 查询条件 -->
      <view class="filter-container">
        <scroll-view class="date-filter" scroll-x="true" show-scrollbar="false">
          <view class="date-filter-box">
            <view
              class="date-tab"
              v-for="(item, index) in dateFilters"
              :key="index"
              :class="{ active: currentDateFilter === item.value }"
              @click="handleDateFilter(item.value)"
            >
              {{ item.label }}
            </view>
          </view>
        </scroll-view>

        <view class="type-filter">
          <view
            class="type-btn"
            v-for="(item, index) in typeFilters"
            :key="index"
            :class="{ active: currentTypeFilter === item.value }"
            @click="handleTypeFilter(item.value)"
          >
            {{ item.label }}
            <template v-if="item.value === 'REFFER'">
              ({{ accessStats.refferCount||0 }})
            </template>
            <template v-if="item.value === 'OTHER'">
              ({{ accessStats.otherCount||0 }})
            </template>
          </view>
        </view>
      </view>

      <!-- 下拉刷新 -->
      <scroll-view
        scroll-y
        class="record-scroll"
        @scrolltolower="loadMore"
        refresher-enabled
        :refresher-triggered="isRefreshing"
        @refresherrefresh="onRefresh"
      >
        <!-- 访问记录列表 -->
        <view class="record-list">
          <template v-if="visitRecords.length > 0">
            <!-- 记录项 -->
            <view class="record-item" v-for="(record, index) in visitRecords" :key="index">
              <view class="record-header">
                <image class="time-dot-img" src="/static/images/my/time-jiedian.png" mode="aspectFit"></image>
                <text class="time-text">{{ record.time }}</text>
              </view>

              <view class="record-body">
                <view class="timeline-container">
                  <view class="time-line"></view>
                </view>

                <view class="record-content">
                  <view class="record-card">
                    <!-- 用户信息 -->
                    <view class="user-info">
                      <image
                        class="user-avatar"
                        :class="{ 'default-avatar': !record.user?.avatar }"
                        :src="record.user?.avatar || '/static/images/default-avatar.png'"
                        mode="aspectFill"
                      ></image>
                      <view class="user-text">
                        <template v-if="record.user?.isWechat">
                          <text class="user-name">微信号：{{ record.user?.name }}</text>
                        </template>
                        <template v-else>
                          <text class="user-name">{{ record.user?.name || '用户' }}</text>
                          <text class="user-phone" v-if="record.user?.phone">{{ record.user?.phone }}</text>
                        </template>
                        <text class="user-type" v-if="record.entityType === 'REFERER'">推荐官</text>
                        <text class="user-type" v-if="record.entityType === 'OTHER'">其他客户</text>
                      </view>
                      <view class="flag-icon">
                        <image
                          :src="record.flagged ? '/static/images/home/<USER>' : '/static/images/home/<USER>'"
                          mode="aspectFit"
                          @click="selectUser(record, index)"
                        ></image>
                      </view>
                    </view>

                    <!-- 访问详情 -->
                    <view class="visit-detail">
                      <text class="visit-text">{{ record.content }}</text>
                    </view>

                    <!-- 备注信息 -->
                    <view class="remark-box">
                      <text class="remark-label">备注：</text>
                      <text class="remark-text">{{ record.remark }}</text>
                      <text class="edit-btn" @click="editRemark(record)">修改</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </template>

          <!-- 空状态 -->
          <view class="empty-state" v-if="visitRecords.length === 0 && !isLoading">
            <image class="empty-image" src="/static/images/empty-data.png" mode="aspectFit"></image>
            <text class="empty-text">暂无访问记录</text>
          </view>

          <!-- 加载状态 -->
          <view class="loading-state" v-if="isLoading">
            <text class="loading-text">加载中...</text>
          </view>

          <!-- 加载更多 -->
          <view class="load-more" v-if="!isLoading && hasMore && visitRecords.length > 0">
            <text class="load-more-text" @click="loadMore">加载更多</text>
          </view>

          <!-- 没有更多 -->
          <view class="no-more" v-if="!isLoading && !hasMore && visitRecords.length > 0">
            <text class="no-more-text">— 没有更多记录了 —</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 日期范围选择弹窗 -->
    <uni-popup ref="datePopup" type="center">
      <view class="date-popup-content">
        <view class="popup-header">
          <text class="popup-title">选择日期范围</text>
          <text class="popup-close" @click="closeDatePopup">×</text>
        </view>
        <view class="popup-body">
          <uni-datetime-picker
            type="daterange"
            v-model="dateRangeValue"
            rangeSeparator="至"
            start="2020-01-01"
            :end="currentDateFormatted"
            :clear-icon="false"
          />
        </view>
        <view class="popup-footer">
          <button class="popup-btn cancel-btn" @click="closeDatePopup">取消</button>
          <button class="popup-btn confirm-btn" @click="confirmDateRange">确定</button>
        </view>
      </view>
    </uni-popup>
  </snowy-layout>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from 'vue'
import bizClueApi from '@/api/biz/bizClueApi'
// uni组件
import uniPopup from '@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue'
import uniDatetimePicker from '@/uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.vue'
// 隐藏原生tabBar
uni.hideTabBar()

// 过滤选项
const dateFilters = [
  { label: '全部', value: 'all' },
  { label: '当天', value: 'today' },
  { label: '最近7天', value: 'week' },
  { label: '最近一个月', value: 'month' },
  { label: '更多', value: 'more' }
]

const typeFilters = [
  { label: '全部', value: 'all' },
  { label: '推荐官访问', value: 'REFFER' },
  { label: '其他客户访问', value: 'OTHER' }
]

// 访问统计数据
const accessStats = ref({
  refferCount: 0,
  otherCount: 0
})

// 当前选中的过滤条件
const currentDateFilter = ref('all')
const currentTypeFilter = ref('all')

// 日期范围
const dateRange = reactive({
  startDate: '',
  endDate: ''
})

// 日期范围选择器的值
const dateRangeValue = ref([])

// 当前日期格式化字符串，用于日期选择器的结束日期限制
const currentDateFormatted = computed(() => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
})

// 日期选择弹窗引用
const datePopup = ref(null)

// 当前选中的用户ID
const selectedUserId = ref('')

// 计算日期范围
const calculateDateRange = (filterType) => {
  const now = new Date()
  let start = new Date()
  let end = new Date()

  switch (filterType) {
    case 'today':
      // 当天的开始时间
      start.setHours(0, 0, 0, 0)
      break
    case 'week':
      // 最近7天的开始时间
      start.setDate(now.getDate() - 6)
      start.setHours(0, 0, 0, 0)
      break
    case 'month':
      // 最近一个月的开始时间
      start.setMonth(now.getMonth() - 1)
      start.setHours(0, 0, 0, 0)
      break
    case 'more':
      // "更多"选项不设置日期范围，可能会打开日期选择器
      return
    default:
      // 全部，不设置日期范围
      start = null
      end = null
      break
  }

  if (start) {
    dateRange.startDate = formatDate(start)
  } else {
    dateRange.startDate = ''
  }

  if (end) {
    dateRange.endDate = formatDate(end)
  } else {
    dateRange.endDate = ''
  }
}

// 格式化日期为 yyyy-MM-dd 格式
const formatDate = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 处理日期过滤
const handleDateFilter = (value) => {
  currentDateFilter.value = value

  if (value === 'more') {
    // 显示日期范围选择弹窗
    showDateRangePicker()
  } else {
    calculateDateRange(value)
    fetchVisitRecords(true)
  }
}

// 显示日期范围选择器
const showDateRangePicker = () => {
  // 如果已经有日期范围，则显示已有的值
  if (dateRange.startDate && dateRange.endDate) {
    dateRangeValue.value = [dateRange.startDate, dateRange.endDate]
  } else {
    // 默认显示最近7天
    dateRangeValue.value = calculateDefaultRange()
  }

  // 显示弹窗
  datePopup.value.open()
}

// 计算默认显示的日期范围（最近7天）
const calculateDefaultRange = () => {
  const end = new Date()
  const start = new Date()
  start.setDate(end.getDate() - 6)

  return [formatDate(start), formatDate(end)]
}

// 关闭日期弹窗
const closeDatePopup = () => {
  datePopup.value.close()

  // 如果没有选择过日期范围，恢复为"全部"
  if (!dateRange.startDate || !dateRange.endDate) {
    currentDateFilter.value = 'all'
  }
}

// 确认日期范围选择
const confirmDateRange = () => {
  if (dateRangeValue.value && dateRangeValue.value.length === 2) {
    dateRange.startDate = dateRangeValue.value[0]
    dateRange.endDate = dateRangeValue.value[1]

    // 关闭弹窗
    datePopup.value.close()

    // 查询记录
    fetchVisitRecords(true)
  } else {
    uni.showToast({
      title: '请选择完整的日期范围',
      icon: 'none'
    })
  }
}

// 处理类型过滤
const handleTypeFilter = (value) => {
  currentTypeFilter.value = value
  fetchVisitRecords(true)
}

// 访问记录列表
const visitRecords = ref([])

// 加载状态
const isLoading = ref(false)
const isRefreshing = ref(false)
const hasMore = ref(true)

// 分页参数
const pageParams = reactive({
  current: 1,
  size: 10,
  total: 0,
  pages: 0
})

// 格式化时长
const formatDuration = (seconds) => {
  if (!seconds) return '0秒'
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60

  let result = ''
  if (hours > 0) {
    result += hours + '小时'
  }
  if (minutes > 0) {
    result += minutes + '分'
  }
  if (remainingSeconds > 0) {
    result += remainingSeconds + '秒'
  }
  return result
}

// 获取访问记录数据
const fetchVisitRecords = (refresh = false) => {
  if (refresh) {
    pageParams.current = 1
    visitRecords.value = []
    hasMore.value = true
  }
  fetchAccessStats()
  if (!hasMore.value && !refresh) return

  isLoading.value = true

  bizClueApi
    .accessRecordQueryAll({
      current: pageParams.current,
      size: pageParams.size,
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
      userType: currentTypeFilter.value !== 'all' ? currentTypeFilter.value : '',
      userId: selectedUserId.value || ''
    })
    .then((res) => {
      // 处理分页数据
      if (res && res.records) {
        const records = res.records || []

        // 更新分页信息
        pageParams.total = res.total || 0
        pageParams.pages = res.pages || 0

        // 处理返回的数据
        const formattedRecords = records.map((item) => {
          let content = ''
          if (item.action === '1') {
            content = `打开${item.keyword || ''}名片`
          } else if (item.action === '2') {
            content = `第${item.count || 1}次浏览产品《${item.keyword || ''}》，观看了${formatDuration(item.cost || 0)}。`
          } else if (item.action === '3') {
            content = `第${item.count || 1}次浏览企业动态《${item.keyword || ''}》，观看了${formatDuration(item.cost || 0)}。`
          } else if (item.action === '4') {
            content = `第${item.count || 1}次浏览宣传册《${item.keyword || ''}》，观看了${formatDuration(item.cost || 0)}。`
          } else if (item.action === '5') {
            content = `第${item.count || 1}次浏览案例《${item.keyword || ''}》，观看了${formatDuration(item.cost || 0)}。`
          } else if (item.action === '6') {
            content = `将${item.keyword || ''}的名片分享给好友`
          } else if (item.action === '7') {
            content = `保存通讯录`
          }

          return {
            uuid: item.uuid,
            userId: item.userId,
            time: item.startDate || '',
            content: content,
            user: {
              name: item.cname || item.name || '',
              avatar: item.cavatar || '',
              phone: item.cphone || '',
              isWechat: !!item.wxName
            },
            remark: item.remark || '',
            flagged: false,
            entityType: item.entityType || ''
          }
        })

        if (refresh) {
          visitRecords.value = formattedRecords
        } else {
          visitRecords.value = [...visitRecords.value, ...formattedRecords]
        }

        // 是否还有更多数据
        hasMore.value = pageParams.current < pageParams.pages

        // 更新页码
        if (records.length > 0) {
          pageParams.current++
        }
      } else {
        // 处理没有分页结构的情况
        const records = res || []

        // 处理返回的数据
        const formattedRecords = records.map((item) => {
          let content = ''
          if (item.action === '1') {
            content = `打开${item.keyword || ''}名片`
          } else if (item.action === '2') {
            content = `第${item.count || 1}次浏览产品《${item.keyword || ''}》，观看了${formatDuration(item.cost || 0)}。`
          } else if (item.action === '3') {
            content = `第${item.count || 1}次浏览企业动态《${item.keyword || ''}》，观看了${formatDuration(item.cost || 0)}。`
          } else if (item.action === '4') {
            content = `第${item.count || 1}次浏览宣传册《${item.keyword || ''}》，观看了${formatDuration(item.cost || 0)}。`
          } else if (item.action === '5') {
            content = `第${item.count || 1}次浏览案例《${item.keyword || ''}》，观看了${formatDuration(item.cost || 0)}。`
          } else if (item.action === '6') {
            content = `将${item.keyword || ''}的名片分享给好友`
          } else if (item.action === '7') {
            content = `保存通讯录`
          }

          return {
            uuid: item.uuid,
            time: item.startDate || '',
            content: content,
            user: {
              name: item.cname || item.name || '',
              avatar: item.cavatar || '',
              phone: item.cphone || '',
              isWechat: !!item.wxName
            },
            remark: item.remark || '',
            flagged: false
          }
        })

        // 是否还有更多数据
        hasMore.value = records.length === pageParams.size

        // 更新页码
        if (records.length > 0) {
          pageParams.current++
        }
      }
    })
    .catch((err) => {
      console.error('获取访问记录失败', err)
      uni.showToast({
        title: '获取访问记录失败',
        icon: 'none'
      })
    })
    .finally(() => {
      isLoading.value = false
      isRefreshing.value = false
    })
}

// 下拉刷新
const onRefresh = () => {
  isRefreshing.value = true
  fetchVisitRecords(true)
  fetchAccessStats()
}

// 加载更多
const loadMore = () => {
  if (isLoading.value || !hasMore.value) return
  fetchVisitRecords()
}

// 编辑备注
const editRemark = (val) => {
  console.log(val)
  uni.showModal({
    title: '编辑备注',
    editable: true,
    content: val.remark || '',
    placeholderText: '请输入备注内容',
    success: (res) => {
      if (res.confirm) {
        console.log('用户点击确定', res.content)
        // 更新备注内容
        const record = visitRecords.value.find((item) => item.uuid === val.uuid)
        if (record) {
          record.remark = res.content

          // 保存备注到服务器
          bizClueApi
            .saveRemark({
              merchantId: uni.getStorageSync('userInfo')?.merchantId,
              uuid: val.uuid,
              remark: res.content
            })
            .then(() => {
              uni.showToast({
                title: '备注保存成功',
                icon: 'success'
              })
            })
            .catch((err) => {
              console.error('保存备注失败', err)
              uni.showToast({
                title: '保存备注失败',
                icon: 'none'
              })
            })
        }
      }
    }
  })
}

// 选择用户进行筛选
const selectUser = (record, index) => {
  // 切换标记状态
  record.flagged = !record.flagged

  if (record.flagged) {
    console.log('选中该用户', record)
    // 选中该用户
    selectedUserId.value = record.userId

    // 将其他记录的标记状态重置
    visitRecords.value.forEach((item, idx) => {
      if (idx !== index) {
        console.log('item', idx, index, idx !== index)
        item.flagged = false
      }
    })

    // 重新获取数据
    fetchVisitRecords(true)
  } else {
    // 取消选中
    selectedUserId.value = ''

    // 重新获取数据
    fetchVisitRecords(true)
  }
}

// 获取访问统计数据
const fetchAccessStats = async () => {
  try {
    const res = await bizClueApi.statMerAccessRecord({
      startDate: dateRange.startDate,
      endDate: dateRange.endDate,
      userType: currentTypeFilter.value !== 'all' ? currentTypeFilter.value : '',
      userId: selectedUserId.value || ''
    })
    if (res) {
      accessStats.value.refferCount = res.refferCount || 0
      accessStats.value.otherCount = res.otherCount || 0
    }
  } catch (error) {
    console.error('获取访问统计数据失败', error)
  }
}

onMounted(() => {
  // 初始加载数据
  fetchVisitRecords(true)
  // 获取访问统计数据
  fetchAccessStats()
})
</script>

<style lang="scss" scoped>
.visit-record-container {
  height: calc(100%);
  background-color: #f5f7fa;
  padding-bottom: 30rpx;
  display: flex;
  flex-direction: column;
}

// 查询条件样式
.filter-container {
  padding-bottom: 20rpx;
}

.date-filter {
  display: flex;
  white-space: nowrap;
  background-color: #fff;
  border-bottom: 1px solid #f0f0f0;
  // padding: 20rpx 0 0 0;
  height: 80rpx;
}

.date-filter-box {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 80rpx;
  justify-content: space-between;
}

.date-tab {
  display: inline-block;
  padding: 0 30rpx;
  font-size: 28rpx;
  color: #333;
  line-height: 40rpx;
  position: relative;
  text-align: center;
}

.date-tab.active {
  font-weight: 500;
  color: #f2c94c;
}

.date-tab.active::after {
  content: '';
  position: absolute;
  bottom: -12rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #f2c94c;
  border-radius: 3rpx;
}

.type-filter {
  display: flex;
  padding: 30rpx 30rpx 10rpx;
  flex-wrap: wrap;
}

.type-btn {
  padding: 12rpx 24rpx;
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
  border-radius: 100rpx;
  background-color: white;
  margin-bottom: 10rpx;
  border: 1rpx solid transparent;
}

.type-btn.active {
  background-color: #fff6e1;
  color: #f2c94c;
  border: 1rpx solid #f2c94c;
}

.record-scroll {
  // height: calc(100vh - 205rpx);
  flex: 1;
  overflow: hidden;
}

.record-list {
  padding: 0 30rpx;
}

.record-item {
  margin-bottom: 30rpx;
}

.record-header {
  display: flex;
  align-items: center;
  margin-bottom: 6rpx;
}

.time-dot-img {
  width: 32rpx;
  height: 32rpx;
}

.time-text {
  font-size: 26rpx;
  color: #999;
  margin-left: 10rpx;
}

.record-body {
  display: flex;
}

.timeline-container {
  width: 40rpx;
  position: relative;
}

.time-line {
  position: absolute;
  width: 2rpx;
  height: 100%;
  border-left: 2rpx dashed #e0e0e0;
  top: 0;
  bottom: 0;
  left: 16rpx;
}

.record-content {
  flex: 1;
  padding-left: 20rpx;
}

.record-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  margin-top: 20rpx;

  &.empty-card {
    height: 100rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.empty-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  background-color: #f0f0f0;

  &.default-avatar {
    background-color: #e0e0e0;
  }
}

.user-text {
  flex: 1;
}

.user-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-right: 10rpx;
}

.user-phone {
  font-size: 28rpx;
  color: #666;
  margin-left: 12rpx;
}

.user-type {
  margin-left: 12rpx;
  background: rgba(255, 209, 0, 0.15);
  border-radius: 6rpx 6rpx 6rpx 6rpx;
  font-size: 22rpx;
  color: #ffd100;
  padding: 10rpx;
}

.flag-icon {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;

  image {
    width: 32rpx;
    height: 32rpx;
  }
}

.visit-detail {
  margin-bottom: 20rpx;
}

.visit-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.remark-box {
  background-color: #f5f7fa;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.remark-label {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.remark-text {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

.edit-btn {
  font-size: 26rpx;
  color: #f2c94c;
  position: absolute;
  right: 24rpx;
}

// 加载状态
.loading-state {
  text-align: center;
  padding: 30rpx 0;
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

// 加载更多
.load-more {
  text-align: center;
  padding: 30rpx 0;
}

.load-more-text {
  font-size: 26rpx;
  color: #1989fa;
}

// 没有更多
.no-more {
  text-align: center;
  padding: 30rpx 0;
}

.no-more-text {
  font-size: 26rpx;
  color: #999;
}

// 日期范围选择弹窗
.date-popup-content {
  width: 650rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f0f0f0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.popup-close {
  font-size: 40rpx;
  color: #999;
  line-height: 40rpx;
  padding: 0 10rpx;
}

.popup-body {
  padding: 30rpx;
}

.popup-footer {
  display: flex;
  justify-content: flex-end;
  padding: 20rpx 30rpx 30rpx;
  border-top: 1px solid #f0f0f0;
}

.popup-btn {
  margin: 0;
  padding: 0;
  width: 160rpx;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  font-size: 28rpx;
  border-radius: 8rpx;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
  margin-right: 20rpx;
}

.confirm-btn {
  background-color: #f2c94c;
  color: #fff;
}
</style>
