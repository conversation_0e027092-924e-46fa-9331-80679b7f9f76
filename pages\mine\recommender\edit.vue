<!--
 * @Descripttion: 修改推荐官页面
 * @Author: <PERSON>
 * @Date: 2025-07-15 16:50:00
 * @LastEditors: <PERSON>
 * @LastEditTime: 2025-07-15 16:50:00
-->
<template>
  <snowy-layout title="修改推荐官信息" :isTabbar="false" :isFirstPage="false">
    <view class="container">
      <view class="form-container">
        <!-- 推荐官姓名 -->
        <view class="form-item">
          <view class="form-item-left">
            <text class="required-mark">*</text>
            <text class="form-label">推荐官姓名</text>
          </view>
          <input
            class="form-input"
            type="text"
            v-model="formData.name"
            placeholder="请输入推荐官姓名"
            placeholder-style="color: #999;"
          />
        </view>
        
        <!-- 性别 -->
        <view class="form-item">
          <view class="form-item-left">
            <text class="required-mark">*</text>
            <text class="form-label">性别</text>
          </view>
          <view class="form-input" @click="showGenderPicker">
            <text v-if="formData.sex === ''" class="placeholder">请选择性别</text>
            <text v-else>{{ formData.sex === 0 ? '男' : '女' }}</text>
            <text class="arrow">></text>
          </view>
        </view>
        
        <!-- 身份证号码 -->
        <view class="form-item">
          <view class="form-item-left">
            <text class="form-label">身份证号码</text>
          </view>
          <input
            class="form-input"
            type="idcard"
            v-model="formData.idCard"
            placeholder="请输入证件号码"
            placeholder-style="color: #999;"
          />
        </view>
        
        <!-- 手机号码 -->
        <view class="form-item">
          <view class="form-item-left">
            <text class="required-mark">*</text>
            <text class="form-label">手机号码</text>
          </view>
          <input
            class="form-input"
            type="number"
            v-model="formData.mobile"
            placeholder="请输入手机号码"
            placeholder-style="color: #999;"
          />
        </view>
      </view>
      
      <!-- 底部占位，确保内容不被按钮遮挡 -->
      <view class="bottom-placeholder"></view>
      
      <!-- 提交按钮 -->
      <view class="submit-button-container">
        <view class="submit-button" @click="handleSubmit">
          <text>保存</text>
        </view>
      </view>
    </view>
  </snowy-layout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import bizMiniApi from '@/api/biz/bizMiniApi'

// 表单数据
const formData = ref({
  id: '',
  name: '',
  sex: '',
  idCard: '',
  mobile: '',
  type: 0 // 默认为外部推荐官
})

// 提交中状态
const submitting = ref(false)
// 加载中状态
const loading = ref(false)

// 显示性别选择器
const showGenderPicker = () => {
  uni.showActionSheet({
    itemList: ['男', '女'],
    success: function(res) {
      formData.value.sex = res.tapIndex
    }
  })
}

// 获取推荐官详情
const getReferralDetail = async (id) => {
  if (loading.value) return
  loading.value = true
  
  try {
    uni.showLoading({
      title: '加载中...'
    })
    
    // 这里需要添加获取详情的API
    const res = await bizMiniApi.bizMiniReferralDetail({ id })
    
    if (res) {
      formData.value = {
        id: res.id,
        name: res.name,
        sex: res.sex,
        idCard: res.idCard || '',
        mobile: res.mobile,
        type: res.type || 0
      }
    }
    
    uni.hideLoading()
  } catch (error) {
    console.error('获取推荐官详情失败:', error)
    uni.hideLoading()
    uni.showToast({
      title: error.message || '获取数据失败',
      icon: 'none'
    })
    // 获取失败返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  } finally {
    loading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  // 表单验证
  if (!formData.value.name) {
    uni.showToast({
      title: '请输入推荐官姓名',
      icon: 'none'
    })
    return
  }
  
  if (formData.value.sex === '') {
    uni.showToast({
      title: '请选择性别',
      icon: 'none'
    })
    return
  }
  
  if (!formData.value.mobile) {
    uni.showToast({
      title: '请输入手机号码',
      icon: 'none'
    })
    return
  }
  
  // 手机号码验证
  if (!/^1[3-9]\d{9}$/.test(formData.value.mobile)) {
    uni.showToast({
      title: '请输入正确的手机号码',
      icon: 'none'
    })
    return
  }
  
  // 身份证号码验证（如果有填写）
  if (formData.value.idCard && !/^\d{17}[\dXx]$/.test(formData.value.idCard)) {
    uni.showToast({
      title: '请输入正确的身份证号码',
      icon: 'none'
    })
    return
  }
  
  // 防止重复提交
  if (submitting.value) return
  submitting.value = true
  
  try {
    uni.showLoading({
      title: '保存中...'
    })
    
    // 准备提交参数
    const params = {
      id: formData.value.id,
      name: formData.value.name,
      sex: formData.value.sex,
      mobile: formData.value.mobile,
      type: formData.value.type
    }
    
    // 如果有身份证号码则添加
    if (formData.value.idCard) {
      params.idCard = formData.value.idCard
    }
    
    // 这里需要添加更新接口
    const res = await bizMiniApi.bizMiniReferralUpdate(params)
    
    // 提交成功
    uni.hideLoading()
    uni.showToast({
      title: '保存成功',
      icon: 'success',
      success: () => {
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    })
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: error.message || '保存失败',
      icon: 'none'
    })
    console.error('修改推荐官失败:', error)
  } finally {
    submitting.value = false
  }
}

onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const id = currentPage.options.id
  
  if (id) {
    getReferralDetail(id)
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'none',
      success: () => {
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    })
  }
})
</script>

<style lang="scss" scoped>
.container {
  background-color: #f1f4f9;
  min-height: 100vh;
  padding: 20rpx 0;
  padding-bottom: 140rpx; /* 为固定按钮留出空间 */
}

.form-container {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 0 30rpx;
  margin: 0 20rpx 20rpx;
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 94rpx;
  border-bottom: 1px solid #f5f5f5;
  
  &:last-child {
    border-bottom: none;
  }
}

.form-item-left {
  display: flex;
  align-items: center;
}

.required-mark {
  color: #ff0000;
  font-size: 28rpx;
  margin-right: 6rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
}

.form-input {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  flex: 1;
}

.placeholder {
  color: #999;
}

.arrow {
  margin-left: 10rpx;
  color: #999;
}

.bottom-placeholder {
  height: 40rpx;
}

.submit-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.submit-button {
  background-color: #ffcc00;
  color: #333;
  text-align: center;
  height: 92rpx;
  line-height: 92rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 500;
}
</style> 