import request from '@/utils/request'

export default {
  /**
   * 新增分佣订单
   * @param {Object} data - 订单数据
   * @param {string} data.tradeOrderId - 交易订单ID
   * @param {number} data.tradeAmount - 交易金额
   * @param {string} data.taskId - 任务ID
   * @param {string} data.taskClueId - 任务线索ID
   * @param {string} data.productName - 产品名称
   * @param {number} data.returnRate - 佣金比例
   * @param {number} data.returnAmount - 佣金金额
   * @returns {Promise} - 返回接口响应
   */
  profitOrderApply(data) {
    return request({
      url: '/profitOrder/apply',
      method: 'POST',
      data: data
    })
  },

  /**
   * 获取分佣订单列表
   * @param {Object} data - 查询参数
   * @param {string} data.taskClueId - 任务线索ID
   * @param {number} data.current - 当前页码
   * @param {number} data.size - 每页条数
   * @returns {Promise} - 返回接口响应
   */
  profitOrderList4TaskClue(data) {
    return request({
      url: '/profitOrder/list4TaskClue',
      method: 'GET',
      data: data
    })
  },

  /**
   * 终止分佣订单
   * @param {Object} data - 请求参数
   * @param {string|number} data.id - 分佣订单ID
   * @returns {Promise} - 返回接口响应
   */
  profitOrderStop(data) {
    return request({
      url: '/profitOrder/stop',
      method: 'POST',
      data: data
    })
  }
}
