import store from '@/store'

/**
 * 用户身份工具类
 * 用于判断当前登录用户的身份类型
 */
export default {
  /**
   * 获取当前用户的角色应用信息
   * @returns {Object|null} roleApp对象
   */
  getRoleApp() {
    return store.getters.roleApp || null
  },

  /**
   * 获取用户实体类型
   * @returns {string|null} entityType - MER,PARTNER,STAFF,REFERER
   */
  getEntityType() {
    return store.getters.entityType || null
  },

  /**
   * 获取用户实体子类型
   * @returns {string|null} entitySubType - GROUP,FIRST,SECOND
   */
  getEntitySubType() {
    return store.getters.entitySubType || null
  },

  /**
   * 判断是否为商户管理员
   * @returns {boolean}
   */
  isMerchantAdmin() {
    return store.getters.isMerchantAdmin || false
  },

  /**
   * 判断是否为合伙人
   * @returns {boolean}
   */
  isPartner() {
    return store.getters.isPartner || false
  },

  /**
   * 判断是否为员工
   * @returns {boolean}
   */
  isStaff() {
    return store.getters.isStaff || false
  },
  /**
   * 判断是否为员工商户管理员
   * @returns {boolean}
   */
  isStaffMERAdmin() {
    return store.getters.isStaffMERAdmin || false
  },
  /**
   * 判断是否为员工部门管理员
   * @returns {boolean}
   */
  isStaffDEPTAdmin() {
    return store.getters.isStaffDEPTAdmin || false
  },

  /**
   * 判断是否为推荐官
   * @returns {boolean}
   */
  isReferer() {
    return store.getters.isReferer || false
  },

  /**
   * 判断是否为集团商户
   * @returns {boolean}
   */
  isGroupMerchant() {
    return store.getters.isGroupMerchant || false
  },

  /**
   * 判断是否为一级代理商
   * @returns {boolean}
   */
  isFirstAgent() {
    return store.getters.isFirstAgent || false
  },

  /**
   * 判断是否为二级代理商
   * @returns {boolean}
   */
  isSecondAgent() {
    return store.getters.isSecondAgent || false
  },

  /**
   * 判断是否为一级合伙人
   * @returns {boolean}
   */
  isFirstPartner() {
    return store.getters.isFirstPartner || false
  },

  /**
   * 判断是否为二级合伙人
   * @returns {boolean}
   */
  isSecondPartner() {
    return store.getters.isSecondPartner || false
  },

  /**
   * 获取用户身份描述文本
   * @returns {string} 身份描述
   */
  getIdentityText() {
    const entityType = this.getEntityType()
    const entitySubType = this.getEntitySubType()

    if (!entityType) {
      return '未知身份'
    }

    switch (entityType) {
      case 'MER':
        return entitySubType === 'GROUP' ? '集团商户' : '商户管理员'
      case 'PARTNER':
        if (entitySubType === 'FIRST') {
          return '一级合伙人'
        } else if (entitySubType === 'SECOND') {
          return '二级合伙人'
        }
        return '合伙人'
      case 'STAFF':
        return '员工'
      case 'REFERER':
        return '推荐官'
      default:
        return '未知身份'
    }
  },

  /**
   * 检查用户是否具有指定的身份类型
   * @param {string} entityType - 实体类型
   * @param {string} entitySubType - 实体子类型（可选）
   * @returns {boolean}
   */
  hasIdentity(entityType, entitySubType = null) {
    const currentEntityType = this.getEntityType()
    const currentEntitySubType = this.getEntitySubType()

    if (currentEntityType !== entityType) {
      return false
    }

    if (entitySubType && currentEntitySubType !== entitySubType) {
      return false
    }

    return true
  },

  /**
   * 检查用户是否具有指定身份类型中的任意一种
   * @param {Array} identities - 身份数组，格式：[{entityType: 'MER', entitySubType: 'GROUP'}, ...]
   * @returns {boolean}
   */
  hasAnyIdentity(identities) {
    if (!Array.isArray(identities) || identities.length === 0) {
      return false
    }

    return identities.some(identity => {
      return this.hasIdentity(identity.entityType, identity.entitySubType)
    })
  },

  /**
   * 获取用户权限级别（数字越大权限越高）
   * @returns {number} 权限级别
   */
  getPermissionLevel() {
    if (this.isGroupMerchant()) {
      return 5 // 集团商户权限最高
    } else if (this.isMerchantAdmin()) {
      return 4 // 商户管理员
    } else if (this.isFirstPartner()) {
      return 3 // 一级合伙人
    } else if (this.isSecondPartner()) {
      return 2 // 二级合伙人
    } else if (this.isStaff()) {
      return 1 // 员工
    } else if (this.isReferer()) {
      return 1 // 推荐官
    }
    return 0 // 未知身份
  },

  /**
   * 判断当前用户是否有权限访问指定级别的功能
   * @param {number} requiredLevel - 所需权限级别
   * @returns {boolean}
   */
  hasPermissionLevel(requiredLevel) {
    return this.getPermissionLevel() >= requiredLevel
  }
}
