# 公共弹框组件使用说明

## CommonModal 组件

基于终止确认弹框样式封装的公共弹框组件，支持内容区域自定义。

### 基本用法

```vue
<template>
  <common-modal
    :visible="modalVisible"
    title="自定义标题"
    @close="handleClose"
    @confirm="handleConfirm"
  >
    <!-- 自定义内容区域 -->
    <view class="custom-content">
      <text>这里是自定义内容</text>
    </view>
  </common-modal>
</template>

<script setup>
import CommonModal from '@/components/custom-modal/common-modal.vue'

const modalVisible = ref(false)

const handleClose = () => {
  modalVisible.value = false
}

const handleConfirm = () => {
  // 处理确认逻辑
  console.log('确认操作')
  modalVisible.value = false
}
</script>
```

### Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| visible | Boolean | false | 是否显示弹框 |
| title | String | '提示' | 弹框标题 |
| showFooter | Boolean | true | 是否显示底部按钮区域 |
| showCancel | Boolean | true | 是否显示取消按钮 |
| showConfirm | Boolean | true | 是否显示确认按钮 |
| cancelText | String | '取消' | 取消按钮文字 |
| confirmText | String | '确定' | 确认按钮文字 |
| maskClosable | Boolean | true | 点击遮罩层是否关闭弹框 |

### Events 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| close | 关闭弹框时触发 | - |
| confirm | 点击确认按钮时触发 | - |
| cancel | 点击取消按钮时触发 | - |

### 插槽

| 插槽名 | 说明 |
|--------|------|
| default | 自定义内容区域 |

### 样式特点

- 弹框宽度：668rpx
- 圆角：24rpx
- 标题居中显示，字体大小32rpx
- 关闭按钮位于右上角
- 底部按钮：取消（灰色）+ 确认（黄色）
- 支持遮罩层点击关闭

### 使用场景

1. **确认对话框**
```vue
<common-modal
  :visible="confirmVisible"
  title="请确认"
  @close="closeConfirm"
  @confirm="handleConfirm"
>
  <text class="confirm-text">确认进行此操作吗？</text>
</common-modal>
```

2. **信息展示弹框**
```vue
<common-modal
  :visible="infoVisible"
  title="详细信息"
  :show-cancel="false"
  confirm-text="知道了"
  @close="closeInfo"
  @confirm="closeInfo"
>
  <view class="info-content">
    <text>这里是详细信息内容</text>
  </view>
</common-modal>
```

3. **表单弹框**
```vue
<common-modal
  :visible="formVisible"
  title="编辑信息"
  @close="closeForm"
  @confirm="submitForm"
>
  <view class="form-content">
    <input v-model="formData.name" placeholder="请输入姓名" />
    <input v-model="formData.phone" placeholder="请输入手机号" />
  </view>
</common-modal>
```
