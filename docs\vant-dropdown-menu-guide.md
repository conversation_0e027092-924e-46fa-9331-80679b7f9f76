# Vant Weapp 下拉菜单组件使用指南

## 概述

本文档介绍如何在项目中使用 Vant Weapp 的 `van-dropdown-menu` 和 `van-dropdown-item` 组件来实现查询条件筛选功能。

## 组件配置

### 1. 全局配置

在 `pages.json` 中的 `globalStyle.usingComponents` 中已经配置了相关组件：

```json
{
  "globalStyle": {
    "usingComponents": {
      "van-dropdown-menu": "/wxcomponents/vant/dropdown-menu/index",
      "van-dropdown-item": "/wxcomponents/vant/dropdown-item/index"
    }
  }
}
```

### 2. 基本用法

```vue
<template>
  <van-dropdown-menu>
    <van-dropdown-item 
      :value="selectedValue" 
      :options="optionList" 
      @change="onChange"
      title="筛选标题"
    />
  </van-dropdown-menu>
</template>

<script setup>
import { ref } from 'vue'

const selectedValue = ref('')
const optionList = ref([
  { text: '全部', value: '' },
  { text: '选项1', value: 'option1' },
  { text: '选项2', value: 'option2' }
])

const onChange = (value) => {
  selectedValue.value = value
  console.log('选择的值:', value)
}
</script>
```

## 商户管理页面实现

### 1. 模板结构

```vue
<template>
  <view class="filter-section">
    <!-- 第一行筛选条件 -->
    <van-dropdown-menu>
      <van-dropdown-item 
        :value="filterData.merchantName" 
        :options="merchantNameOptions" 
        @change="onMerchantNameChange"
        title="商户名称"
      />
      <van-dropdown-item 
        :value="filterData.merchantType" 
        :options="merchantTypeOptions" 
        @change="onMerchantTypeChange"
        title="商户类型"
      />
      <van-dropdown-item 
        :value="filterData.status" 
        :options="statusOptions" 
        @change="onStatusChange"
        title="状态"
      />
    </van-dropdown-menu>
    
    <!-- 第二行筛选条件 -->
    <van-dropdown-menu>
      <van-dropdown-item 
        :value="filterData.packageName" 
        :options="packageNameOptions" 
        @change="onPackageNameChange"
        title="套餐名称"
      />
      <van-dropdown-item 
        :value="filterData.joinTime" 
        :options="joinTimeOptions" 
        @change="onJoinTimeChange"
        title="入驻时间"
      />
      <van-dropdown-item 
        :value="filterData.expireTime" 
        :options="expireTimeOptions" 
        @change="onExpireTimeChange"
        title="激活码到期日"
      />
    </van-dropdown-menu>
  </view>
</template>
```

### 2. 数据定义

```javascript
// 查询条件
const filterData = ref({
  merchantName: '',
  merchantType: '',
  status: '',
  packageName: '',
  joinTime: '',
  expireTime: ''
})

// 下拉选项数据 - Vant组件格式
const merchantNameOptions = ref([
  { text: '全部', value: '' },
  { text: '商贸公司', value: '商贸公司' },
  { text: '科技有限公司', value: '科技有限公司' },
  { text: '餐饮管理公司', value: '餐饮管理公司' },
  { text: '零售连锁店', value: '零售连锁店' }
])

const merchantTypeOptions = ref([
  { text: '全部', value: '' },
  { text: '企业', value: '企业' },
  { text: '个体工商户', value: '个体工商户' },
  { text: '合作社', value: '合作社' },
  { text: '其他', value: '其他' }
])

// ... 其他选项数据
```

### 3. 事件处理

```javascript
// Vant下拉菜单事件处理
const onMerchantNameChange = (value) => {
  filterData.value.merchantName = value
  refreshList()
}

const onMerchantTypeChange = (value) => {
  filterData.value.merchantType = value
  refreshList()
}

// ... 其他事件处理函数

// 刷新列表
const refreshList = () => {
  currentPage.value = 1
  merchantList.value = []
  loadMerchantList()
}
```

## 组件属性说明

### van-dropdown-menu 属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| active-color | string | #ee0a24 | 菜单标题和选项的选中态颜色 |
| overlay | boolean | true | 是否显示遮罩层 |
| z-index | number | 10 | 菜单栏 z-index 层级 |
| duration | number | 200 | 动画时长，单位毫秒 |
| direction | string | down | 菜单展开方向，可选值为 up |
| close-on-click-overlay | boolean | true | 是否在点击遮罩层后关闭菜单 |

### van-dropdown-item 属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | string/number | - | 当前选中项对应的 value |
| title | string | 当前选中项文字 | 菜单项标题 |
| options | Array | [] | 选项数组 |
| disabled | boolean | false | 是否禁用菜单 |
| title-class | string | - | 标题额外类名 |

### options 数组格式

```javascript
[
  { text: '显示文本', value: '选项值' },
  { text: '全部商品', value: 0 },
  { text: '新款商品', value: 1 }
]
```

## 事件说明

### van-dropdown-item 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| change | 点击选项导致 value 变化时触发 | value: 当前选中项的 value |
| open | 打开菜单栏时触发 | - |
| close | 关闭菜单栏时触发 | - |
| opened | 打开菜单栏且动画结束后触发 | - |
| closed | 关闭菜单栏且动画结束后触发 | - |

## 样式自定义

```scss
.filter-section {
  background: white;
  border-top: 1rpx solid #f0f0f0;
  
  // Vant下拉菜单之间的间距
  .van-dropdown-menu {
    margin-bottom: 2rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}
```

## 注意事项

1. **数据格式**：options 数组必须使用 `{ text: '', value: '' }` 格式
2. **事件绑定**：使用 `@change` 监听选项变化
3. **默认值**：建议在 options 中添加"全部"选项，value 为空字符串
4. **性能优化**：在事件处理函数中及时更新筛选条件并刷新列表
5. **样式兼容**：Vant 组件自带样式，无需额外的下拉框样式代码

## 示例文件

完整的使用示例请参考：`examples/vant-dropdown-example.vue`

## 迁移指南

从自定义下拉框迁移到 Vant 组件的主要步骤：

1. 删除自定义的下拉框模板代码
2. 将选项数据格式化为 Vant 组件格式
3. 替换事件处理函数
4. 删除自定义下拉框样式
5. 测试功能是否正常
