<!--
 * @Descripttion: 我的套餐详情页面
 * @version:
 * @Author: 
 * @Date: 2025-02-19 10:00:00
 * @LastEditors: 
 * @LastEditTime: 2025-02-19 10:00:00
-->
<template>
  <snowy-layout title="我的套餐" :isTabbar="false" :isFirstPage="false">
    <view class="package-detail-container">
      <!-- 套餐卡片 -->
      <view class="package-card">
        <view class="package-header">
          <view class="package-name">{{ packageInfo.name }}</view>
          <view class="package-price">
            <view class="price-container">
              <text class="current-price">{{ packageInfo.price }}</text>
              <text class="original-price">{{ packageInfo.originalPrice }}</text>
            </view>
            <view class="renew-btn" @click="handleButtonClick">
              {{ isNearExpiry ? '续费' : '升级套餐' }}
            </view>
          </view>
        </view>

        <view class="package-desc">{{ packageInfo.desc }}</view>

        <view class="package-info-list">
          <view class="info-row">
            <view class="info-item">
              <text class="info-label">名片数量：</text>
              <text class="info-value">{{ packageInfo.cardNum }}</text>
            </view>
            <view class="info-item">
              <text class="info-label">存储空间：</text>
              <text class="info-value">{{ packageInfo.storage }}</text>
            </view>
          </view>
          <view v-if="!packageInfo.isPerson" class="info-item">
            <text class="info-label">任务数量：</text>
            <text class="info-value">{{ packageInfo.taskNum }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">生效时间：</text>
            <text class="info-value">{{ packageInfo.effectStartTime }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">到期时间：</text>
            <text class="info-value">{{ packageInfo.effectEndTime }}</text>
          </view>
        </view>
      </view>
      <!-- 套餐展示图片 -->
      <view class="package-img">
        <image :src="packageInfo.displayFilePath || ''" mode="widthFix"></image>
      </view>
    </view>

    <!-- 激活码弹窗 -->
    <view class="activation-modal" v-if="showActivationModal">
      <view class="modal-content">
        <view class="modal-header">
          <view class="modal-title"></view>
          <view class="close-icon" @click="closeModal">X</view>
        </view>
        <view class="modal-body">
          <view class="input-label">
            <text class="required-star">*</text>
            <text>激活码</text>
          </view>
          <input class="activation-input" v-model="activationCode" placeholder="请输入激活码" />
          <view class="activation-tip">如无激活码，可联系管理员获取，使用微信扫一扫即可添加：</view>
          <view class="qrcode-container">
            <image class="qrcode-img" src="/static/images/wechat-qrcode.png" mode="aspectFit"></image>
          </view>
          <view class="qrcode-desc">扫一扫上面的二维码添加，如有问题，加我解答。</view>
          <button class="confirm-btn" @click="handleActivate">确 定</button>
        </view>
      </view>
    </view>

    <!-- 激活确认弹窗 -->
    <view class="confirm-modal" v-if="showConfirmModal">
      <view class="confirm-content">
        <view class="confirm-title">提示</view>
        <view class="confirm-message">
          折算新套餐后 {{ cdkeyInfo.convertDays }} 天，新套餐激活后总共可使用 {{ cdkeyInfo.remainDay }} 天，
          <br />请确认！
        </view>
        <view class="confirm-buttons">
          <button class="cancel-btn" @click="cancelActivate">取 消</button>
          <button class="confirm-activate-btn" @click="confirmActivate">确 定</button>
        </view>
      </view>
    </view>
  </snowy-layout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import bizMerchantApi from '@/api/biz/bizMerchantApi'
import uniIcons from '@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue'
import fileApi from '@/api/dev/file-api'

// 套餐信息
const packageInfo = ref({
  name: '标准版',
  price: '100.00',
  originalPrice: '200.00',
  cardNum: '125人',
  storage: '500Mb',
  effectStartTime: '2025-02-05 10:10:23',
  effectEndTime: '2025-02-30 10:10:23',
  desc: '我是套餐更自由的创作体验，更高效的创收路径，高效对接设计资源，快速落地创意方案。'
})

// 判断是否接近到期（1个月内）
const isNearExpiry = computed(() => {
  try {
    const endDate = new Date(packageInfo.value.effectEndTime)
    const today = new Date()
    const diffTime = endDate.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    // 如果剩余天数小于等于30天，则视为接近到期
    return diffDays <= 30 && diffDays > 0
  } catch (e) {
    return false
  }
})

// 激活码弹窗状态
const showActivationModal = ref(false)
const activationCode = ref('')

// 确认弹窗状态
const showConfirmModal = ref(false)
const cdkeyInfo = ref({
  convertDays: 0,
  remainDay: 0,
  code: ''
})

// 获取套餐信息
const getPackageInfo = async () => {
  try {
    const res = await bizMerchantApi.getOwner()
    if (res && res.cpackage) {
      console.log('套餐信息:', res.cpackage)
      packageInfo.value.id = res.cpackage.id
      // 套餐主要信息
      packageInfo.value.name = res.cpackage.packageInfo.name || '标准版'
      packageInfo.value.desc = res.cpackage.packageInfo.intro || packageInfo.value.desc

      // 价格信息
      if (res.cpackage.packageInfo.price) {
        packageInfo.value.price = parseFloat(res.cpackage.packageInfo.price).toFixed(2)
      }
      if (res.cpackage.packageInfo.underlinePrice) {
        packageInfo.value.originalPrice = parseFloat(res.cpackage.packageInfo.underlinePrice).toFixed(2) + '/年'
      }

      // 使用额度
      packageInfo.value.cardNum = res.cpackage.usedCardNum + '/' + res.cpackage.packageInfo?.cardNum + '人'
      packageInfo.value.storage = res.cpackage.packageInfo?.storage + 'MB'

      // 有效期
      if (res.cpackage.effectStartTime) {
        packageInfo.value.effectStartTime = res.cpackage.effectStartTime
      }
      if (res.cpackage.effectEndTime) {
        packageInfo.value.effectEndTime = res.cpackage.effectEndTime
      }
      packageInfo.value.displayFilePath = res.cpackage.packageInfo.displayFilePath
      if (res.cpackage.packageInfo.displayFileId && !res.cpackage.packageInfo.displayFilePath) {
        const fileDetail = await fileApi.fileDetail({
          id: res.cpackage.packageInfo.displayFileId
        })
        if (fileDetail) {
          packageInfo.value.displayFilePath = fileDetail.downloadPath
        }
      }
      packageInfo.value.taskNum = res.cpackage.packageInfo?.taskNum || 0
      packageInfo.value.merchantType = res.cpackage.packageInfo?.merchantType
      packageInfo.value.isPerson = packageInfo.value.merchantType == 'PERSON'
    }
  } catch (error) {
    console.error('获取套餐信息失败:', error)
    uni.showToast({
      title: '获取套餐信息失败',
      icon: 'none'
    })
  }
}

// 按钮点击事件 - 续费或升级套餐
const handleButtonClick = () => {
  // 打开激活码输入弹窗
  showActivationModal.value = true
}

// 关闭弹窗
const closeModal = () => {
  showActivationModal.value = false
  activationCode.value = ''
}

// 校验激活码
const handleActivate = async () => {
  if (!activationCode.value.trim()) {
    uni.showToast({
      title: '请输入激活码',
      icon: 'none'
    })
    return
  }

  try {
    // 先调用校验接口
    const res = await bizMerchantApi.checkCdkey({
      code: activationCode.value.trim(),
      cpackageId: packageInfo.value.id
    })

    if (!res || res === null) {
      // 直接调用激活接口
      await activateCdkeyDirectly()
    } else {
      // 显示确认弹窗
      cdkeyInfo.value = {
        convertDays: res.convertDays || 0,
        remainDay: res.remainDay || 0,
        code: activationCode.value.trim()
      }
      showConfirmModal.value = true
      showActivationModal.value = false
    }
  } catch (error) {
    console.error('校验激活码失败:', error)
    uni.showToast({
      title: '激活码无效，请检查后重试',
      icon: 'none'
    })
  }
}

// 直接激活
const activateCdkeyDirectly = async () => {
  try {
    await bizMerchantApi.activateCdkey({
      code: activationCode.value.trim(),
      cpackageId: packageInfo.value.id
    })

    uni.showToast({
      title: '激活成功',
      icon: 'success'
    })

    // 关闭弹窗并刷新套餐信息
    closeModal()
    getPackageInfo()
  } catch (error) {
    console.error('激活失败:', error)
    uni.showToast({
      title: '激活失败，请检查激活码是否正确',
      icon: 'none'
    })
  }
}

// 取消激活
const cancelActivate = () => {
  showConfirmModal.value = false
  activationCode.value = ''
}

// 确认激活
const confirmActivate = async () => {
  try {
    await bizMerchantApi.activateCdkey({
      code: cdkeyInfo.value.code,
      cpackageId: packageInfo.value.id
    })

    uni.showToast({
      title: '激活成功',
      icon: 'success'
    })

    // 关闭弹窗并刷新套餐信息
    showConfirmModal.value = false
    getPackageInfo()
  } catch (error) {
    console.error('激活失败:', error)
    uni.showToast({
      title: '激活失败，请重试',
      icon: 'none'
    })
  }
}

// 返回上一页
const handleBack = () => {
  uni.navigateBack()
}

// 续费操作
const handleRenew = () => {
  uni.showModal({
    title: '续费提示',
    content: '确定要续费此套餐吗？',
    success: (res) => {
      if (res.confirm) {
        // 这里添加续费逻辑
        uni.showToast({
          title: '续费成功',
          icon: 'success'
        })
      }
    }
  })
}

onMounted(() => {
  // 获取套餐详情数据
  getPackageInfo()
})
</script>

<style lang="scss" scoped>
.package-detail-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  overflow: hidden;
}

// 自定义导航栏
.custom-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 90rpx;
  padding: 0 30rpx;
  background-color: #fff;
  position: relative;
}

.nav-left {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
}

.nav-back-icon {
  font-size: 36rpx;
  font-weight: bold;
}

.nav-title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
}

.nav-right {
  display: flex;
  align-items: center;
}

.more-icon {
  margin-right: 20rpx;
  font-size: 32rpx;
  transform: rotate(90deg);
  letter-spacing: -3rpx;
}

.circle-icon {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  border: 2rpx solid #333;
  display: flex;
  justify-content: center;
  align-items: center;
}

// 套餐卡片
.package-card {
  margin: 30rpx;
  padding: 36rpx 30rpx;
  background: url('https://antscard.com/minio/art-cloud-prod/2025/6/9/1931972311994671105.png') 100% 100% no-repeat;
  border-radius: 16rpx;
  box-sizing: border-box;
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.package-name {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
}

.package-price {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  flex: 1;
}

.price-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-right: 15rpx;
  margin-left: 20rpx;
  flex: 1;
}

.current-price {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  white-space: nowrap;

  &::before {
    content: '¥';
    font-size: 28rpx;
    font-weight: normal;
    margin-right: 4rpx;
  }
}

.original-price {
  font-size: 26rpx;
  color: #999;
  text-decoration: line-through;
  white-space: nowrap;
}

.renew-btn {
  background-color: #222;
  color: #fff;
  font-size: 28rpx;
  padding: 10rpx 28rpx;
  border-radius: 50rpx;
}

.package-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 36rpx;
}

.package-info-list {
  border-top: 1px dashed rgba(0, 0, 0, 0.1);
  padding-top: 24rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.info-item {
  display: flex;
  font-size: 28rpx;
  margin-bottom: 16rpx;
  line-height: 1.6;
}

.info-row .info-item {
  margin-bottom: 0;
}

.info-label {
  color: #333;
  font-weight: 500;
}

.info-value {
  color: #666;
}

// 会员权益部分
.benefits-container {
  margin: 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.benefits-title {
  background-color: #fff8e0;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
}

.title-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.benefits-content {
  background-color: #fff;
  padding: 20rpx 30rpx;
}

.benefit-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
}

.benefit-left {
  display: flex;
  flex-direction: column;
}

.benefit-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.benefit-subtitle {
  font-size: 24rpx;
  color: #999;
}

.benefit-right {
  text-align: right;
}

.benefit-amount {
  font-size: 30rpx;
  color: #ff5a5f;
  font-weight: 500;
}

.benefit-desc {
  font-size: 24rpx;
  color: #999;
  padding: 6rpx 0 16rpx;
  border-bottom: 1px solid #f5f5f5;
  margin-bottom: 16rpx;
}

// 激活码弹窗
.activation-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  width: 80%;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  padding-bottom: 30rpx;
}

.modal-header {
  display: flex;
  justify-content: flex-end;
  padding: 20rpx;
  position: relative;
}

.modal-title {
  flex: 1;
}

.close-icon {
  width: 40rpx;
  height: 40rpx;
  line-height: 36rpx;
  text-align: center;
  font-size: 30rpx;
  color: #999;
}

.modal-body {
  padding: 0 40rpx 30rpx;
}

.input-label {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  font-size: 28rpx;
}

.required-star {
  color: #f00;
  margin-right: 4rpx;
}

.activation-input {
  width: 100%;
  height: 80rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  margin-bottom: 28rpx;
  font-size: 28rpx;
}

.activation-tip {
  font-size: 24rpx;
  color: #666;
  margin: 30rpx 0 20rpx;
}

.qrcode-container {
  display: flex;
  justify-content: center;
  margin-bottom: 10rpx;
}

.qrcode-img {
  width: 360rpx;
  height: 360rpx;
}

.qrcode-desc {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  margin-bottom: 40rpx;
}

.confirm-btn {
  background-color: #1989fa;
  color: #fff;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 30rpx;
  margin: 0;
  padding: 0;
}

// 激活确认弹窗
.confirm-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirm-content {
  width: 80%;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  padding: 40rpx 30rpx;
}

.confirm-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

.confirm-message {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 40rpx;
  text-align: center;
}

.confirm-buttons {
  display: flex;
  justify-content: flex-end;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin: 0;
  margin-right: 20rpx;
  padding: 0 30rpx;
  min-width: 140rpx;
}

.confirm-activate-btn {
  background-color: #1989fa;
  color: #fff;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin: 0;
  padding: 0 30rpx;
  min-width: 140rpx;
}

.package-img {
  width: 100%;
  height: 100%;

  image {
    width: 100%;
    height: 100%;
  }
}
</style>
