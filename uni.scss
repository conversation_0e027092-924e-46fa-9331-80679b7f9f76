/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 需要放到文件最上面 https://uniapp.dcloud.net.cn/component/uniui/uni-sass.html*/
@import '@/uni_modules/uni-scss/variables.scss';


$snowy-primary: #5677fc;

$snowy-style: 'circular'; // 'square'、'circular'

/*
 以下变量是默认值，如不需要修改可以不用给下面的变量重新赋值
 */
// 主色
$uni-primary: $snowy-primary;
$uni-primary-disable:mix(#fff,$uni-primary,50%);
$uni-primary-light: mix(#fff,$uni-primary,80%);

// 辅助色
// 除了主色外的场景色，需要在不同的场景中使用（例如危险色表示危险的操作）。
$uni-success: #18bc37;
$uni-success-disable:mix(#fff,$uni-success,50%);
$uni-success-light: mix(#fff,$uni-success,80%);

$uni-warning: #f3a73f;
$uni-warning-disable:mix(#fff,$uni-warning,50%);
$uni-warning-light: mix(#fff,$uni-warning,80%);

$uni-error: #e43d33;
$uni-error-disable:mix(#fff,$uni-error,50%);
$uni-error-light: mix(#fff,$uni-error,80%);

$uni-info: #8f939c;
$uni-info-disable:mix(#fff,$uni-info,50%);
$uni-info-light: mix(#fff,$uni-info,80%);

// 中性色
// 中性色用于文本、背景和边框颜色。通过运用不同的中性色，来表现层次结构。
$uni-main-color: #3a3a3a; 			// 主要文字
$uni-base-color: #6a6a6a;			// 常规文字
$uni-secondary-color: #909399;	// 次要文字
$uni-extra-color: #c7c7c7;			// 辅助说明

// 边框颜色
$uni-border-1: #F0F0F0;
$uni-border-2: #EDEDED;
$uni-border-3: #DCDCDC;
$uni-border-4: #B9B9B9;

// 常规色
$uni-black: #000000;
$uni-white: #ffffff;
$uni-transparent: rgba($color: #000000, $alpha: 0);

// 背景色
$uni-bg-color: #f7f7f7;

/* 水平间距 */
$uni-spacing-sm: 8px;
$uni-spacing-base: 15px;
$uni-spacing-lg: 30px;

// 阴影
$uni-shadow-sm:0 0 5px rgba($color: #d8d8d8, $alpha: 0.5);
$uni-shadow-base:0 1px 8px 1px rgba($color: #a5a5a5, $alpha: 0.2);
$uni-shadow-lg:0px 1px 10px 2px rgba($color: #a5a4a4, $alpha: 0.5);

// 蒙版
$uni-mask: rgba($color: #000000, $alpha: 0.4);


// /* 颜色变量 */

// /* 行为相关颜色 */
// $uni-color-primary: #5677fc;
// $uni-color-success: #4cd964;
// $uni-color-warning: #f0ad4e;
// $uni-color-error: #dd524d;

// /* 文字基本颜色 */
// $uni-text-color:#333;//基本色
// $uni-text-color-inverse:#fff;//反色
// $uni-text-color-grey:#999;//辅助灰色，如加载更多的提示信息
// $uni-text-color-placeholder: #808080;
// $uni-text-color-disable:#c0c0c0;

// /* 背景颜色 */
// $uni-bg-color:#ffffff;
// $uni-bg-color-grey:#f8f8f8;
// $uni-bg-color-hover:#f1f1f1;//点击状态颜色
// $uni-bg-color-mask:rgba(0, 0, 0, 0.4);//遮罩颜色

// /* 边框颜色 */
// $uni-border-color:#c8c7cc;

// /* 尺寸变量 */

// /* 文字尺寸 */
// $uni-font-size-sm:12px;
// $uni-font-size-base:14px;
// $uni-font-size-lg:16px;

// /* 图片尺寸 */
// $uni-img-size-sm:20px;
// $uni-img-size-base:26px;
// $uni-img-size-lg:40px;

// /* Border Radius */
// $uni-border-radius-sm: 2px;
// $uni-border-radius-base: 3px;
// $uni-border-radius-lg: 6px;
// $uni-border-radius-circle: 50%;

// /* 水平间距 */
// $uni-spacing-row-sm: 5px;
// $uni-spacing-row-base: 10px;
// $uni-spacing-row-lg: 15px;

// /* 垂直间距 */
// $uni-spacing-col-sm: 4px;
// $uni-spacing-col-base: 8px;
// $uni-spacing-col-lg: 12px;

// /* 透明度 */
// $uni-opacity-disabled: 0.3; // 组件禁用态的透明度

// /* 文章场景相关 */
// $uni-color-title: #2C405A; // 文章标题颜色
// $uni-font-size-title:20px;
// $uni-color-subtitle: #555555; // 二级标题颜色
// $uni-font-size-subtitle:26px;
// $uni-color-paragraph: #3F536E; // 文章段落颜色
// $uni-font-size-paragraph:15px;