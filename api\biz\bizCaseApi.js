import request from '@/utils/request'

export default {
  /**
   * 分页获取案例列表
   * @param {Object} data - 查询参数
   * @param {number} data.page - 页码
   * @param {number} data.pageSize - 每页条数
   * @param {string} [data.categoryId] - 分类ID
   * @returns {Promise} - 返回接口响应
   */
  bizCasePage(data) {
    return request({
      url: '/biz/case/info/page',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取案例详情
   * @param {Object} data - 查询参数
   * @param {string|number} data.id - 案例ID
   * @returns {Promise} - 返回接口响应
   */
  bizCaseDetail(data) {
    return request({
      url: '/biz/case/info/detail',
      method: 'GET',
      data: data
    })
  }
} 