<!DOCTYPE html>
<html lang="zh_cn">
	<head>
		<meta charset="UTF-8" />
		<link rel="icon" href="/static/favicon.ico">
		<script>
			// var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
			// document.write('<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' + (coverSupport ? ', viewport-fit=cover' : '') + '" />')
			var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'));
			// 创建 meta 元素
			var meta = document.createElement('meta');
			meta.name = 'viewport';
			meta.content = 'width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' + (coverSupport ? ', viewport-fit=cover' : '');
			// 将 meta 元素插入到 head 中
			document.head.appendChild(meta);
		</script>
		<title></title>
		<!--preload-links-->
		<!--app-context-->
	</head>
	<body>
		<div id="app"><!--app-html--></div>
		<script type="module" src="/main.js"></script>
	</body>
</html>