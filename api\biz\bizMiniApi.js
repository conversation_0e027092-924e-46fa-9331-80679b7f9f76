import request from '@/utils/request'

export default {
  /**
   * 获取TabList
   * @param {Object} data - 查询参数
   * @returns {Promise} - 返回接口响应
   */
  bizMiniTabList(data) {
    return request({
      url: '/biz/mini/tabList',
      method: 'GET',
      data: data
    })
  },

  /**
   * 新增推荐官
   * @param {Object} data - 推荐官信息
   * @param {string} data.name - 推荐官姓名
   * @param {string} data.sex - 性别
   * @param {string} [data.idCard] - 身份证号码
   * @param {string} data.phone - 手机号码
   * @returns {Promise} - 返回接口响应
   */
  bizMiniReferralAdd(data) {
    return request({
      url: '/biz/miniB/referral/add',
      method: 'POST',
      data: data
    })
  },

  /**
   * 获取推荐官列表
   * @param {Object} data - 查询参数
   * @param {number} data.current - 页码
   * @param {number} data.size - 每页条数
   * @param {string} [data.keyword] - 搜索关键词(可搜索姓名、证件号、手机号)
   * @returns {Promise} - 返回接口响应
   */
  bizMiniReferralList(data) {
    return request({
      url: '/biz/miniB/referral/list',
      method: 'GET',
      data: data
    })
  },

  /**
   * 删除推荐官
   * @param {Object} data - 删除参数
   * @param {string|number} data.id - 推荐官ID
   * @returns {Promise} - 返回接口响应
   */
  bizMiniReferralDelete(data) {
    return request({
      url: '/biz/miniB/referral/delete',
      method: 'POST',
      data: data
    })
  },

  /**
   * 获取推荐官详情
   * @param {Object} data - 查询参数
   * @param {string|number} data.id - 推荐官ID
   * @returns {Promise} - 返回接口响应
   */
  bizMiniReferralDetail(data) {
    return request({
      url: '/biz/miniB/referral/detail',
      method: 'GET',
      data: data
    })
  },

  /**
   * 更新推荐官信息
   * @param {Object} data - 推荐官信息
   * @param {string|number} data.id - 推荐官ID
   * @param {string} data.name - 推荐官姓名
   * @param {string} data.sex - 性别
   * @param {string} [data.idCard] - 身份证号码
   * @param {string} data.phone - 手机号码
   * @returns {Promise} - 返回接口响应
   */
  bizMiniReferralUpdate(data) {
    return request({
      url: '/biz/miniB/referral/edit',
      method: 'POST',
      data: data
    })
  },

  /**
   * 获取产品分页
   * @param {Object} data - 查询参数
   * @param {number} data.current - 页码
   * @param {number} data.size - 每页条数
   * @param {string} [data.categoryId] - 分类ID
   * @returns {Promise} - 返回接口响应
   */
  bizMiniProductPage(data) {
    return request({
      url: '/biz/mini/product_page',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取产品详情
   * @param {Object} data - 查询参数
   * @param {string|number} data.id - 产品ID
   * @returns {Promise} - 返回接口响应
   */
  bizMiniProductDetail(data) {
    return request({
      url: '/biz/mini/product_detail',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取产品分类
   * @param {Object} data - 查询参数
   * @returns {Promise} - 返回接口响应
   */
  bizMiniProductCategory(data) {
    return request({
      url: '/biz/mini/product_category',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取数据详情（指定企业业务模块详情）
   * @param {Object} data - 查询参数
   * @param {string|number} data.id - 模块ID
   * @returns {Promise} - 返回接口响应
   */
  bizMiniDataDetail(data) {
    return request({
      url: '/biz/mini/data_detail',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取案例分页
   * @param {Object} data - 查询参数
   * @param {number} data.current - 页码
   * @param {number} data.size - 每页条数
   * @param {string} [data.categoryId] - 分类ID
   * @returns {Promise} - 返回接口响应
   */
  bizMiniCasePage(data) {
    return request({
      url: '/biz/mini/case_page',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取案例详情
   * @param {Object} data - 查询参数
   * @param {string|number} data.id - 案例ID
   * @returns {Promise} - 返回接口响应
   */
  bizMiniCaseDetail(data) {
    return request({
      url: '/biz/mini/case_detail',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取案例分类
   * @param {Object} data - 查询参数
   * @returns {Promise} - 返回接口响应
   */
  bizMiniCaseCategory(data) {
    return request({
      url: '/biz/mini/case_category',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取名片详情
   * @param {Object} data - 查询参数
   * @param {string|number} data.id - 名片ID
   * @returns {Promise} - 返回接口响应
   */
  bizMiniCardDetail(data) {
    return request({
      url: '/biz/mini/card_detail',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取宣传册分页
   * @param {Object} data - 查询参数
   * @param {number} data.current - 页码
   * @param {number} data.size - 每页条数
   * @returns {Promise} - 返回接口响应
   */
  bizMiniBrochurePage(data) {
    return request({
      url: '/biz/mini/brochure_page',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取宣传册详情
   * @param {Object} data - 查询参数
   * @param {string|number} data.id - 宣传册ID
   * @returns {Promise} - 返回接口响应
   */
  bizMiniBrochureDetail(data) {
    return request({
      url: '/biz/mini/brochure_detail',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取文章分页
   * @param {Object} data - 查询参数
   * @param {number} data.current - 页码
   * @param {number} data.size - 每页条数
   * @returns {Promise} - 返回接口响应
   */
  bizMiniArticlePage(data) {
    return request({
      url: '/biz/mini/article_page',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取文章详情
   * @param {Object} data - 查询参数
   * @param {string|number} data.id - 文章ID
   * @returns {Promise} - 返回接口响应
   */
  bizMiniArticleDetail(data) {
    return request({
      url: '/biz/mini/article_detail',
      method: 'GET',
      data: data
    })
  }
} 