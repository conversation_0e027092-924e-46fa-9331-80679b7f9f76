'use strict'

import hasOwnProp from './hasOwnProp'
import isArray from './isArray'
import isNull from './isNull'
import isNaN from './isNaN'
import isUndefined from './isUndefined'
import isFunction from './isFunction'
import isObject from './isObject'
import isString from './isString'
import isPlainObject from './isPlainObject'
import isLeapYear from './isLeapYear'
import isDate from './isDate'
import eqNull from './eqNull'
import each from './each'
import forOf from './forOf'
import lastForOf from './lastForOf'
import indexOf from './indexOf'
import lastIndexOf from './lastIndexOf'
import keys from './keys'
import values from './values'
import clone from './clone'
import getSize from './getSize'
import lastEach from './lastEach'
import remove from './remove'
import clear from './clear'
import isNumberFinite from './isFinite'
import isFloat from './isFloat'
import isInteger from './isInteger'
import isBoolean from './isBoolean'
import isNumber from './isNumber'
import isRegExp from './isRegExp'
import isError from './isError'
import isTypeError from './isTypeError'
import isEmpty from './isEmpty'
import isSymbol from './isSymbol'
import isArguments from './isArguments'
import isElement from './isElement'
import isDocument from './isDocument'
import isWindow from './isWindow'
import isFormData from './isFormData'
import isMap from './isMap'
import isWeakMap from './isWeakMap'
import isSet from './isSet'
import isWeakSet from './isWeakSet'
import isMatch from './isMatch'
import isEqual from './isEqual'
import isEqualWith from './isEqualWith'
import getType from './getType'
import uniqueId from './uniqueId'
import findIndexOf from './findIndexOf'
import findLastIndexOf from './findLastIndexOf'
import toStringJSON from './toStringJSON'
import toJSONString from './toJSONString'
import entries from './entries'
import pick from './pick'
import omit from './omit'
import first from './first'
import last from './last'
import has from './has'
import get from './get'
import set from './set'
import groupBy from './groupBy'
import countBy from './countBy'
import range from './range'
import destructuring from './destructuring'

var baseExports = {
  hasOwnProp: hasOwnProp,
  eqNull: eqNull,
  isNaN: isNumberNaN,
  isFinite: isNumberFinite,
  isUndefined: isUndefined,
  isArray: isArray,
  isFloat: isFloat,
  isInteger: isInteger,
  isFunction: isFunction,
  isBoolean: isBoolean,
  isString: isString,
  isNumber: isNumber,
  isRegExp: isRegExp,
  isObject: isObject,
  isPlainObject: isPlainObject,
  isDate: isDate,
  isError: isError,
  isTypeError: isTypeError,
  isEmpty: isEmpty,
  isNull: isNull,
  isSymbol: isSymbol,
  isArguments: isArguments,
  isElement: isElement,
  isDocument: isDocument,
  isWindow: isWindow,
  isFormData: isFormData,
  isMap: isMap,
  isWeakMap: isWeakMap,
  isSet: isSet,
  isWeakSet: isWeakSet,
  isLeapYear: isLeapYear,
  isMatch: isMatch,
  isEqual: isEqual,
  isEqualWith: isEqualWith,
  getType: getType,
  uniqueId: uniqueId,
  getSize: getSize,
  indexOf: indexOf,
  lastIndexOf: lastIndexOf,
  findIndexOf: findIndexOf,
  findLastIndexOf: findLastIndexOf,
  toStringJSON: toStringJSON,
  toJSONString: toJSONString,
  keys: keys,
  values: values,
  entries: entries,
  pick: pick,
  omit: omit,
  first: first,
  last: last,
  each: each,
  forOf: forOf,
  lastForOf: lastForOf,
  lastEach: lastEach,
  has: has,
  get: get,
  set: set,
  groupBy: groupBy,
  countBy: countBy,
  clone: clone,
  clear: clear,
  remove: remove,
  range: range,
  destructuring: destructuring
}

export default baseExports
