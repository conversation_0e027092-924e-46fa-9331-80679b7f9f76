# Cursor项目规范

## 基本设置
- 使用中文回答所有问题
- 新建的页面要在pages.json中注册

## 项目结构和命名规范
- 组件命名：PascalCase
- 函数命名：camelCase
- 常量命名：UPPER_CASE
- 文件夹结构遵循：/components, /api, /utils, /pages
- 请求的接口都定义在/api/biz/目录下,定义方式和/api/biz/目录下其他接口一致

## 页面生成规范
- 页面生成时，需要生成页面结构使用<snowy-layout title="访问记录" :isTabbar="true" :isFirstPage="true">标签
- 页面文件中，需要配置页面标题、导航栏样式、是否显示tabbar等,如果是tabbar页面，需要配置uni.hideTabBar()
- 按照UI图片1:1还原页面,精确生成页面结构
- 如果是新页面则需要在pages.json中配置页面路径

## 代码风格
- 使用Prettier规则

## UI设计规范
- 遵循现代设计趋势和最佳实践
- 优先采用Flexbox或Grid布局系统
- 确保适当的留白和视觉层次

## UI组件标准
- 使用组件库作为基础（如uni-app等）
- 自定义组件需符合现代设计审美
- 设计组件应包含默认、悬停、聚焦、禁用等状态
- 使用适当的动画和过渡效果增强用户体验
- 确保设计一致性：同类元素使用相同样式
- 根据用户操作提供视觉反馈