<template>
  <snowy-layout :title="isPreview ? '查看工单' : '编辑工单'" :isTabbar="false" :isFirstPage="false">
    <scroll-view class="task-edit-container" scroll-y="true">
      <!-- 表单区域 -->
      <view class="form-section" style="padding: 0 30rpx;">
        <view class="form-item horizontal">
          <view class="form-label">工单名称</view>
          <view class="form-input-box">
            <input class="form-input" type="text" v-model="formData.name" placeholder="请输入工单名称" :disabled="isPreview" />
          </view>
        </view>

        <view class="form-item horizontal">
          <view class="form-label">工单编号</view>
          <view class="form-input-box">
            <input class="form-input" type="text" v-model="formData.code" placeholder="请输入工单编号" :disabled="isPreview" />
          </view>
        </view>

        <view class="form-item horizontal">
          <view class="form-label">
            <text class="required-mark">*</text>
            <text>产品名称</text>
          </view>
          <view class="form-input-box">
            <input class="form-input" type="text" v-model="formData.productName" placeholder="请输入产品名称"
              :disabled="isPreview" />
          </view>
        </view>

        <view class="form-item">
          <view class="form-label">备注</view>
          <view class="form-input-box textarea-box">
            <textarea class="form-textarea" v-model="formData.remarks" placeholder="请输入备注" :disabled="isPreview" />
          </view>
        </view>
      </view>

      <!-- 生命周期区域 -->
      <view class="form-section stage-section" style="padding: 0 30rpx 30rpx 30rpx;">
        <view class="stage-header">
          <view class="stage-title">
            <text class="required-mark">*</text>
            <text>阶段</text>
          </view>
        </view>

        <!-- 生命周期选择器 -->
        <view class="stage-selector" @click="!isPreview && toggleStageDropdown()" :class="{ disabled: isPreview }">
          <text>{{ getCurrentStageName() }}</text>
          <uni-icons type="bottom" size="16" color="#333" v-if="!isPreview"></uni-icons>
        </view>

        <!-- 进度条 -->
        <view class="progress-container" v-if="lifeCycleList.length > 0">
          <scroll-view class="progress-scroll" scroll-x="true" show-scrollbar="false" :scroll-left="scrollLeft"
            scroll-with-animation="true">
            <view class="progress-content">
              <view class="progress-line"></view>
              <view class="progress-steps">
                <view v-for="(step, index) in lifeCycleList" :key="step.id" class="progress-step"
                  :class="{ current: step.stage === formData.stage }" @click="!isPreview && selectStage(step, index)">
                  <view class="progress-dot" :class="{ active: index <= formData.lifeCycleTapIndex }"></view>
                  <view class="progress-text" :class="{ active: index <= formData.lifeCycleTapIndex }">{{
                    step.smallStage }}</view>
                </view>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
      <!-- 跟进情况区域 -->
      <view class="form-section follow-section" style="padding: 0 30rpx;">
        <view class="form-title">跟进情况</view>
        <view class="form-input-box textarea-box">
          <textarea class="form-textarea" v-model="formData.desc" placeholder="请输入跟进内容" :disabled="isPreview" />
        </view>

        <!-- 跟进佐证 -->
        <view class="form-item">
          <view class="form-label">跟进佐证</view>
          <view class="upload-box">
            <view class="upload-item" v-for="(fileDetail, index) in formData.evidenceFileDetails" :key="index">
              <image class="upload-image" :src="fileDetail.tempPath || fileDetail.downloadPath" mode="aspectFill">
              </image>
              <view class="delete-image" @click="deleteEvidenceImage(index)" v-if="!isPreview">
                <uni-icons type="closeempty" size="14" color="#fff"></uni-icons>
              </view>
            </view>
            <view class="upload-btn" @click="uploadEvidenceImage"
              v-if="!isPreview && formData.evidenceFileDetails.length < 4">
              <uni-icons type="plusempty" size="30" color="#999"></uni-icons>
            </view>
          </view>
        </view>
      </view>
      <!-- 付款记录区域 - 仅在stage为1-交易中，2-交易完成 阶段显示 -->
      <view v-if="!isPreview && (formData.stage === 1 || formData.stage === 2)" class="add-btn" @click="addPayment">
        <text>+ 添加付款</text>
      </view>
      <view class="form-section payment-section" v-if="formData.stage === 1 || formData.stage === 2">
        <!-- 付款记录列表 -->
        <view class="payment-list">
          <view class="payment-item" v-for="(payment, index) in formData.payments" :key="index">
            <view class="payment-header">
              <text class="payment-index">付款-{{ index + 1 }}</text>
              <image src="/static/images/icons/delete.png" mode="widthFix" class="delete-btn"
                @click="deletePayment(index)" v-if="!isPreview && payment.isNew"></image>
            </view>
            <view class="payment-form">
              <view class="form-item horizontal">
                <view class="form-label">
                  <text class="required-mark">*</text>
                  <text>项目名称</text>
                </view>
                <view class="form-input-box">
                  <input class="form-input" type="text" v-model="payment.productName" placeholder="请输入名称"
                    :disabled="isPreview || !payment.isNew" />
                </view>
              </view>

              <view class="form-item horizontal">
                <view class="form-label">
                  <text class="required-mark">*</text>
                  <text>付款金额(元)</text>
                </view>
                <view class="form-input-box">
                  <input class="form-input" type="digit" v-model="payment.amount" placeholder="请输入金额"
                    :disabled="isPreview || !payment.isNew" />
                </view>
              </view>

              <view class="form-item horizontal">
                <view class="form-label">
                  <text class="required-mark">*</text>
                  <text>付款日期</text>
                </view>
                <view class="form-input-box">
                  <uni-datetime-picker type="date" v-model="payment.payTime" :clear-icon="false" placeholder="请选择付款日期"
                    :border="false" :disabled="isPreview || !payment.isNew" class="datetime-picker">
                    <view class="date-display">
                      <text class="form-value">{{ payment.payTime || '请选择付款日期' }}</text>
                      <uni-icons type="calendar" size="16" color="#CCCCCC"></uni-icons>
                    </view>
                  </uni-datetime-picker>
                </view>
              </view>

              <view class="form-item horizontal">
                <view class="form-label">创建人</view>
                <view class="form-input-box">
                  <text class="form-value">{{ payment.staffName || '' }}</text>
                </view>
              </view>

              <view class="form-item">
                <view class="form-label">备注</view>
                <view class="form-input-box textarea-box">
                  <textarea class="form-textarea" v-model="payment.remarks" placeholder="请输入备注"
                    :disabled="isPreview || !payment.isNew" />
                </view>
              </view>

              <view class="form-item">
                <view class="form-label">
                  <text class="required-mark">*</text>
                  <text>付款凭证</text>
                </view>
                <view class="upload-box">
                  <view class="upload-item" v-for="(fileDetail, imgIndex) in payment.fileDetails" :key="imgIndex">
                    <image class="upload-image" :src="fileDetail.tempPath || fileDetail.downloadPath" mode="aspectFill">
                    </image>
                    <view class="delete-image" @click="deleteImage(index, imgIndex)" v-if="!isPreview && payment.isNew">
                      <uni-icons type="closeempty" size="14" color="#fff"></uni-icons>
                    </view>
                  </view>
                  <view class="upload-btn" @click="uploadImage(index)"
                    v-if="!isPreview && payment.isNew && payment.fileDetails.length < 4">
                    <uni-icons type="plusempty" size="30" color="#999"></uni-icons>
                  </view>
                </view>
              </view>
            </view>

          </view>
        </view>
      </view>

      <!-- 底部按钮区域 -->
      <view class="bottom-buttons" v-if="!isPreview">
        <button class="bottom-btn submit-btn" @click="submitForm">提交</button>
      </view>
    </scroll-view>
  </snowy-layout>
</template>

<script setup>
import { ref, reactive, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import bizInventoryApi from '@/api/biz/bizInventoryApi'
import fileApi from '@/api/dev/file-api.js'

const clueId = ref('')
const taskId = ref('')
const isEdit = ref(false)
const isPreview = ref(false) // 预览模式标识

// 表单数据
const formData = reactive({
  id: '',
  taskClueId: '',
  name: '',
  code: '',
  productName: '',
  remarks: '',
  lifeCycleId: '',
  lifeCycleTapIndex: 0,
  stage: 0,
  smallStage: '',
  desc: '',
  evidence: '',
  staffId: '',
  extJson: '',
  payments: [],
  evidenceFiles: [], // 跟进佐证文件ID数组
  evidenceFileDetails: [] // 跟进佐证文件详情数组，用于显示
})

// 生命周期数据
const lifeCycleList = ref([])
const originalStage = ref(0) // 记录原始阶段，用于编辑时的限制
const scrollLeft = ref(0) // 进度条滚动位置

// 进度步骤映射
const stageMap = {
  0: '交易意向',
  1: '交易中',
  2: '交易完成',
  3: '成交完成'
}

// 监听阶段变化
watch(
  () => formData.stage,
  (newVal) => {
    // 如果切换到交易中或交易完成阶段，且付款记录为空，则添加一条默认记录
    if ((newVal === 1 || newVal === 2) && formData.payments.length === 0) {
      addPayment()
    }
    // 自动滚动到当前阶段
    setTimeout(() => {
      scrollToCurrentStage()
    }, 100)
  }
)

// 获取当前阶段名称
const getCurrentStageName = () => {
  if (lifeCycleList.value.length > 0) {
    const currentStage = lifeCycleList.value.find(item => item.id === formData.lifeCycleId)
    return currentStage ? currentStage.smallStage : '交易意向'
  }
  return stageMap[formData.stage] || '交易意向'
}

// 自动滚动到当前阶段
const scrollToCurrentStage = () => {
  if (lifeCycleList.value.length === 0) return

  const currentIndex = lifeCycleList.value.findIndex(item => item.stage === formData.stage)
  if (currentIndex >= 0) {
    // 每个节点宽度约160rpx（120rpx + 40rpx margin），滚动到当前节点居中
    const itemWidth = 160
    const containerWidth = 750 // 屏幕宽度
    const targetScrollLeft = Math.max(0, currentIndex * itemWidth - containerWidth / 2 + itemWidth / 2)
    scrollLeft.value = targetScrollLeft
  }
}

// 添加付款记录
const addPayment = () => {
  if (isPreview.value) {
    return
  }

  formData.payments.push({
    id: '',
    ticketId: '',
    productName: '',
    amount: '',
    payTime: '',
    remarks: '',
    picture: '',
    extJson: '',
    staffName: formData.staffName,
    paths: [], // 文件ID数组
    fileDetails: [], // 用于显示的文件详情数组
    isNew: true // 标识为新添加的记录
  })
}

// 删除付款记录
const deletePayment = (index) => {
  if (isPreview.value) {
    return
  }

  const payment = formData.payments[index]

  // 只允许删除新增的记录
  if (!payment.isNew) {
    uni.showToast({
      title: '已保存的付款记录不能删除',
      icon: 'none'
    })
    return
  }

  uni.showModal({
    title: '提示',
    content: '确定要删除此付款记录吗？',
    success: (res) => {
      if (res.confirm) {
        formData.payments.splice(index, 1)
      }
    }
  })
}



// 上传图片
const uploadImage = async (index) => {
  if (isPreview.value) {
    return
  }

  const payment = formData.payments[index]

  // 只允许为新增记录上传图片
  if (!payment.isNew) {
    uni.showToast({
      title: '已保存的付款记录不能修改',
      icon: 'none'
    })
    return
  }

  try {
    const res = await new Promise((resolve, reject) => {
      uni.chooseImage({
        count: 1,
        success: resolve,
        fail: reject
      })
    })

    const tempFilePath = res.tempFilePaths[0]

    // 显示上传进度
    uni.showLoading({
      title: '上传中...'
    })

    // 上传文件并获取ID
    const ret = await fileApi.fileUploadDynamicReturnId2({
      filePath: tempFilePath
    })

    // 获取文件详情
    const fileDetail = await fileApi.fileDetail({ id: ret })

    // 保存文件ID到paths数组
    formData.payments[index].paths.push(ret)

    // 保存文件详情用于显示
    formData.payments[index].fileDetails.push({
      id: fileDetail.id,
      downloadPath: fileDetail.downloadPath,
      tempPath: tempFilePath // 临时路径用于预览
    })

    uni.hideLoading()
    uni.showToast({
      title: '上传成功',
      icon: 'success'
    })
  } catch (error) {
    uni.hideLoading()
    console.error('上传失败：', error)
    uni.showToast({
      title: '上传失败',
      icon: 'none'
    })
  }
}

// 删除图片
const deleteImage = (paymentIndex, imageIndex) => {
  if (isPreview.value) {
    return
  }

  const payment = formData.payments[paymentIndex]

  // 只允许删除新增记录的图片
  if (!payment.isNew) {
    uni.showToast({
      title: '已保存的付款凭证不能删除',
      icon: 'none'
    })
    return
  }

  // 删除文件ID
  formData.payments[paymentIndex].paths.splice(imageIndex, 1)
  // 删除文件详情
  formData.payments[paymentIndex].fileDetails.splice(imageIndex, 1)
}

// 上传跟进佐证图片
const uploadEvidenceImage = async () => {
  if (isPreview.value) {
    return
  }

  try {
    const res = await new Promise((resolve, reject) => {
      uni.chooseImage({
        count: 1,
        success: resolve,
        fail: reject
      })
    })

    const tempFilePath = res.tempFilePaths[0]

    // 显示上传进度
    uni.showLoading({
      title: '上传中...'
    })

    // 上传文件并获取ID
    const ret = await fileApi.fileUploadDynamicReturnId2({
      filePath: tempFilePath
    })

    // 获取文件详情
    const fileDetail = await fileApi.fileDetail({ id: ret })

    // 保存文件ID到evidenceFiles数组
    formData.evidenceFiles.push(ret)

    // 保存文件详情用于显示
    formData.evidenceFileDetails.push({
      id: fileDetail.id,
      downloadPath: fileDetail.downloadPath,
      tempPath: tempFilePath // 临时路径用于预览
    })

    uni.hideLoading()
    uni.showToast({
      title: '上传成功',
      icon: 'success'
    })
  } catch (error) {
    uni.hideLoading()
    console.error('上传失败：', error)
    uni.showToast({
      title: '上传失败',
      icon: 'none'
    })
  }
}

// 删除跟进佐证图片
const deleteEvidenceImage = (imageIndex) => {
  if (isPreview.value) {
    return
  }

  // 删除文件ID
  formData.evidenceFiles.splice(imageIndex, 1)
  // 删除文件详情
  formData.evidenceFileDetails.splice(imageIndex, 1)
}

// 获取工单详情
const getTaskDetail = async () => {
  try {
    const params = {}
    if (taskId.value) {
      params.id = taskId.value
      isEdit.value = true
    } else if (clueId.value) {
      params.taskClueId = clueId.value
      isEdit.value = false
    }

    const res = await bizInventoryApi.getTicketDetail(params)
    if (res) {
      const data = res

      // 填充表单数据
      Object.assign(formData, {
        ...data,
        id: data.id || '',
        taskClueId: data.taskClueId || clueId.value,
        name: data.name || '',
        code: data.code || '',
        productName: data.productName || '',
        remarks: data.remarks || '',
        lifeCycleId: data.lifeCycleId || '',
        stage: data.stage || 0,
        smallStage: data.smallStage || '',
        desc: data.desc || '',
        evidence: data.evidence || '',
        staffId: data.staffId || '',
        extJson: data.extJson || '',
        payments: [],
        evidenceFiles: data.evidence ? data.evidence.split(',') : [],
        evidenceFileDetails: []
      })
      // 处理跟进佐证文件
      if (data.paths && data.paths.length > 0) {
        formData.evidenceFileDetails = data.paths.map(v => {
          return {
            id: '',
            downloadPath: v
          }
        })
      }

      // 处理付款记录数据
      if (data.payments && data.payments.length > 0) {
        for (const payment of data.payments) {
          const paymentItem = {
            ...payment,
            fileDetails: (payment.paths || []).map(v => {
              return {
                id: '',
                downloadPath: v
              }
            }),
            paths: payment.picture ? payment.picture.split(',') : [],
            isNew: false // 从接口获取的记录标记为不可编辑
          }
          formData.payments.push(paymentItem)
        }
      }

      // 保存原始阶段
      originalStage.value = data.lifeCycle.findIndex(item => item.id === data.lifeCycleId)

      // 设置生命周期数据
      if (data.lifeCycle && data.lifeCycle.length > 0) {
        lifeCycleList.value = data.lifeCycle
        formData.lifeCycleTapIndex = lifeCycleList.value.findIndex(item => item.id === data.lifeCycleId)

        // 数据加载完成后自动滚动到当前阶段
        setTimeout(() => {
          scrollToCurrentStage()
        }, 200)
      }
    }
  } catch (error) {
    console.error('获取工单详情失败：', error)
    uni.showToast({
      title: '获取详情失败',
      icon: 'none'
    })
  }
}

// 切换阶段下拉框
const toggleStageDropdown = () => {
  if (isPreview.value) {
    return
  }

  if (lifeCycleList.value.length === 0) {
    uni.showToast({
      title: '暂无可选阶段',
      icon: 'none'
    })
    return
  }

  // 根据是否为编辑模式过滤可选阶段
  let availableStages = lifeCycleList.value
  if (isEdit.value) {
    // 编辑模式：只能选择当前阶段及之后的阶段
    availableStages = lifeCycleList.value.filter((_, index) => index >= originalStage.value)
  }

  const itemList = availableStages.map(item => item.smallStage)

  uni.showActionSheet({
    itemList: itemList,
    success: (res) => {
      const selectedStage = availableStages[res.tapIndex]
      formData.stage = selectedStage.stage
      formData.lifeCycleId = selectedStage.id
      formData.smallStage = selectedStage.smallStage
      formData.lifeCycleTapIndex = lifeCycleList.value.findIndex(item => item.id === selectedStage.id)
      console.log(selectedStage, 'selectedStage', formData);

    }
  })
}
const selectStage = (item, index) => {
  if (isPreview.value) {
    formData.stage = item.stage
    formData.lifeCycleId = item.id
    formData.smallStage = item.smallStage
    formData.lifeCycleTapIndex = index
  }
}
// 提交表单
const submitForm = async () => {
  // 表单验证
  if (!formData.productName) {
    uni.showToast({
      title: '请输入产品名称',
      icon: 'none'
    })
    return
  }

  if (formData.stage === undefined || formData.stage === null) {
    uni.showToast({
      title: '请选择阶段',
      icon: 'none'
    })
    return
  }

  // 如果是交易中或交易完成阶段，验证付款记录
  if (formData.stage === 1 || formData.stage === 2) {
    if (formData.payments.length === 0) {
      uni.showToast({
        title: '请添加付款记录',
        icon: 'none'
      })
      return
    }

    for (let i = 0; i < formData.payments.length; i++) {
      const payment = formData.payments[i]
      if (!payment.productName) {
        uni.showToast({
          title: `请输入付款-${i + 1}的项目名称`,
          icon: 'none'
        })
        return
      }

      if (!payment.amount) {
        uni.showToast({
          title: `请输入付款-${i + 1}的金额`,
          icon: 'none'
        })
        return
      }

      if (!payment.payTime) {
        uni.showToast({
          title: `请选择付款-${i + 1}的日期`,
          icon: 'none'
        })
        return
      }

      if (payment.paths.length === 0) {
        uni.showToast({
          title: `请上传付款-${i + 1}的凭证`,
          icon: 'none'
        })
        return
      }
    }
  }

  try {
    uni.showLoading({
      title: '提交中...'
    })

    // 准备提交数据，清理fileDetails字段
    const submitData = {
      ...formData,
      taskClueId: clueId.value,
      evidence: formData.evidenceFiles.join(','), // 将跟进佐证文件ID数组赋值给paths字段
      payments: formData.payments.map(payment => {
        const { fileDetails, ...paymentData } = payment
        paymentData.picture = paymentData.paths.join(',')
        paymentData.paths = null
        return paymentData
      })
    }

    // 删除临时字段
    delete submitData.evidenceFiles
    delete submitData.evidenceFileDetails

    if (formData.id) {
      // 编辑工单
      await bizInventoryApi.updateTicket(submitData)
    } else {
      // 新增工单
      await bizInventoryApi.createTicket(submitData)
    }

    uni.hideLoading()
    uni.showToast({
      title: formData.id ? '更新成功' : '创建成功',
      icon: 'success'
    })

    // 返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  } catch (error) {
    uni.hideLoading()
    console.error('提交工单失败：', error)
    uni.showToast({
      title: '提交失败',
      icon: 'none'
    })
  }
}

// 删除工单
const deleteTask = () => {
  uni.showModal({
    title: '提示',
    content: '确定要废弃此工单吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({
            title: '删除中...'
          })

          await bizInventoryApi.deleteTicket({ id: formData.id })

          uni.hideLoading()
          uni.showToast({
            title: '已废弃',
            icon: 'success'
          })

          // 返回上一页
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        } catch (error) {
          uni.hideLoading()
          console.error('删除工单失败：', error)
          uni.showToast({
            title: '删除失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

onLoad((option) => {
  if (option.clueId) {
    clueId.value = option.clueId
  }

  if (option.taskId) {
    taskId.value = option.taskId
  }

  // 检查是否为预览模式
  if (option.preview === '1') {
    isPreview.value = true
  }

  // 获取工单详情
  getTaskDetail()
})
</script>

<style lang="scss" scoped>
.task-edit-container {
  height: 100%;
  background-color: #f5f5f5;
  padding-bottom: 130rpx;
  overflow: auto;
  box-sizing: border-box;
}

// 表单区域样式
.form-section {
  background-color: #fff;
  margin-bottom: 20rpx;
  // padding: 0 30rpx;
}

.stage-section {
  padding-bottom: 30rpx;
}

.follow-section {
  padding-bottom: 20rpx;
}

.form-title {
  display: flex;
  align-items: center;
  height: 90rpx;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.required-mark {
  color: #ff4d4f;
  margin-right: 5rpx;
  font-size: 30rpx;
}

.payment-form {
  padding: 0 30rpx;
}

.form-item {
  padding: 24rpx 0;
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }

  &.horizontal {
    display: flex;
    align-items: center;

    .form-label {
      width: 175rpx;
      margin-bottom: 0;
      flex-shrink: 0;
    }

    .form-input-box {
      flex: 1;
    }
  }
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.form-input-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-value {
  flex: 1;
  font-size: 28rpx;
  color: #666;
  text-align: right;
  padding-right: 10rpx;
}

.form-input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
  color: #333;
  text-align: right;

  &::placeholder {
    color: #999;
  }

  &:disabled {
    color: #999;
    background-color: #f5f5f5;
  }
}

.textarea-box {
  padding: 0;
}

.form-textarea {
  width: 100%;
  height: 180rpx;
  font-size: 28rpx;
  background-color: #f8f8f8;
  padding: 20rpx;
  border-radius: 8rpx;
  box-sizing: border-box;

  &::placeholder {
    color: #999;
  }

  &:disabled {
    color: #999;
    background-color: #f0f0f0;
  }
}

// 阶段选择样式
.stage-header {
  display: flex;
  align-items: center;
  height: 90rpx;
}

.stage-title {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

// 阶段选择器样式
.stage-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 32rpx;
  color: #333;
  background-color: #fff;
  border: 1px solid #eeeeee;
  border-radius: 12rpx;
  padding: 0 30rpx;
  height: 90rpx;
  width: 100%;
  box-sizing: border-box;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 60rpx;

  &.disabled {
    background-color: #f5f5f5;
    color: #999;
    cursor: not-allowed;
  }
}

// 进度条样式
.progress-container {
  margin-bottom: 40rpx;
  position: relative;
}

.progress-scroll {
  width: 100%;
  white-space: nowrap;
}

.progress-content {
  display: inline-block;
  min-width: 100%;
  padding: 0 30rpx;
  position: relative;
}

.progress-line {
  position: absolute;
  top: 12rpx;
  left: 90rpx;
  width: calc(100% - 180rpx);
  height: 2rpx;
  background-color: #eeeeee;
  z-index: 1;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(to right, #ffcb00, #ffcb00);
    width: 0%;
    transition: width 0.3s ease;
  }
}

.progress-steps {
  display: flex;
  position: relative;
  z-index: 2;
  min-width: max-content;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120rpx;
  margin-right: 40rpx;
  flex-shrink: 0;
  transition: all 0.3s ease;

  &:last-child {
    margin-right: 0;
  }

  &.current {
    transform: scale(1.05);

    .progress-dot {
      box-shadow: 0 0 10rpx rgba(255, 203, 0, 0.5);
    }

    .progress-text {
      font-weight: 600;
    }
  }
}

.progress-dot {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: #ffffff;
  border: 2rpx solid #eeeeee;
  box-sizing: border-box;
  margin-bottom: 15rpx;

  &.active {
    background-color: #ffcb00;
    border: none;
  }
}

.progress-text {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  white-space: nowrap;
  max-width: 120rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  margin-top: 8rpx;

  &.active {
    color: #333;
    font-weight: 500;
  }
}

// 付款记录样式
.payment-section {
  margin-bottom: 20rpx;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 90rpx;
}

.section-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.add-btn {
  width: 100%;
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 209, 0, 1);
  font-size: 32rpx;
  font-weight: bold;
  background-color: white;
  letter-spacing: 4rpx;

  text {
    margin-left: 4rpx;
  }
}

.payment-item {
  background-color: #fff;
  border-radius: 8rpx;
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.payment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx;
  border-bottom: 1px solid #f5f5f5;
  background-color: #f2f2f2;
}

.payment-index {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.delete-btn {
  padding: 10rpx;
  width: 32rpx;
  height: 32rpx;
}

.badge {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  background-color: #4080ff;
  color: #fff;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 上传区域样式
.upload-box {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10rpx;
}

.upload-item {
  width: 160rpx;
  height: 160rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}

.upload-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-image {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-btn {
  width: 160rpx;
  height: 160rpx;
  border: 1px dashed #cccccc;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 日期选择器样式
.datetime-picker {
  width: 100%;

  :deep(.uni-date-editor--x) {
    border: none !important;
    background: transparent !important;
    padding: 0 !important;
    height: auto !important;
  }

  :deep(.uni-date-x) {
    background: transparent !important;
    border: none !important;
    padding: 0 !important;
    height: auto !important;
  }

  :deep(.uni-date__x-input) {
    color: #666 !important;
    font-size: 28rpx !important;
    text-align: right !important;
    padding: 0 !important;
  }

  :deep(.icon-calendar) {
    display: none !important;
  }
}

.date-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0;
  pointer-events: none;
}

// 底部按钮样式
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx 50rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.bottom-btn {
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 32rpx;
  border-radius: 45rpx;
  font-weight: normal;
  margin: 0;
  padding: 0;
}

.submit-btn {
  background-color: #ffcb00;
  color: #333;
}

.delete-btn {
  color: #fff;
}
</style>
