import request from '@/utils/request'

export default {
  /**
   * 分页获取宣传册列表
   * @param {Object} data - 查询参数
   * @param {number} data.current - 页码
   * @param {number} data.size - 每页条数
   * @param {number} [data.status] - 状态(1-启用,0-禁用)
   * @returns {Promise} - 返回接口响应
   */
  brochurePage(data) {
    return request({
      url: '/biz/mini/brochure_page',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取宣传册详情
   * @param {Object} data - 查询参数
   * @param {string|number} data.id - 宣传册ID
   * @returns {Promise} - 返回接口响应
   */
  brochureDetail(data) {
    return request({
      url: '/biz/brochure/detail',
      method: 'GET',
      data: data
    })
  },

  /**
   * 添加宣传册
   * @param {Object} data - 宣传册数据
   * @param {string} data.title - 标题
   * @param {string} data.description - 描述
   * @param {string} data.storagePath - 图片路径
   * @param {number} [data.sort=99] - 排序
   * @param {number} [data.status=1] - 状态(1-启用,0-禁用)
   * @returns {Promise} - 返回接口响应
   */
  brochureAdd(data) {
    return request({
      url: '/biz/brochure/add',
      method: 'POST',
      data: data
    })
  },

  /**
   * 编辑宣传册
   * @param {Object} data - 宣传册数据
   * @param {string|number} data.id - 宣传册ID
   * @param {string} [data.title] - 标题
   * @param {string} [data.description] - 描述
   * @param {string} [data.storagePath] - 图片路径
   * @param {number} [data.sort] - 排序
   * @param {number} [data.status] - 状态(1-启用,0-禁用)
   * @returns {Promise} - 返回接口响应
   */
  brochureEdit(data) {
    return request({
      url: '/biz/brochure/edit',
      method: 'POST',
      data: data
    })
  },

  /**
   * 删除宣传册
   * @param {Object} data - 查询参数
   * @param {string|number} data.id - 宣传册ID
   * @returns {Promise} - 返回接口响应
   */
  brochureDelete(data) {
    return request({
      url: '/biz/brochure/delete',
      method: 'POST',
      data: data
    })
  },

  /**
   * 获取宣传册列表
   * @param {Object} data - 查询参数
   * @param {number} [data.limit=10] - 获取数量
   * @param {number} [data.status=1] - 状态(1-启用,0-禁用)
   * @returns {Promise} - 返回接口响应
   */
  brochureList(data) {
    return request({
      url: '/biz/brochure/list',
      method: 'GET',
      data: data
    })
  }
} 