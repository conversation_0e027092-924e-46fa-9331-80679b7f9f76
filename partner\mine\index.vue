<template>
  <snowy-layout title="我的" :isTabbar="true" :isFirstPage="true">
    <view class="partner-mine">
      <!-- 用户信息卡片 -->
      <view class="user-card">
        <!-- 头像和基本信息 -->
        <view class="user-header">
          <view class="avatar-container">
            <image class="avatar" :src="$store.getters.allEnv[$store.getters.envKey].image_baseUrl + '/static/images/partner/default-avatar.png'" mode="aspectFill"></image>
          </view>
          <view class="user-basic-info">
            <view class="company-name">{{ partnerInfo.name ||'' }}</view>
            <view class="phone-number">{{ partnerInfo.userPhone || '' }}</view>
          </view>
        </view>

        <!-- 详细信息 -->
        <view class="detail-info">
          <view class="info-item">
            <text class="info-label">所属区域：</text>
            <text class="info-value">{{ partnerInfo.areaName ||  '' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">联系人：</text>
            <text class="info-value">{{ partnerInfo.contactName || '' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">联系电话：</text>
            <text class="info-value">{{ partnerInfo.contactPhone ||  '' }}</text>
          </view>
        </view>
      </view>
    
      <!-- 退出登录 -->
      <view class="logout-section">
        <button class="logout-btn" @click="handleLogout">退出登录</button>
      </view>
    </view>
  </snowy-layout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import loginApi from '@/api/auth/login-api'
import bizPartnerApi from '@/api/biz/bizPartnerApi'

const store = useStore()

// 合伙人详情信息
const partnerInfo = ref({})
const isLoading = ref(false)

// 用户信息（作为备用）
const userInfo = computed(() => store.getters.userInfo || {})

// 获取合伙人详情
const getPartnerDetail = async () => {
  try {
    isLoading.value = true
    const roleApp = store.getters.roleApp || {}

    if (roleApp.entityId) {
      const response = await bizPartnerApi.getPartnerDetail({
        id: roleApp.entityId
      })

      if (response) {
        partnerInfo.value = response || {}
      } else {
        console.error('获取合伙人详情失败:')
      }
    }
  } catch (error) {
    console.error('获取合伙人详情失败:', error)
  } finally {
    isLoading.value = false
  }
}

// 页面跳转
const navigateTo = (url) => {
  uni.navigateTo({
    url: url,
    fail: () => {
      uni.showToast({
        title: '页面开发中',
        icon: 'none'
      })
    }
  })
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          await loginApi.logout()
          uni.clearStorageSync()
          uni.reLaunch({
            url: '/pages/auth/login'
          })
        } catch (error) {
          console.error('退出登录失败:', error)
        }
      }
    }
  })
}

// 页面加载时获取合伙人详情
onMounted(() => {
  getPartnerDetail()
})
</script>

<style lang="scss" scoped>
.partner-mine {
  min-height: 100%;
  background: white;
  padding: 40rpx 30rpx;
}

.user-card {
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;

  .user-header {
    display: flex;
    align-items: center;
    margin-bottom: 40rpx;

    .avatar-container {
      margin-right: 30rpx;
      flex-shrink: 0;

      .avatar {
        width: 140rpx;
        height: 140rpx;
        border-radius: 50%;
        border: 4rpx dashed #E0E0E0;
        padding: 6rpx;
        box-sizing: border-box;
      }
    }

    .user-basic-info {
      flex: 1;
      padding-top: 10rpx;

      .company-name {
        font-size: 36rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 15rpx;
        line-height: 1.3;
      }

      .phone-number {
        font-size: 30rpx;
        color: #666;
        line-height: 1.3;
      }
    }
  }

  .detail-info {
    .info-item {
      display: flex;
      align-items: center;
      padding: 18rpx 0;

      &:last-child {
        border-bottom: none;
      }

      .info-label {
        font-size: 28rpx;
        color: #666;
        width: 160rpx;
        flex-shrink: 0;
      }

      .info-value {
        font-size: 28rpx;
        color: #333;
        flex: 1;
      }
    }
  }
}

.menu-section {
  .menu-group {
    background: white;
    border-radius: 20rpx;
    margin-bottom: 40rpx;
    overflow: hidden;

    .menu-item {
      display: flex;
      align-items: center;
      padding: 35rpx 40rpx;
      border-bottom: 1rpx solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .menu-icon {
        width: 50rpx;
        height: 50rpx;
        margin-right: 30rpx;
      }

      .menu-text {
        flex: 1;
        font-size: 30rpx;
        color: #333;
      }

      .menu-arrow {
        font-size: 28rpx;
        color: #ccc;
      }
    }
  }
}

.logout-section {
  margin-top: 60rpx;

  .logout-btn {
    width: 100%;
    height: 88rpx;
    line-height: 88rpx;
    background: #4A90E2;
    color: white;
    border: none;
    border-radius: 20rpx;
    font-size: 32rpx;
    font-weight: 500;
    box-shadow: 0 4rpx 20rpx rgba(74, 144, 226, 0.3);
  }
}
</style>
