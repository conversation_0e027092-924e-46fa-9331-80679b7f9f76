<template>
  <view v-if="visible" class="modal-overlay" @click="handleOverlayClick">
    <view class="modal-container" @click.stop>
      <!-- 标题栏 -->
      <view class="modal-header">
        <text class="modal-title">{{ title }}</text>
        <view class="close-btn" @click="handleClose">
          <text class="close-icon">×</text>
        </view>
      </view>
      
      <!-- 内容区域 - 插槽 -->
      <view class="modal-content">
        <slot></slot>
      </view>
      
      <!-- 底部按钮 -->
      <view class="modal-footer" v-if="showFooter">
        <view class="footer-btn cancel-btn" @click="handleClose" v-if="showCancel">
          {{ cancelText }}
        </view>
        <view class="footer-btn confirm-btn" @click="handleConfirm" v-if="showConfirm">
          {{ confirmText }}
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '提示'
  },
  showFooter: {
    type: Boolean,
    default: true
  },
  showCancel: {
    type: Boolean,
    default: true
  },
  showConfirm: {
    type: Boolean,
    default: true
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  confirmText: {
    type: String,
    default: '确定'
  },
  maskClosable: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['close', 'confirm', 'cancel'])

// 关闭弹框
const handleClose = () => {
  emit('close')
}

// 取消
const handleCancel = () => {
  emit('cancel')
  handleClose()
}

// 确认
const handleConfirm = () => {
  emit('confirm')
}

// 点击遮罩层关闭
const handleOverlayClick = () => {
  if (props.maskClosable) {
    handleClose()
  }
}
</script>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.modal-container {
  background-color: #fff;
  border-radius: 24rpx;
  width: 668rpx;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 40rpx 20rpx 40rpx;
  position: relative;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.close-icon {
  font-size: 48rpx;
  color: #999;
  line-height: 1;
}

.modal-content {
  padding: 20rpx 40rpx 40rpx 40rpx;
}

.modal-footer {
  display: flex;
  padding: 0 40rpx 40rpx 40rpx;
  gap: 20rpx;
}

.footer-btn {
  flex: 1;
  height: 88rpx;
  border-radius: 44rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 32rpx;
  font-weight: 500;
  
  &.cancel-btn {
    background-color: #f5f5f5;
    color: #666;
  }
  
  &.confirm-btn {
    background-color: #ffd700;
    color: #333;
  }
}

</style>
