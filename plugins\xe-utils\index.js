'use strict'

// 核心
import XEUtils from './ctor'

// 对象相关的方法
import assign from './assign'
import objectEach from './objectEach'
import lastObjectEach from './lastObjectEach'
import objectMap from './objectMap'
import merge from './merge'

// 数组相关的方法
import map from './map'
import some from './some'
import every from './every'
import includeArrays from './includeArrays'
import arrayEach from './arrayEach'
import lastArrayEach from './lastArrayEach'
import uniq from './uniq'
import union from './union'
import toArray from './toArray'
import sortBy from './sortBy'
import orderBy from './orderBy'
import shuffle from './shuffle'
import sample from './sample'
import slice from './slice'
import filter from './filter'
import findKey from './findKey'
import includes from './includes'
import find from './find'
import findLast from './findLast'
import reduce from './reduce'
import copyWithin from './copyWithin'
import chunk from './chunk'
import zip from './zip'
import unzip from './unzip'
import zipObject from './zipObject'
import flatten from './flatten'
import pluck from './pluck'
import invoke from './invoke'
import toArrayTree from './toArrayTree'
import toTreeArray from './toTreeArray'
import findTree from './findTree'
import eachTree from './eachTree'
import mapTree from './mapTree'
import filterTree from './filterTree'
import searchTree from './searchTree'
import arrayIndexOf from './arrayIndexOf'
import arrayLastIndexOf from './arrayLastIndexOf'

// 基础方法
import hasOwnProp from './hasOwnProp'
import isArray from './isArray'
import isNull from './isNull'
import isNumberNaN from './isNaN'
import isUndefined from './isUndefined'
import isFunction from './isFunction'
import isObject from './isObject'
import isString from './isString'
import isPlainObject from './isPlainObject'
import isLeapYear from './isLeapYear'
import isDate from './isDate'
import eqNull from './eqNull'
import each from './each'
import forOf from './forOf'
import lastForOf from './lastForOf'
import indexOf from './indexOf'
import lastIndexOf from './lastIndexOf'
import keys from './keys'
import values from './values'
import clone from './clone'
import getSize from './getSize'
import lastEach from './lastEach'
import remove from './remove'
import clear from './clear'
import isNumberFinite from './isFinite'
import isFloat from './isFloat'
import isInteger from './isInteger'
import isBoolean from './isBoolean'
import isNumber from './isNumber'
import isRegExp from './isRegExp'
import isError from './isError'
import isTypeError from './isTypeError'
import isEmpty from './isEmpty'
import isSymbol from './isSymbol'
import isArguments from './isArguments'
import isElement from './isElement'
import isDocument from './isDocument'
import isWindow from './isWindow'
import isFormData from './isFormData'
import isMap from './isMap'
import isWeakMap from './isWeakMap'
import isSet from './isSet'
import isWeakSet from './isWeakSet'
import isMatch from './isMatch'
import isEqual from './isEqual'
import isEqualWith from './isEqualWith'
import getType from './getType'
import uniqueId from './uniqueId'
import findIndexOf from './findIndexOf'
import findLastIndexOf from './findLastIndexOf'
import toStringJSON from './toStringJSON'
import toJSONString from './toJSONString'
import entries from './entries'
import pick from './pick'
import omit from './omit'
import first from './first'
import last from './last'
import has from './has'
import get from './get'
import set from './set'
import groupBy from './groupBy'
import countBy from './countBy'
import range from './range'
import destructuring from './destructuring'

// 数值相关方法
import random from './random'
import max from './max'
import min from './min'
import commafy from './commafy'
import round from './round'
import ceil from './ceil'
import floor from './floor'
import toFixed from './toFixed'
import toInteger from './toInteger'
import toNumber from './toNumber'
import toNumberString from './toNumberString'
import add from './add'
import subtract from './subtract'
import multiply from './multiply'
import divide from './divide'
import sum from './sum'
import mean from './mean'

// 日期相关的方法
import getWhatYear from './getWhatYear'
import getWhatQuarter from './getWhatQuarter'
import getWhatMonth from './getWhatMonth'
import getWhatDay from './getWhatDay'
import toStringDate from './toStringDate'
import toDateString from './toDateString'
import now from './now'
import timestamp from './timestamp'
import isValidDate from './isValidDate'
import isDateSame from './isDateSame'
import getWhatWeek from './getWhatWeek'
import getYearDay from './getYearDay'
import getYearWeek from './getYearWeek'
import getMonthWeek from './getMonthWeek'
import getDayOfYear from './getDayOfYear'
import getDayOfMonth from './getDayOfMonth'
import getDateDiff from './getDateDiff'

// 字符串相关的方法
import padEnd from './padEnd'
import padStart from './padStart'
import repeat from './repeat'
import trim from './trim'
import trimRight from './trimRight'
import trimLeft from './trimLeft'
import escape from './escape'
import unescape from './unescape'
import camelCase from './camelCase'
import kebabCase from './kebabCase'
import startsWith from './startsWith'
import endsWith from './endsWith'
import template from './template'
import toFormatString from './toFormatString'
import toValueString from './toValueString'

// 函数相关的方法
import noop from './noop'
import property from './property'
import bind from './bind'
import once from './once'
import after from './after'
import before from './before'
import throttle from './throttle'
import debounce from './debounce'
import delay from './delay'

// 地址相关的方法
import unserialize from './unserialize'
import serialize from './serialize'
import parseUrl from './parseUrl'

// 浏览器相关的方法
import getBaseURL from './getBaseURL'
import locat from './locat'
import cookie from './cookie'
import browse from './browse'

assign(XEUtils, {
  // object
  assign: assign,
  objectEach: objectEach,
  lastObjectEach: lastObjectEach,
  objectMap: objectMap,
  merge: merge,

  // array
  uniq: uniq,
  union: union,
  sortBy: sortBy,
  orderBy: orderBy,
  shuffle: shuffle,
  sample: sample,
  some: some,
  every: every,
  slice: slice,
  filter: filter,
  find: find,
  findLast: findLast,
  findKey: findKey,
  includes: includes,
  arrayIndexOf: arrayIndexOf,
  arrayLastIndexOf: arrayLastIndexOf,
  map: map,
  reduce: reduce,
  copyWithin: copyWithin,
  chunk: chunk,
  zip: zip,
  unzip: unzip,
  zipObject: zipObject,
  flatten: flatten,
  toArray: toArray,
  includeArrays: includeArrays,
  pluck: pluck,
  invoke: invoke,
  arrayEach: arrayEach,
  lastArrayEach: lastArrayEach,
  toArrayTree: toArrayTree,
  toTreeArray: toTreeArray,
  findTree: findTree,
  eachTree: eachTree,
  mapTree: mapTree,
  filterTree: filterTree,
  searchTree: searchTree,

  // base
  hasOwnProp: hasOwnProp,
  eqNull: eqNull,
  isNaN: isNumberNaN,
  isFinite: isNumberFinite,
  isUndefined: isUndefined,
  isArray: isArray,
  isFloat: isFloat,
  isInteger: isInteger,
  isFunction: isFunction,
  isBoolean: isBoolean,
  isString: isString,
  isNumber: isNumber,
  isRegExp: isRegExp,
  isObject: isObject,
  isPlainObject: isPlainObject,
  isDate: isDate,
  isError: isError,
  isTypeError: isTypeError,
  isEmpty: isEmpty,
  isNull: isNull,
  isSymbol: isSymbol,
  isArguments: isArguments,
  isElement: isElement,
  isDocument: isDocument,
  isWindow: isWindow,
  isFormData: isFormData,
  isMap: isMap,
  isWeakMap: isWeakMap,
  isSet: isSet,
  isWeakSet: isWeakSet,
  isLeapYear: isLeapYear,
  isMatch: isMatch,
  isEqual: isEqual,
  isEqualWith: isEqualWith,
  getType: getType,
  uniqueId: uniqueId,
  getSize: getSize,
  indexOf: indexOf,
  lastIndexOf: lastIndexOf,
  findIndexOf: findIndexOf,
  findLastIndexOf: findLastIndexOf,
  toStringJSON: toStringJSON,
  toJSONString: toJSONString,
  keys: keys,
  values: values,
  entries: entries,
  pick: pick,
  omit: omit,
  first: first,
  last: last,
  each: each,
  forOf: forOf,
  lastForOf: lastForOf,
  lastEach: lastEach,
  has: has,
  get: get,
  set: set,
  groupBy: groupBy,
  countBy: countBy,
  clone: clone,
  clear: clear,
  remove: remove,
  range: range,
  destructuring: destructuring,

  // number
  random: random,
  min: min,
  max: max,
  commafy: commafy,
  round: round,
  ceil: ceil,
  floor: floor,
  toFixed: toFixed,
  toNumber: toNumber,
  toNumberString: toNumberString,
  toInteger: toInteger,
  add: add,
  subtract: subtract,
  multiply: multiply,
  divide: divide,
  sum: sum,
  mean: mean,

  // date
  now: now,
  timestamp: timestamp,
  isValidDate: isValidDate,
  isDateSame: isDateSame,
  toStringDate: toStringDate,
  toDateString: toDateString,
  getWhatYear: getWhatYear,
  getWhatQuarter: getWhatQuarter,
  getWhatMonth: getWhatMonth,
  getWhatWeek: getWhatWeek,
  getWhatDay: getWhatDay,
  getYearDay: getYearDay,
  getYearWeek: getYearWeek,
  getMonthWeek: getMonthWeek,
  getDayOfYear: getDayOfYear,
  getDayOfMonth: getDayOfMonth,
  getDateDiff: getDateDiff,

  // string
  trim: trim,
  trimLeft: trimLeft,
  trimRight: trimRight,
  escape: escape,
  unescape: unescape,
  camelCase: camelCase,
  kebabCase: kebabCase,
  repeat: repeat,
  padStart: padStart,
  padEnd: padEnd,
  startsWith: startsWith,
  endsWith: endsWith,
  template: template,
  toFormatString: toFormatString,
  toString: toValueString,
  toValueString: toValueString,

  // function
  noop: noop,
  property: property,
  bind: bind,
  once: once,
  after: after,
  before: before,
  throttle: throttle,
  debounce: debounce,
  delay: delay,

  // url
  unserialize: unserialize,
  serialize: serialize,
  parseUrl: parseUrl,

  // web
  getBaseURL: getBaseURL,
  locat: locat,
  browse: browse,
  cookie: cookie
})



export default XEUtils
