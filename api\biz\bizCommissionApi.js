import request from '@/utils/request'

export default {
  /**
   * 获取账户详情
   * @param {Object} data - 请求参数
   * @param {string} data.entityId - 实体ID
   * @param {string} data.entityType - 实体类型
   * @returns {Promise} - 返回接口响应
   */
  getAccountDetail(data) {
    return request({
      url: '/account/detail',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取提现记录列表
   * @param {Object} data - 请求参数
   * @param {string} data.entityId - 实体ID
   * @param {string} data.entityType - 实体类型
   * @param {string} [data.startDate] - 开始日期
   * @param {string} [data.endDate] - 结束日期
   * @returns {Promise} - 返回接口响应
   */
  getCashList(data) {
    return request({
      url: '/cash/list',
      method: 'GET',
      data: data
    })
  },

  /**
   * 获取分佣记录列表
   * @param {Object} data - 请求参数
   * @param {string} data.entityId - 实体ID
   * @param {string} data.entityType - 实体类型
   * @param {string} [data.startDate] - 开始日期
   * @param {string} [data.endDate] - 结束日期
   * @returns {Promise} - 返回接口响应
   */
  getProfitOrderDetailList(data) {
    return request({
      url: '/profitOrder/detailList',
      method: 'GET',
      data: data
    })
  }
}
