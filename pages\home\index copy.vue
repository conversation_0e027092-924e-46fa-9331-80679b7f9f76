<!--
 * @Descripttion:
 * @version:
 * @Author: zhengyangyang
 * @Date: 2025-02-18 15:00:58
 * @LastEditors: zhengyangyang
 * @LastEditTime: 2025-02-18 18:48:54
-->
<template>
  <snowy-layout title="雷达" :isTabbar="true" :isFirstPage="true">
    <view class="container">
      <view class="tabs-header">
        <view class="tab active">线索列表</view>
        <view class="tab hidden">访问记录</view>
      </view>
      
      <scroll-view 
        class="clue-scroll-view"
        scroll-y 
        @scrolltolower="onScrollToLower" 
        @refresherrefresh="onRefresherRefresh"
        :refresher-enabled="true"
        :refresher-triggered="isRefreshing"
        refresher-background="#f1f4f9"
      >
        <view class="clue-list">
          <!-- 线索卡片 -->
          <view v-for="(item, index) in clueList" :key="index" class="clue-card" @click="goToClueDetail(item)">
            <view class="card-header">
              <view class="user-info">
                <image class="avatar" :src="item.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
                <view class="info-detail">
                  <view class="name-phone">
                    <text class="name">{{ item.name }}</text>
                    <text class="phone">{{ item.phone }}</text>
                  </view>
                  <view class="source">{{ item.source }}</view>
                </view>
              </view>
              <view class="status" :class="getStatusClass(item.status)">{{ item.statusText }}</view>
            </view>
    
            <view class="tags">
              <view v-for="(tag, tagIndex) in item.tags" :key="tagIndex" class="tag" :class="tag.type">{{ tag.name }}</view>
            </view>
            
            <view class="visit-info">
              <view class="info-item">
                <text class="label">首次访问</text>
                <text class="value">{{ item.firstVisitTime }}</text>
              </view>
              <view class="info-item">
                <text class="label">访问次数</text>
                <text class="value">{{ item.visitCount }}</text>
              </view>
              <view class="info-item">
                <text class="label">最后访问</text>
                <text class="value">{{ item.lastVisitTime }}</text>
              </view>
            </view>
          </view>
          
          <!-- 线索跟踪信息
          <view v-if="followUpInfo" class="follow-up-info">
            <view class="follow-text">线索跟踪：{{ followUpInfo.content }}</view>
            <view v-if="followUpInfo.images && followUpInfo.images.length > 0" class="image-preview">
              <image 
                v-for="(img, imgIndex) in followUpInfo.images" 
                :key="imgIndex" 
                class="preview-img" 
                :src="img" 
                mode="aspectFill"
              ></image>
            </view>
            <view class="follow-time">
              <text class="name">{{ followUpInfo.creator }}</text>
              <text class="time">{{ followUpInfo.createTime }}</text>
            </view>
          </view> -->
          
          <!-- 加载状态 -->
          <view v-if="isLoading" class="loading">
            <view class="loading-icon"></view>
            <text>加载中...</text>
          </view>
          <view v-else-if="!hasMore && clueList.length > 0" class="no-more">没有更多数据了</view>
          <view v-else-if="clueList.length === 0" class="empty">暂无数据</view>
        </view>
      </scroll-view>
    </view>
  </snowy-layout>
</template>
<script setup>
import { ref, computed, onMounted } from 'vue'
import { onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
import store from '@/store'
import bizClueApi from '@/api/biz/bizClueApi'

// 隐藏原生tabBar
uni.hideTabBar()

// 分享信息
const shareInfo = ref({})
const currentMenu = computed(() => {
  return store.getters.currentMenu
})
const handleSetShareInfo = (info) => {
  console.log(info, 'handleSetShareInfo')
  shareInfo.value = info
}

// 线索列表数据
const clueList = ref([])
const page = ref(1)
const pageSize = ref(10)
const hasMore = ref(true)
const isLoading = ref(false)
const isRefreshing = ref(false)
const followUpInfo = ref(null)

// 状态样式映射
const getStatusClass = (status) => {
  const statusMap = {
    0: 'waiting',    // 等待进
    1: 'expired',    // 已超时
    2: 'in-conversation'  // 洽谈中
  }
  return statusMap[status] || 'waiting'
}

// 获取线索列表数据
const getClueList = (refresh = false) => {
  if (refresh) {
    page.value = 1
    clueList.value = []
  }
  
  isLoading.value = true
  
  bizClueApi.clueList({
    current: page.value,
    size: pageSize.value
  }).then(res => {
    console.log(res, 'getClueList')
    if (true) {
      const records = res.records || []
      const formattedRecords = records.map(item => {
        // 处理状态
        const status = formatStatus(item.status)
        const statusText = getStatusText(item.status)
        
        return {
          id: item.id,
          userId: item.userId,
          name: item.name,
          phone: item.phone,
          avatar: item.avatar || '/static/images/default-avatar.png',
          source: formatSource(item.referrerType, item.referrerName),
          status: status,
          statusText: statusText,
          tags: formatTags(item),
          firstVisitTime: item.createTime || '',
          visitCount: item.visitCount || 0,
          lastVisitTime: item.updateTime || ''
        }
      })
      
      if (page.value === 1) {
        clueList.value = formattedRecords
      } else {
        clueList.value = [...clueList.value, ...formattedRecords]
      }
      
      hasMore.value = records.length === pageSize.value
    } else {
      uni.showToast({
        title: res.msg || '获取线索列表失败',
        icon: 'none'
      })
    }
    
    isLoading.value = false
    isRefreshing.value = false
  }).catch(err => {
    console.error('获取线索列表失败', err)
    uni.showToast({
      title: '获取线索列表失败',
      icon: 'none'
    })
    isLoading.value = false
    isRefreshing.value = false
  })
}

// 格式化来源信息
const formatSource = (referrerType, referrerName) => {
  if (!referrerType || !referrerName) return ''
  
  let sourceType = ''
  switch (referrerType) {
    case 'STAFF':
      sourceType = '员工分享'
      break
    case 'C_USER':
      sourceType = 'C端用户'
      break
    case 'REFERER':
      sourceType = '推荐官'
      break
    default:
      sourceType = '渠道码'
  }
  
  return `来自${sourceType} ${referrerName}`
}

// 格式化状态
const formatStatus = (status) => {
  // 将后端状态映射为前端状态
  const statusMap = {
    'TODO': 0,       // 待跟进
    'TIMEOUT': 1,    // 待跟进-已超时
    'PRE': 2,        // 初步沟通
    'ADVPAY': 2,     // 已支付预付款
    'FINISHED': 2    // 已完成
  }
  return statusMap[status] || 0
}

// 获取状态文字
const getStatusText = (status) => {
  // 将后端状态映射为前端状态文字
  const statusTextMap = {
    'TODO': '待跟进',
    'TIMEOUT': '待跟进-已超时',
    'PRE': '初步沟通',
    'ADVPAY': '已支付预付款',
    'FINISHED': '已完成'
  }
  return statusTextMap[status] || '待跟进'
}

// 格式化标签
const formatTags = (data) => {
  const tags = []
  
  // 添加客户类型标签
  if (data.customerType) {
    tags.push({
      name: data.customerType,
      type: 'blue'
    })
  }
  
  // 添加行业标签
  if (data.industry) {
    tags.push({
      name: data.industry,
      type: 'orange'
    })
  }
  
  // 添加职位标签
  if (data.position) {
    tags.push({
      name: data.position,
      type: 'grey'
    })
  }
  
  return tags
}

// 跳转到线索详情页
const goToClueDetail = (clue) => {
  uni.navigateTo({
    url: `/pages/home/<USER>
  })
}

// 滚动到底部加载更多
const onScrollToLower = () => {
  if (isLoading.value || !hasMore.value) return
  page.value++
  getClueList()
}

// 下拉刷新
const onRefresherRefresh = () => {
  isRefreshing.value = true
  getClueList(true)
}

// 页面加载时获取数据
onMounted(() => {
  getClueList()
})

// 定义页面的分享处理函数
onShareAppMessage(() => {
  return {
    ...shareInfo.value,
    success: (res) => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    },
    fail: (err) => {
      uni.showToast({
        title: '分享失败',
        icon: 'error'
      })
    }
  }
})

// 分享到朋友圈
onShareTimeline(() => {
  return {
    title: shareInfo.value?.title,
    query: `userInfo=刘飞`,
    imageUrl: shareInfo.value?.imageUrl
  }
})
</script>
<style lang="scss" scoped>
.container {
  background-color: rgba(241, 244, 249, 1);
  padding-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.tabs-header {
  display: flex;
  background-color: #fff;
  padding: 0 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
  justify-content: center;
}

.tab {
  padding: 20rpx 0;
  margin-right: 40rpx;
  font-size: 30rpx;
  position: relative;
  
  &.active {
    color: #337fff;
    font-weight: 500;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 4rpx;
      background-color: #337fff;
      border-radius: 2rpx;
    }
  }
  
  &.hidden {
    display: none;
  }
}

.clue-scroll-view {
  flex: 1;
  overflow-y: auto;
}

.clue-list {
  padding: 20rpx;
}

.clue-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.info-detail {
  display: flex;
  flex-direction: column;
}

.name-phone {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.name {
  font-size: 30rpx;
  font-weight: 500;
  margin-right: 20rpx;
}

.phone {
  font-size: 28rpx;
  color: #666;
}

.source {
  font-size: 24rpx;
  color: #999;
}

.status {
  padding: 8rpx 16rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  
  &.waiting {
    background-color: rgba(51, 127, 255, 0.1);
    color: #337fff;
  }
  
  &.expired {
    background-color: rgba(153, 153, 153, 0.1);
    color: #999;
  }
  
  &.in-conversation {
    background-color: rgba(82, 196, 26, 0.1);
    color: #52C41A;
  }
}

.tags {
  display: flex;
  margin-bottom: 20rpx;
}

.tag {
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-size: 22rpx;
  margin-right: 12rpx;
  
  &.blue {
    background-color: rgba(51, 127, 255, 0.1);
    color: #337fff;
  }
  
  &.orange {
    background-color: rgba(255, 152, 0, 0.1);
    color: #FF9800;
  }
  
  &.grey {
    background-color: rgba(153, 153, 153, 0.1);
    color: #999;
  }
}

.visit-info {
  display: flex;
  justify-content: space-between;
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 6rpx;
}

.value {
  font-size: 24rpx;
  color: #333;
}

.follow-up-info {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
}

.follow-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.image-preview {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.preview-img {
  width: 140rpx;
  height: 140rpx;
  border-radius: 8rpx;
  margin-right: 15rpx;
  margin-bottom: 15rpx;
}

.follow-time {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
}

.loading, .no-more, .empty {
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  
  .loading-icon {
    width: 32rpx;
    height: 32rpx;
    border: 3rpx solid #337fff;
    border-bottom-color: transparent;
    border-radius: 50%;
    margin-right: 10rpx;
    animation: loading 1s linear infinite;
  }
}

@keyframes loading {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.video-float-btn {
  position: fixed;
  bottom: 160rpx;
  right: 30rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #337fff;
  border-radius: 50%;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.video-icon {
  width: 60rpx;
  height: 60rpx;
}

.video-button-container {
  position: fixed;
  bottom: 100rpx;
  right: 30rpx;
  z-index: 999;
}

.video-button {
  background-color: #337fff;
  border-radius: 10rpx;
  padding: 10rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-text {
  color: #fff;
  font-size: 28rpx;
  margin-left: 10rpx;
}
</style>
