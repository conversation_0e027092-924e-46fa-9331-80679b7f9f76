<template>
  <snowy-layout title="商户看板" :isTabbar="true" :isFirstPage="true">
    <view class="merchant-board">
      <!-- 时间筛选标签 -->
      <view class="time-filter">
        <view class="filter-tabs">
          <view
            class="filter-tab"
            :class="{ active: activeTab === 'all' }"
            @click="switchTab('all')"
          >
            <text>全部</text>
          </view>
          <view
            class="filter-tab"
            :class="{ active: activeTab === 'today' }"
            @click="switchTab('today')"
          >
            <text>当天</text>
          </view>
          <view
            class="filter-tab"
            :class="{ active: activeTab === 'week' }"
            @click="switchTab('week')"
          >
            <text>最近7天</text>
          </view>
          <view
            class="filter-tab"
            :class="{ active: activeTab === 'month' }"
            @click="switchTab('month')"
          >
            <text>最近一个月</text>
          </view>
          <view
            class="filter-tab"
            :class="{ active: activeTab === 'more' }"
            @click="switchTab('more')"
          >
            <text>更多</text>
          </view>
        </view>
      </view>

      <!-- 任务列表 -->
      <view class="task-list">
        <!-- 表头 -->
        <view class="list-header">
          <view class="header-item task-name">任务名称</view>
          <view class="header-item visit-count">访问次数</view>
        </view>

        <!-- 任务数据 -->
        <scroll-view
          class="task-scroll"
          scroll-y="true"
          :refresher-enabled="true"
          :refresher-triggered="refreshing"
          @refresherrefresh="onRefresh"
        >
          <view v-if="taskList.length > 0" class="task-items">
            <view
              v-for="(item, index) in taskList"
              :key="index"
              class="task-item"
            >
              <view class="item-name">{{ item.taskName || '--' }}</view>
              <view class="item-count">{{ item.visitNum || 0 }}</view>
            </view>
          </view>

          <!-- 空状态 -->
          <view v-else class="empty-state">
            <text class="empty-text">暂无数据</text>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 日期范围选择弹框 -->
    <uni-popup ref="dateRangePopup" type="center" :mask-click="false">
      <view class="date-range-popup">
        <view class="popup-header">
          <text class="popup-title">选择日期范围</text>
          <view class="popup-close" @click="closeDateRangePopup">×</view>
        </view>
        <view class="popup-content">
          <uni-datetime-picker
            v-model="customDateRange"
            type="daterange"
            rangeSeparator="至"
            placeholder="选择日期范围"
            @change="onCustomDateChange"
          />
        </view>
        <view class="popup-footer">
          <view class="popup-btn cancel" @click="closeDateRangePopup">取消</view>
          <view class="popup-btn confirm" @click="confirmCustomDate">确定</view>
        </view>
      </view>
    </uni-popup>
  </snowy-layout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import bizInventoryApi from '@/api/biz/bizInventoryApi'

// 当前选中的时间筛选标签
const activeTab = ref('all')

// 日期范围选择相关
const dateRangePopup = ref(null)
const customDateRange = ref('')

// 任务列表数据
const taskList = ref([])

// 刷新状态
const refreshing = ref(false)

// 切换时间筛选标签
const switchTab = (tab) => {
  if (tab === 'more') {
    // 显示日期范围选择弹框
    dateRangePopup.value.open()
  } else {
    activeTab.value = tab
    loadData()
  }
}

// 日期范围选择相关函数
const onCustomDateChange = (value) => {
  customDateRange.value = value
}

const closeDateRangePopup = () => {
  dateRangePopup.value.close()
  // 重置为之前的选中状态
  if (activeTab.value === 'more') {
    activeTab.value = 'all'
  }
}

const confirmCustomDate = () => {
  if (!customDateRange.value) {
    uni.showToast({
      title: '请选择日期范围',
      icon: 'none'
    })
    return
  }

  activeTab.value = 'more'
  dateRangePopup.value.close()
  loadData()
}

// 下拉刷新
const onRefresh = async () => {
  refreshing.value = true
  try {
    await loadData()
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('刷新失败:', error)
  } finally {
    refreshing.value = false
  }
}

// 获取数据
const loadData = async () => {
  try {
    const params = {}

    // 根据选中的时间类型设置参数
    if (activeTab.value === 'more') {
      // 自定义日期范围
      if (customDateRange.value && Array.isArray(customDateRange.value)) {
        params.dateTimeStart = customDateRange.value[0]
        params.dateTimeEnd = customDateRange.value[1]
      }
    } else if (activeTab.value !== 'all') {
      // 预设时间类型
      const dateTypeMap = {
        'today': 0,   // 当天
        'week': 1,    // 最近7天
        'month': 2    // 最近一个月
      }
      params.dateType = dateTypeMap[activeTab.value]
    }

    const response = await bizInventoryApi.getMerchantBoard(params)

    taskList.value = response || []
  } catch (error) {
    console.error('获取数据失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  }
}

// 页面加载
onMounted(() => {
  loadData()
})
</script>

<style lang="scss" scoped>
.merchant-board {
  height: 100%;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
}

// 时间筛选标签
.time-filter {
  padding: 20rpx 30rpx;
  background: white;

  .filter-tabs {
    display: flex;
    justify-content: space-around;

    .filter-tab {
      position: relative;
      // padding: 20rpx 30rpx 0rpx 30rpx;
      font-size: 32rpx;
      color: #666;
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        color: rgba(255, 209, 0, 1);
        font-weight: 600;

        &::after {
          content: '';
          position: absolute;
          bottom: -10rpx;
          left: 50%;
          transform: translateX(-50%);
          width: 40rpx;
          height: 4rpx;
          background-color: rgba(255, 209, 0, 1);
          border-radius: 2rpx;
        }
      }
    }
  }
}

// 任务列表
.task-list {
  flex: 1;
  margin: 20rpx 30rpx;
  border-radius: 20rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .list-header {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;

    .header-item {
      padding: 30rpx 40rpx;
      font-size: 30rpx;
      color: rgba(0, 0, 0, 0.60);

      &.task-name {
        flex: 1;
      }

      &.visit-count {
        width: 200rpx;
        text-align: center;
      }
    }
  }

  .task-scroll {
    flex: 1;
    height: 0;

    .task-items {
      .task-item {
        display: flex;
        border-bottom: 1px solid #f0f0f0;
        transition: background-color 0.2s ease;
        background: white;
        &:last-child {
          border-bottom: none;
        }

        &:active {
          background-color: #f8f9fa;
        }

        .item-name {
          flex: 1;
          padding: 40rpx;
          font-size: 30rpx;
          color: #333;
          line-height: 1.4;
        }

        .item-count {
          width: 200rpx;
          padding: 40rpx 20rpx;
          font-size: 30rpx;
          font-weight: 600;
          color: rgba(0, 0, 0, 0.80);
          text-align: center;
          line-height: 1.4;
        }
      }
    }

    .empty-state {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 400rpx;

      .empty-text {
        font-size: 28rpx;
        color: #999;
      }
    }
  }
}

// 日期范围选择弹框样式
.date-range-popup {
  width: 600rpx;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1px solid #f0f0f0;

    .popup-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .popup-close {
      width: 60rpx;
      height: 60rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 40rpx;
      color: #999;
      cursor: pointer;
    }
  }

  .popup-content {
    padding: 40rpx 30rpx;
  }

  .popup-footer {
    display: flex;
    border-top: 1px solid #f0f0f0;

    .popup-btn {
      flex: 1;
      height: 100rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      cursor: pointer;

      &.cancel {
        color: #666;
        border-right: 1px solid #f0f0f0;
      }

      &.confirm {
        color: rgba(255, 209, 0, 1);
        font-weight: 600;
      }
    }
  }
}
</style>