<!--
 * @Descripttion:
 * @version:
 * @Author: zhengyangyang
 * @Date: 2025-02-18 15:00:58
 * @LastEditors: zhengyangyang
 * @LastEditTime: 2025-02-18 18:48:54
-->
<template>
  <snowy-layout title="我的" :isTabbar="true" :isFirstPage="true">
    <view class="container">
      <!-- 个人信息卡片 -->
      <view class="user-card">
        <!-- 头像和基本信息 -->
        <view class="user-info-row">
          <view class="info-label">头像</view>
          <view class="avatar-container">
            <image class="avatar" :src="userInfo.avatar || '/static/images/default-avatar.png'" mode="aspectFill">
            </image>
          </view>
        </view>
        <view class="user-info-row">
          <view class="info-label">姓名</view>
          <view class="info-value">{{ userInfo.name || '张三' }}</view>
        </view>
        <view class="user-info-row">
          <view class="info-label">性别</view>
          <view class="info-value">{{ userInfo.gender === 'male' ? '男' : '女' }}</view>
        </view>
      </view>
      <view class="user-card">
        <!-- 联系方式信息 -->
        <view class="user-info-row">
          <view class="info-label">名片展示手机号</view>
          <view class="info-value">{{ userInfo.cardPhone || '18845623456' }}</view>
        </view>
        <view class="user-info-row">
          <view class="info-label">名片展示微信号</view>
          <view class="info-value">{{ userInfo.cardWxId || '18845623456' }}</view>
        </view>
        <view class="user-info-row">
          <view class="info-label">名片展示邮箱</view>
          <view class="info-value">{{ userInfo.cardEmail || '18845623456' }}</view>
        </view>
        <view class="user-info-row">
          <view class="info-label">名片展示地址</view>
          <view class="info-value">{{ userInfo.cardAddress || '18845623456' }}</view>
        </view>
      </view>

      <!-- 套餐信息  v-if="packageInfo.name" -->
      <view v-if="packageInfo.name" class="package-card">
        <view class="package-header">
          <text class="package-title">我的套餐</text>
        </view>
        <view class="package-info-row">
          <view class="package-name-row">
            <text class="package-name">{{ packageInfo.name || '我是套餐名称' }}</text>
            <text class="package-remaining">剩余天数：<text class="days-count">{{ packageInfo.remainingDays || '28'
                }}</text>天</text>
          </view>
          <view class="view-details-row">
            <text class="view-details" @click="viewPackageDetails">查看详情</text>
          </view>
        </view>
      </view>

      <!-- 功能按钮区 -->
      <view class="function-buttons">
        <view class="function-button" @click="handleNewRecommender">
          <image class="function-icon" src="/static/images/my/my-i1.png" mode="aspectFit"></image>
          <text class="function-text">新建推荐官</text>
        </view>
        <view class="function-button" @click="handleMyRecommenders">
          <image class="function-icon" src="/static/images/my/my-i2.png" mode="aspectFit"></image>
          <text class="function-text">我的推荐官</text>
        </view>
        <view class="function-button" @click="handlePaymentRecord">
          <image class="function-icon" src="/static/images/my/jy-icon.png" mode="aspectFit"></image>
          <text class="function-text">付款记录</text>
        </view>
        <view class="function-button" @click="handleMyCommission">
          <image class="function-icon" src="/static/images/my/yj-icon.png" mode="aspectFit"></image>
          <text class="function-text">我的佣金</text>
        </view>
        <!-- <view class="function-button" @click="handleCardEdit">
          <image class="function-icon" src="/static/images/my/my-i3.png" mode="aspectFit"></image>
          <text class="function-text">名片修改</text>
        </view> -->
      </view>

      <!-- 退出登录按钮 -->
      <view class="logout-button" @click="handleLogout">
        <text>退出登录</text>
      </view>
    </view>
  </snowy-layout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import store from '@/store'
import loginApi from '@/api/auth/login-api'
import bizMerchantApi from '@/api/biz/bizMerchantApi'
import bizCardApi from '@/api/biz/bizCardApi'

// 隐藏原生tabBar
uni.hideTabBar()

const currentMenu = computed(() => {
  return store.getters.currentMenu
})

// 用户信息
const userInfo = ref({
  avatar: '',
  name: '',
  gender: '男',
  phone: '',
  wechat: '',
  email: '',
  address: ''
})

// 套餐信息
const packageInfo = ref({
  name: '',
  remainingDays: 0
})

// 获取用户信息
const getUserInfo = async () => {
  try {
    const res = await bizMerchantApi.getOwner()
    if (res) {
      console.log(res, 'getUserInfo');
      userInfo.value = res
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}

// 获取名片信息
const getCardInfo = async () => {
  try {
    const res = await bizCardApi.getOwner()
    if (res) {
      const card = res.staffVo.card
      userInfo.value.cardPhone = card.cardPhone || ''
      userInfo.value.cardWxId = card.cardWxId || ''
      userInfo.value.cardEmail = card.cardEmail || ''
      userInfo.value.cardAddress = card.cardAddress || ''
    }
  } catch (error) {
    console.error('获取名片信息失败:', error)
  }
}

// 获取商户信息和套餐信息
const getMerchantInfo = async () => {
  try {
    const res = await bizMerchantApi.getOwner()
    if (res) {
      // 处理套餐信息
      if (res.cpackage) {
        packageInfo.value.name = res.cpackage.cpackageName || '标准版'

        // 计算剩余天数
        if (res.cpackage.effectEndTime) {
          const endDate = new Date(res.cpackage.effectEndTime)
          const today = new Date()

          // 计算天数差异
          const diffTime = endDate.getTime() - today.getTime()
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

          packageInfo.value.remainingDays = diffDays > 0 ? diffDays : 0
        }
      }
    }
  } catch (error) {
    console.error('获取商户信息失败:', error)
  }
}

// 查看套餐详情
const viewPackageDetails = () => {
  // 跳转到套餐详情页面
  uni.navigateTo({
    url: '/pages/mine/package-detail'
  })
}

// 新建推荐官
const handleNewRecommender = () => {
  uni.navigateTo({
    url: '/pages/mine/recommender/new'
  })
}

// 我的推荐官
const handleMyRecommenders = () => {
  uni.navigateTo({
    url: '/pages/mine/recommender/list'
  })
}

// 我的佣金
const handleMyCommission = () => {
  uni.navigateTo({
    url: '/pages/mine/commission'
  })
}

// 付款记录
const handlePaymentRecord = () => {
  uni.navigateTo({
    url: '/pages/mine/payment-record'
  })
}

// 名片修改
const handleCardEdit = () => {
  // uni.navigateTo({
  //   url: '/pages/mine/info/edit'
  // })
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          await loginApi.logout()
          uni.clearStorageSync()
          uni.reLaunch({
            url: '/pages/auth/login'
          })
        } catch (error) {
          console.error('退出登录失败:', error)
        }
      }
    }
  })
}

onMounted(async () => {
  await getUserInfo()
  await getMerchantInfo()
  await getCardInfo()
})
</script>

<style lang="scss" scoped>
.container {
  background-color: rgba(241, 244, 249, 1);
  min-height: 100vh;
  padding: 20rpx;
}

.user-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 0 30rpx;
  margin-bottom: 20rpx;
}

.user-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 94rpx; // 增加行高
  border-bottom: 1px solid #f5f5f5;

  &:last-child {
    border-bottom: none;
  }
}

.divider {
  height: 16rpx;
  background-color: #f5f5f5;
  margin: 0 -30rpx; // 让分隔线延展到容器边缘
}

.section-divider {
  height: 16rpx;
  background-color: transparent;
}

.info-label {
  font-size: 28rpx;
  color: #333;
}

.info-value {
  font-size: 28rpx;
  color: #333;
}

.avatar-container {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid #eee;
}

.avatar {
  width: 100%;
  height: 100%;
}

.package-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
}

.package-header {
  margin-bottom: 10rpx;
}

.package-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.package-info-row {
  padding: 16rpx 0;
}

.package-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6rpx;
}

.view-details-row {
  display: flex;
  justify-content: flex-end;
  margin-top: 16rpx;
}

.package-name {
  font-size: 28rpx;
  color: #333;
}

.package-remaining {
  font-size: 26rpx;
  color: #666;
}

.days-count {
  color: #ff3b30;
  font-weight: 500;
}

.view-details {
  color: #1989fa;
  font-size: 26rpx;
  text-align: right;
}

.function-buttons {
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.function-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 33%;
}

.function-icon {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 16rpx;
}

.function-text {
  font-size: 26rpx;
  color: #333;
}

.logout-button {
  background-color: #ffcc00;
  color: #333;
  text-align: center;
  height: 100rpx; // 增加高度
  line-height: 100rpx;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: 500;
  margin-top: 40rpx;
}
</style>
