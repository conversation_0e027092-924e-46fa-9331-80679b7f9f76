<!--
 * @Descripttion: 线索详情页
 * @version:
 * @Author: zhengyangyang
 * @Date: 2025-02-20 10:00:00
 * @LastEditors: zhengyangyang
 * @LastEditTime: 2025-02-20 10:00:00
-->
<template>
  <snowy-layout title="线索详情" :isTabbar="false" :isFirstPage="false">
    <view class="container">
      <!-- 顶部用户信息 -->
      <view class="user-info-card">
        <view class="card-header">
          <view class="user-info">
            <image class="avatar" :src="clueInfo.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
            <view class="info-detail">
              <view class="name-phone">
                <text class="name">{{ clueInfo.name }}</text>
                <text class="phone">{{ clueInfo.phone }}</text>
              </view>
              <view class="source">{{ clueInfo.source }}</view>
            </view>
          </view>
          <view class="status" :class="getStatusClass(clueInfo.status)">{{ clueInfo.statusText }}</view>
        </view>

        <view class="tags">
          <view v-for="(tag, tagIndex) in clueInfo.tags" :key="tagIndex" class="tag" :class="tag.type">{{ tag.name }}</view>
          <view v-if="clueInfo.productCategory" class="tag grey">{{ clueInfo.productCategory }}</view>
        </view>

        <view class="visit-info">
          <view class="info-item">
            <text class="label">首次访问</text>
            <text class="value">{{ clueInfo.createTime }}</text>
          </view>
          <view class="info-item">
            <text class="label">访问次数</text>
            <text class="value">{{ clueInfo.visitCount }}</text>
          </view>
          <view class="info-item">
            <text class="label">最后访问</text>
            <text class="value">{{ clueInfo.updateTime }}</text>
          </view>
        </view>
      </view>

      <!-- 线索进度跟踪 -->
      <view v-if="clueInfo.trackingContent" class="tracking-card">
        <view class="tracking-text">备注：{{ clueInfo.remark }}</view>
      </view>

      <!-- 选项卡 -->
      <view class="tabs">
        <view class="tab active">
          访问记录
          <view class="active-line"></view>
        </view>
      </view>

      <!-- 选项卡内容 -->
      <view
        class="tab-content-scroll"
        scroll-y
        @scrolltolower="loadMoreVisitRecords"
        :refresher-enabled="true"
        @refresherrefresh="refreshVisitRecords"
        :refresher-triggered="isRefreshing"
      >
        <!-- 访问记录 -->
        <view class="visit-record-content">
          <view class="timeline">
            <view v-for="(item, index) in visitRecords" :key="index" class="timeline-item">
              <view class="timeline-dot"></view>
              <view class="timeline-line" v-if="index !== visitRecords.length - 1"></view>
              <view class="timeline-content">
                <view class="timeline-time">{{ item.time }}</view>
                <view class="timeline-text" v-html="formatContent(item.content)"></view>
              </view>
            </view>
          </view>

          <!-- 加载状态 -->
          <view v-if="isVisitLoading" class="loading">
            <view class="loading-icon"></view>
            <text>加载中...</text>
          </view>
          <view v-else-if="!hasMoreVisit && visitRecords.length > 0" class="no-more">没有更多数据了</view>
        </view>
      </view>
    </view>
  </snowy-layout>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import bizClueApi from '@/api/biz/bizClueApi'

// 线索ID
const clueId = ref('')
const userId = ref('')
// 选项卡
const tabs = ref([{ name: '访问记录' }])
const activeTabIndex = ref(0)

// 线索详情
const clueInfo = ref({})
const timelineItems = ref([])
const visitRecords = ref([])
const lifecycleRecords = ref([])

// 加载状态
const isLoading = ref(false)
const isVisitLoading = ref(false)
const isLifecycleLoading = ref(false)
const isRefreshing = ref(false)
const hasMore = ref(true)
const hasMoreVisit = ref(true)
const hasMoreLifecycle = ref(true)
const page = ref({
  progress: 1,
  visit: 1,
  lifecycle: 1
})
const pageSize = ref(10)

// 状态样式映射
const getStatusClass = (status) => {
  const statusMap = {
    0: 'waiting', // 等待进
    1: 'expired', // 已超时
    2: 'in-conversation' // 洽谈中
  }
  return statusMap[status] || 'waiting'
}

// 获取线索详情
const getClueDetail = () => {
  if (!clueId.value) return

  isLoading.value = true
  bizClueApi
    .clueDetail({
      id: clueId.value
    })
    .then((res) => {
      // 处理后端返回的数据
      const data = res
      clueInfo.value = {
        ...data,
        source: formatSource(data.referrerType, data.referrerName),
        statusText: getStatusText(data.status)
      }
      // 获取访问记录
      getVisitRecords(true)
    })
    .catch((err) => {
      console.error('获取线索详情失败', err)
      uni.showToast({
        title: '获取线索详情失败',
        icon: 'none'
      })
    })
    .finally(() => {
      isLoading.value = false
    })
}

// 格式化来源信息
const formatSource = (referrerType, referrerName) => {
  if (!referrerType || !referrerName) return ''

  let sourceType = ''
  switch (referrerType) {
    case 'STAFF':
      sourceType = '员工分享'
      break
    case 'C_USER':
      sourceType = 'C端用户'
      break
    case 'REFERER':
      sourceType = '推荐官'
      break
    default:
      sourceType = '渠道码'
  }

  return `来自${sourceType} ${referrerName}`
}

// 格式化状态
const formatStatus = (status) => {
  // 将后端状态映射为前端状态
  const statusMap = {
    TODO: 0, // 待跟进
    TIMEOUT: 1, // 待跟进-已超时
    PRE: 2, // 初步沟通
    ADVPAY: 2, // 已支付预付款
    FINISHED: 2 // 已完成
  }
  return statusMap[status] || 0
}

// 获取状态文字
const getStatusText = (status) => {
  // 将后端状态映射为前端状态文字
  const statusTextMap = {
    TODO: '待跟进',
    TIMEOUT: '待跟进-已超时',
    PRE: '初步沟通',
    ADVPAY: '已支付预付款',
    FINISHED: '已完成'
  }
  return statusTextMap[status] || '待跟进'
}

// 格式化标签
const formatTags = (data) => {
  const tags = []

  // 添加客户类型标签
  if (data.customerType) {
    tags.push({
      name: data.customerType,
      type: 'blue'
    })
  }

  // 添加行业标签
  if (data.industry) {
    tags.push({
      name: data.industry,
      type: 'orange'
    })
  }

  // 添加职位标签
  if (data.position) {
    tags.push({
      name: data.position,
      type: 'grey'
    })
  }

  return tags
}

// 获取访问记录
const getVisitRecords = (refresh = false) => {
  if (refresh) {
    page.value.visit = 1
    visitRecords.value = []
  }

  isVisitLoading.value = true

  bizClueApi
    .accessRecordQuery({
      userId: userId.value,
      clueId: clueId.value,
      current: page.value.visit,
      size: pageSize.value
    })
    .then((res) => {
      if (true) {
        const records = res || []
        const formattedRecords = records.map((item) => {
          // 处理后端返回的访问记录数据
          // 1. 打开名片，记录动作。
          // * 2. 产品：记录动作，记录停留时间。
          // * 3. 企业动态：记录动作，记录停留时间。
          // * 4. 宣传册：记录动作，记录停留时间。
          // * 5. 案例：记录动作，记录停留时间。
          // * 6. 名片分享(名片页面分享按钮)：记录动作。
          // * 7. 保存通讯录(名片页面保存通讯录按钮)，记录动作。
          let content = ''
          if (item.action === '1') {
            content = `打开${item.keyword || ''}名片`
          } else if (item.action === '2') {
            console.log(item)
            content = `第${item.count || 1}次浏览产品《${item.keyword || ''}》，观看了${formatDuration(item.cost || 0)}。`
          } else if (item.action === '3') {
            content = `第${item.count || 1}次浏览企业动态《${item.keyword || ''}》，观看了${formatDuration(item.cost || 0)}。`
          } else if (item.action === '4') {
            content = `第${item.count || 1}次浏览宣传册《${item.keyword || ''}》，观看了${formatDuration(item.cost || 0)}。`
          } else if (item.action === '5') {
            content = `第${item.count || 1}次浏览案例《${item.keyword || ''}》，观看了${formatDuration(item.cost || 0)}。`
          } else if (item.action === '6') {
            content = `将${item.keyword || ''}的名片分享给好友`
          } else if (item.action === '7') {
            content = `保存通讯录`
          }
          return {
            time: item.startDate || '',
            content: content
          }
        })
        console.log(formattedRecords)

        visitRecords.value = formattedRecords
        hasMoreVisit.value = records.length === pageSize.value
      }
    })
    .catch((err) => {
      console.error('获取访问记录失败', err)
      uni.showToast({
        title: '获取访问记录失败',
        icon: 'none'
      })
    })
    .finally(() => {
      isVisitLoading.value = false
      isRefreshing.value = false
    })
}

// 格式化时长
const formatDuration = (seconds) => {
  if (!seconds) return '0秒'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const remainingSeconds = seconds % 60

  let result = ''
  if (hours > 0) {
    result += `${hours}小时`
  }
  if (minutes > 0) {
    result += `${minutes}分`
  }
  if (remainingSeconds > 0) {
    result += `${remainingSeconds}秒`
  }

  return result || '0秒'
}

// 格式化内容，处理链接
const formatContent = (content) => {
  // 替换《》中的内容为蓝色链接
  return content.replace(/《([^》]+)》/g, '<span class="link-text" style="color: #337fff;">《$1》</span>')
}

// 加载更多访问记录
const loadMoreVisitRecords = () => {
  // if (!isVisitLoading.value && hasMoreVisit.value) {
  //   page.value.visit++
  //   getVisitRecords()
  // }
}

// 刷新访问记录
const refreshVisitRecords = () => {
  isRefreshing.value = true
  getVisitRecords(true)
}

// 获取时间轴数据
const getTimelineItems = (refresh = false) => {
  if (refresh) {
    page.value.progress = 1
    timelineItems.value = []
  }

  isLoading.value = true

  // 模拟请求
  setTimeout(() => {
    // Mock数据
    const mockData = generateTimelineData(page.value.progress, pageSize.value)

    if (page.value.progress === 1) {
      timelineItems.value = mockData.list
    } else {
      timelineItems.value = [...timelineItems.value, ...mockData.list]
    }

    hasMore.value = mockData.hasMore
    isLoading.value = false
    isRefreshing.value = false
  }, 500)
}

// 获取生命周期记录
const getLifecycleRecords = (refresh = false) => {
  if (refresh) {
    page.value.lifecycle = 1
    lifecycleRecords.value = []
  }

  isLifecycleLoading.value = true

  // 模拟请求
  setTimeout(() => {
    // Mock数据
    const mockData = generateLifecycleRecords(page.value.lifecycle, pageSize.value)

    if (page.value.lifecycle === 1) {
      lifecycleRecords.value = mockData.list
    } else {
      lifecycleRecords.value = [...lifecycleRecords.value, ...mockData.list]
    }

    hasMoreLifecycle.value = mockData.hasMore
    isLifecycleLoading.value = false
    isRefreshing.value = false
  }, 500)
}

// 生成时间轴数据
const generateTimelineData = (page, pageSize) => {
  const list = []
  const totalPages = 2

  if (page > totalPages) {
    return {
      list: [],
      hasMore: false
    }
  }

  for (let i = 0; i < pageSize; i++) {
    const index = (page - 1) * pageSize + i
    if (index >= 10) break

    list.push({
      id: `timeline_${index}`,
      time: '2024-07-29 19:24',
      content: `第${10 - index}次推送内容《数字经验发展论坛》，观看了${10 - index}小时${22}分钟`,
      images: index % 3 === 0 ? ['/static/images/preview1.png', '/static/images/preview2.png', '/static/images/preview3.png'] : []
    })
  }

  return {
    list,
    hasMore: page < totalPages
  }
}

// 生成访问记录
const generateVisitRecords = (page, pageSize) => {
  const list = []
  const totalPages = 2

  if (page > totalPages) {
    return {
      list: [],
      hasMore: false
    }
  }

  const visitItems = [
    {
      time: '2024-07-29 19:24',
      content: '第2次浏览资讯《数字经路发展论坛》，观看了1小时2分12秒。'
    },
    {
      time: '2024-07-29 19:24',
      content: '将李四的名片分享给好友，建议保持关注且留意新增商机。'
    },
    {
      time: '2024-07-29 19:24',
      content: '第2次浏览宣传册《小程序名片系统》，观看了1小时2分12秒。'
    },
    {
      time: '2024-07-29 19:24',
      content: '第1次浏览资讯《数字丝路发展论坛》，观看了1小时2分12秒。'
    },
    {
      time: '2024-07-29 19:24',
      content: '第1次浏览产品《数字丝路发展论坛》，观看了1小时2分12秒。'
    },
    {
      time: '2024-07-29 19:24',
      content: '第2次浏览文章《数字丝路发展》，观看了2分12秒。'
    },
    {
      time: '2024-07-29 19:24',
      content: '第1次浏览视频《数字丝路发展论坛》，观看了1小时2分12秒。'
    }
  ]

  // 计算应添加的记录数量
  const startIndex = (page - 1) * pageSize
  const endIndex = Math.min(startIndex + pageSize, visitItems.length)

  // 如果没有更多记录了，则返回空列表
  if (startIndex >= visitItems.length) {
    return {
      list: [],
      hasMore: false
    }
  }

  // 添加记录
  for (let i = startIndex; i < endIndex; i++) {
    list.push({
      id: `visit_${i}`,
      ...visitItems[i]
    })
  }

  return {
    list,
    hasMore: endIndex < visitItems.length
  }
}

// 生成生命周期记录
const generateLifecycleRecords = (page, pageSize) => {
  const list = []
  const totalPages = 2

  if (page > totalPages) {
    return {
      list: [],
      hasMore: false
    }
  }

  for (let i = 0; i < pageSize; i++) {
    const index = (page - 1) * pageSize + i
    if (index >= 10) break

    list.push({
      id: `lifecycle_${index}`,
      time: '2024-07-29 19:24',
      content: `线索状态由"未分配"变更为"处理中"`,
      operator: '系统自动'
    })
  }

  return {
    list,
    hasMore: page < totalPages
  }
}

// 页面加载
onLoad((options) => {
  if (options.id) {
    clueId.value = options.id
    userId.value = options.userId
    getClueDetail()
  }
})
</script>

<style lang="scss" scoped>
.container {
  background-color: #f1f4f9;
  padding: 20rpx;
}

.user-info-card {
  background-color: #fff;
  padding: 24rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.info-detail {
  display: flex;
  flex-direction: column;
}

.name-phone {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.name {
  font-size: 30rpx;
  font-weight: 500;
  margin-right: 20rpx;
}

.phone {
  font-size: 28rpx;
  color: #666;
}

.source {
  font-size: 24rpx;
  color: #999;
}

.status {
  padding: 8rpx 16rpx;
  border-radius: 30rpx;
  font-size: 24rpx;

  &.waiting {
    background-color: rgba(51, 127, 255, 0.1);
    color: #337fff;
  }

  &.expired {
    background-color: rgba(153, 153, 153, 0.1);
    color: #999;
  }

  &.in-conversation {
    background-color: rgba(82, 196, 26, 0.1);
    color: #52c41a;
  }
}

.tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.tag {
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  font-size: 22rpx;
  margin-right: 12rpx;
  margin-bottom: 10rpx;

  &.blue {
    background-color: rgba(51, 127, 255, 0.1);
    color: #337fff;
  }

  &.orange {
    background-color: rgba(255, 152, 0, 0.1);
    color: #ff9800;
  }

  &.grey {
    background-color: rgba(153, 153, 153, 0.1);
    color: #999;
  }
}

.visit-info {
  display: flex;
  justify-content: space-between;
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.label {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 6rpx;
}

.value {
  font-size: 24rpx;
  color: #333;
}

.tracking-card {
  background-color: #fff;
  padding: 24rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

.tracking-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.edit-link {
  color: #337fff;
  margin-left: 10rpx;
}

.tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 10rpx;
  background-color: #f1f4f9;
  position: relative;
}

.tab {
  position: relative;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #337fff;
  font-weight: 500;
  width: 120rpx;
  text-align: center;
}

.active-line {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #337fff;
  border-radius: 2rpx;
  z-index: 1;
}

.tab-content-scroll {
  background-color: transparent;
  overflow-y: auto;
  box-sizing: border-box;
}

.visit-record-content {
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.timeline {
  position: relative;
  padding-left: 40rpx;
}

.timeline-item {
  position: relative;
  padding-bottom: 30rpx;
}

.timeline-dot {
  position: absolute;
  left: -38rpx;
  top: 6rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #337fff;
  z-index: 1;
}

.timeline-line {
  position: absolute;
  left: -30rpx;
  top: 26rpx;
  width: 2rpx;
  height: calc(100% - 6rpx);
  background-color: #e0e0e0;
  border-left: 2rpx dashed #e0e0e0;
  background-color: transparent;
}

.timeline-content {
  padding: 10rpx 0;
  border-radius: 0;
}

.timeline-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.timeline-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.link-text {
  color: #337fff;
}

.loading,
.no-more {
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;

  .loading-icon {
    width: 32rpx;
    height: 32rpx;
    border: 3rpx solid #337fff;
    border-bottom-color: transparent;
    border-radius: 50%;
    margin-right: 10rpx;
    animation: loading 1s linear infinite;
  }
}

@keyframes loading {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
