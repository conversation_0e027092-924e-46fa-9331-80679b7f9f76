<template>
  <snowy-layout title="新建合伙人" :isTabbar="false" :isFirstPage="false">
    <view class="second-add-page">
      <uni-forms ref="formRef" :modelValue="formData" :rules="rules">
        <view class="form-container">
          <!-- 合伙人姓名 -->
          <uni-forms-item name="name" required>
            <view class="form-item">
              <view class="form-label required">
                <text>合伙人姓名</text>
              </view>
              <uni-easyinput v-model="formData.name" placeholder="请输入合伙人姓名" :maxlength="20" :border="false"
                :clearable="false" :styles="inputStyles" />
            </view>
          </uni-forms-item>

          <!-- 身份证号码 -->
          <uni-forms-item name="idName">
            <view class="form-item">
              <view class="form-label noRequired">
                <text>身份证号码</text>
              </view>
              <uni-easyinput v-model="formData.idName" placeholder="请输入证件号码" :maxlength="18" :border="false"
                :clearable="false" :styles="inputStyles" />
            </view>
          </uni-forms-item>

          <!-- 手机号码 -->
          <uni-forms-item name="contactPhone" required>
            <view class="form-item">
              <view class="form-label required">
                <text>手机号码</text>
              </view>
              <uni-easyinput v-model="formData.contactPhone" placeholder="请输入手机号码" type="number" :maxlength="11"
                :border="false" :clearable="false" :styles="inputStyles" />
            </view>
          </uni-forms-item>

          <!-- 备注 -->
          <uni-forms-item name="remark">
            <view class="form-item textarea-item">
              <view class="form-label noRequired">
                <text>备注</text>
              </view>
            </view>
            <view class="textarea-container">
              <uni-easyinput v-model="formData.remark" type="textarea" placeholder="请输入备注" :maxlength="200"
                :border="false" :styles="textareaStyles" />
            </view>
          </uni-forms-item>
        </view>
      </uni-forms>

      <!-- 提交按钮 -->
      <view class="submit-section">
        <button class="submit-btn" @click="handleSubmit">
          提交
        </button>
      </view>
    </view>
  </snowy-layout>
</template>

<script setup>
import { ref } from 'vue'
import { useStore } from 'vuex'

const store = useStore()

// 表单引用
const formRef = ref()

// 表单数据
const formData = ref({
  name: '',
  idName: '',
  contactPhone: '',
  remark: ''
})

// 提交状态
const isSubmitting = ref(false)

// 表单验证规则
const rules = ref({
  name: {
    rules: [
      {
        required: true,
        errorMessage: '请输入合伙人姓名'
      },
      {
        minLength: 2,
        maxLength: 20,
        errorMessage: '姓名长度应为2-20个字符'
      }
    ]
  },
  contactPhone: {
    rules: [
      {
        required: true,
        errorMessage: '请输入手机号码'
      },
      {
        pattern: /^1[3-9]\d{9}$/,
        errorMessage: '请输入正确的手机号码'
      }
    ]
  },
  idName: {
    rules: [
      {
        pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
        errorMessage: '请输入正确的身份证号码'
      }
    ]
  }
})

// 输入框样式
const inputStyles = ref({
  color: '#333',
  backgroundColor: 'transparent',
  borderColor: 'transparent',
  placeholderColor: '#ccc'
})

// 文本域样式
const textareaStyles = ref({
  color: '#333',
  backgroundColor: '#f8f9fa',
  borderColor: 'transparent',
  placeholderColor: '#ccc'
})

// 提交表单
const handleSubmit = async () => {
  if (isSubmitting.value) return

  try {
    // 使用uni-forms验证
    const valid = await formRef.value.validate()
    if (!valid) return
    const roleApp=store.getters.roleApp||{}
    isSubmitting.value = true
    formData.value.parentId = roleApp.entityId
    formData.value.level = 2
    // 调用新增接口
    const response = await store.dispatch('partner/addSecondPartner', formData.value)

    if (response.success) {
      uni.showToast({
        title: '新建成功',
        icon: 'success'
      })

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    } else {
      uni.showToast({
        title: response.message || '新建失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('新建合伙人失败:', error)
    if (error.errorMessage || error.length) {
      // 表单验证错误
      uni.showToast({
        title: error.errorMessage,
        icon: 'none'
      })
    } else {
      uni.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
    }
  } finally {
    isSubmitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.second-add-page {
  min-height: 100%;
  background-color: #f5f5f5;
  padding: 30rpx 0 0 0;
  box-sizing: border-box;
  position: relative;
}

.form-container {
  background: white;
  border-radius: 20rpx;
  padding: 0;
  overflow: hidden;
}

.form-item {
  display: flex;
  align-items: center;
  padding: 20rpx;

  &.textarea-item {
    padding-bottom: 20rpx;
  }
}

.form-label {
  font-size: 32rpx;
  color: #333;
  width: 200rpx;
  flex-shrink: 0;
  position: relative;

  &.required::before {
    content: '*';
    color: #ff4757;
    margin-right: 8rpx;
  }

  &.noRequired::before {
    content: '';
    margin-right: 47rpx;
  }
}

// uni-easyinput 样式通过 styles 属性控制
:deep(.uni-easyinput__content) {
  flex: 1;
  text-align: right;
}

.textarea-container {
  :deep(.uni-easyinput__content) {
    flex: 1;
    text-align: left !important;
  }
}

:deep(.uni-easyinput__content-input) {
  text-align: right;
  font-size: 30rpx;
}

:deep(.uni-easyinput__content-textarea) {
  font-size: 30rpx;
}

:deep(.uni-forms-item__error.msg--active) {
  opacity: 0 !important;
}

.textarea-container {
  padding: 0 30rpx 30rpx;
}

// uni-forms 样式调整
:deep(.uni-forms-item) {
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 0;

  &:last-child {
    border-bottom: none;
  }
}

:deep(.uni-forms-item__content) {
  padding: 0;
}

:deep(.uni-forms-item__error) {
  margin-top: 10rpx;
  margin-left: 30rpx;
}

.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 30rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: rgba(82, 150, 255, 1);
  border-radius: 44rpx;
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;

  &:disabled {
    background: rgba(82, 150, 255, 1);
    color: white;
  }

  &:not(:disabled):active {
    transform: scale(0.98);
    transition: transform 0.1s;
  }
}
:deep(.uni-forms-item .is-required) {
  display: none !important;
}

</style>
